---
title: Localization prop (experimental)
description: Use the Clerk localizations package to override and provide
  predefined or custom localizations for your Clerk components, enabling you to
  offer localized content or tailor the wording to match your brand.
lastUpdated: 2025-07-29T18:14:32.000Z
---

> \[!WARNING]
> This feature is currently experimental and may not behave as expected. If you encounter any issues, [contact support](/contact/support){{ target: '_blank' }} with as much detail as possible.

<PERSON> offers the ability to override the strings for all of the elements in each of the Clerk components. This allows you to provide localization for your users or change the wording to suit your brand.

## `@clerk/localizations`

The `@clerk/localizations` package contains predefined localizations for the Clerk components.

### Languages

Clerk currently supports the following languages with English as the default:

| English name | Language tag (BCP 47) | Key |
| - | - | - |

| English (US) | en-US | `enUS` |

| Portuguese (BR) | pt-BR | `ptBR` |

| Spanish | es-ES | `esES` |
|

### Usage

> \[!CAUTION]
> The localizations will only update the text in the [Clerk components](/docs/components/overview) used in your application. The hosted [Clerk Account Portal](/docs/account-portal/overview) will remain in English.

To get started, install the `@clerk/localizations` package.

<CodeBlockTabs options={["npm", "yarn", "pnpm", "bun"]}>
  ```bash {{ filename: 'terminal' }}
  npm install @clerk/localizations
  ```

  ```bash {{ filename: 'terminal' }}
  yarn add @clerk/localizations
  ```

  ```bash {{ filename: 'terminal' }}
  pnpm add @clerk/localizations
  ```

  ```bash {{ filename: 'terminal' }}
  bun add @clerk/localizations
  ```
</CodeBlockTabs>

Once the `@clerk/localizations` package is installed, you can import the localizations you need by removing the "-" from the locale.

<Tabs items={["Next.js", "Astro", "JavaScript", "Remix", "Vue", "Nuxt"]}>
  <Tab>
    In the following example, the fr-FR locale is imported as `frFR`. The imported localization is then passed to the `localization` prop on the <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider></SDKLink>.

    <CodeBlockTabs options={["App Router", "Pages Router"]}>
      ```tsx {{ filename: 'app/layout.tsx', mark: [3, 4, 8] }}
      import { ClerkProvider } from '@clerk/nextjs'
      import './globals.css'
      // fr-FR locale is imported as frFR
      import { frFR } from '@clerk/localizations'

      export default function RootLayout({ children }: { children: React.ReactNode }) {
        return (
          <ClerkProvider localization={frFR}>
            <html lang="en">
              <body>{children}</body>
            </html>
          </ClerkProvider>
        )
      }
      ```

      ```tsx {{ filename: '_app.tsx', mark: [3, 4, 9] }}
      import { ClerkProvider } from '@clerk/nextjs'
      // fr-FR locale is imported as frFR
      import { frFR } from '@clerk/localizations'
      import type { AppProps } from 'next/app'

      function MyApp({ Component, pageProps }: AppProps) {
        return (
          // Add the localization prop to the ClerkProvider
          <ClerkProvider localization={frFR} {...pageProps}>
            <Component {...pageProps} />
          </ClerkProvider>
        )
      }

      export default MyApp
      ```
    </CodeBlockTabs>
  </Tab>

  
</Tabs>

## Adding or updating a localization

Clerk's localizations are customer-sourced and we encourage customers to add or update localizations. To do so, follow these steps:

1. Fork the [https://github.com/clerk/javascript/](https://github.com/clerk/javascript/) repo.
2. Clone it locally to edit it.
3. Review Clerk's [Contributing](https://github.com/clerk/javascript/blob/main/docs/CONTRIBUTING.md) guide.
4. If you are updating an existing localization locate the file in `packages/localizations/src`
5. If you are adding a new language, copy the `en-US.ts` file and name it according to your language. The naming is the abbreviated language-region. For example, for French in Canada, it would be `fr-CA.ts.`
6. Go through the file and edit the entries.
7. If you are adding a new localization, add the language to the `packages/localizations/src/index.ts` file.
8. Commit your changes to git and push them to your fork. Create a [Pull Request](https://github.com/clerk/clerk-docs/pulls) from your fork to Clerk's repo against the `main` branch. We will review and either approve or ask for updates.

## Custom localizations

You can also provide your own localizations for the Clerk components. This is useful if you want to provide limited or quick localization for a language Clerk doesn't currently support, adjust the wording to match your brand, or customize default error messages.

First, you need to find the key for the element that you want to customize. To find the key for your translation, open up Clerk's [English localization file](https://github.com/clerk/javascript/blob/main/packages/localizations/src/en-US.ts). Search the file for the term that you want to customize.

For example, say you want to change the text of the "Continue" button on the `<SignIn />` component to say "LETS GO!". In this case, you'd search for "Continue". The first result that comes up is `formButtonPrimary`, which is the key for the "Continue" button.

Now that you know the key, you can pass it to the `localization` prop and set the value to the text you want to display. In this case, you'd set the value to "LETS GO!", as shown in the following example.

<Tabs items={["Next.js", "Astro", "JavaScript", "Remix", "Vue", "Nuxt"]}>
  <Tab>
    In Next.js, pass the `localization` prop to the <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider></SDKLink> component.

    ```tsx {{ filename: 'app/layout.tsx', mark: [[4, 7], 12] }}
    import { ClerkProvider } from '@clerk/nextjs'
    import './globals.css'

    // Set your customizations in a `localization` object
    const localization = {
      formButtonPrimary: 'LETS GO!',
    }

    export default function RootLayout({ children }: { children: React.ReactNode }) {
      return (
        // Pass the `localization` object to the `localization` prop on the `<ClerkProvider>` component
        <ClerkProvider localization={localization}>
          <html lang="en">
            <body>{children}</body>
          </html>
        </ClerkProvider>
      )
    }
    ```
  </Tab>
</Tabs>

You can also customize multiple entries by passing multiple keys. The following example updates the "to continue to" subtitles on the `<SignUp />` component to say "to access" instead.

<Tabs items={["Next.js", "Astro", "JavaScript", "Remix", "Vue", "Nuxt"]}>
  <Tab>
    In Next.js, pass the `localization` prop to the <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider></SDKLink> component.

    ```tsx {{ filename: 'app/layout.tsx', mark: [[4, 14], 19] }}
    import { ClerkProvider } from '@clerk/nextjs'
    import './globals.css'

    // Set your customizations in a `localization` object
    const localization = {
      signUp: {
        start: {
          subtitle: 'to access {{applicationName}}',
        },
        emailCode: {
          subtitle: 'to access {{applicationName}}',
        },
      },
    }

    export default function RootLayout({ children }: { children: React.ReactNode }) {
      return (
        // Pass the `localization` object to the `localization` prop on the `<ClerkProvider>` component
        <ClerkProvider localization={localization}>
          <html lang="en">
            <body>{children}</body>
          </html>
        </ClerkProvider>
      )
    }
    ```
  </Tab>

</Tabs>

### Example: Customize error messages

You can customize Clerk's default error messages by targeting the `unstable__errors` key. This key lets you define specific error keys for different error types and assign them custom message strings. You can find the full list of error keys in the [English localization file](https://github.com/clerk/javascript/blob/main/packages/localizations/src/en-US.ts). Search for the `unstable__errors` object to find the keys you can customize.

The following example updates the `not_allowed_access` error message. This message appears when a user tries to sign in with an email domain that isn't allowed to access your application.

<Tabs items={["Next.js", "Astro", "JavaScript", "Remix", "Vue", "Nuxt"]}>
  <Tab>
    In Next.js, pass the `localization` prop to the <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider></SDKLink> component.

    <CodeBlockTabs options={["App Router", "Pages Router"]}>
      ```tsx {{ filename: 'app/layout.tsx', mark: [[4, 9], 14] }}
      import { ClerkProvider } from '@clerk/nextjs'
      import './globals.css'

      const localization = {
        unstable__errors: {
          not_allowed_access:
            'Send us an email if you want your corporate email domain allowlisted for access',
        },
      }

      export default function RootLayout({ children }: { children: React.ReactNode }) {
        return (
          // Add the localization prop to the ClerkProvider
          <ClerkProvider localization={localization}>
            <html lang="en">
              <body>{children}</body>
            </html>
          </ClerkProvider>
        )
      }
      ```

      ```tsx {{ filename: '_app.tsx', mark: [[4, 9], 14] }}
      import { ClerkProvider } from '@clerk/nextjs'
      import type { AppProps } from 'next/app'

      const localization = {
        unstable__errors: {
          not_allowed_access:
            'Send us an email if you want your corporate email domain allowlisted for access',
        },
      }

      function MyApp({ Component, pageProps }: AppProps) {
        return (
          // Add the localization prop to the ClerkProvider
          <ClerkProvider localization={localization} {...pageProps}>
            <Component {...pageProps} />
          </ClerkProvider>
        )
      }

      export default MyApp
      ```
    </CodeBlockTabs>
  </Tab>

</Tabs>
