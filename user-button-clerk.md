---
title: "`<UserButton />` component"
description: Clerk's <UserButton /> component is used to render the familiar
  user button UI popularized by Google.
search:
  rank: 1
sdk: astro, chrome-extension, expo, nextjs, nuxt, react, react-router, remix,
  tanstack-react-start, vue, js-frontend
sdkScoped: "true"
canonical: /docs/:sdk:/components/user/user-button
lastUpdated: 2025-07-29T18:14:32.000Z
availableSdks: astro,chrome-extension,expo,nextjs,nuxt,react,react-router,remix,tanstack-react-start,vue,js-frontend
notAvailableSdks: ios,nodejs,expressjs,fastify,go,ruby,js-backend,sdk-development
activeSdk: nextjs
---

![The \<UserButton /> component renders the familiar user button UI popularized by Google.](/docs/images/ui-components/user-button.png){{ style: { maxWidth: '436px' } }}

The `<UserButton />` component renders the familiar user button UI popularized by Google. When selected, it opens a dropdown menu with options to manage account settings and sign out. The "Manage account" option launches the <SDKLink href="/docs/:sdk:/components/user/user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>\<UserProfile /></SDKLink> component, providing access to profile and security settings.

For users that have [multi-session](/docs/authentication/configuration/session-options#multi-session-applications) enabled, the `<UserButton />` also allows users to sign into multiple accounts at once and instantly switch between them without the need for a full page reload. Learn more [here](/docs/authentication/configuration/session-options#multi-session-applications).

## Properties

The `<UserButton />` component accepts the following properties, all of which are **optional**:

<Properties>
  * `afterMultiSessionSingleSignOutUrl` (deprecated)
  * `string`

  **Deprecated. Move `afterMultiSessionSingleSignOutUrl` to <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider /></SDKLink>.** The full URL or path to navigate to after signing out from a currently active account in a multi-session app.

  ***

  * `afterSignOutUrl` (deprecated)
  * `string`

  **Deprecated. Move `afterSignOutUrl` to <SDKLink href="/docs/:sdk:/components/clerk-provider" sdks={["chrome-extension","expo","nextjs","react","react-router","remix","tanstack-react-start"]} code={true}>\<ClerkProvider /></SDKLink>.** The full URL or path to navigate to after a successful sign-out.

  ***

  * `afterSwitchSessionUrl`
  * `string`

  The full URL or path to navigate to after a successful account change in a multi-session app.

  ***

  * `appearance`
  * <code>[Appearance](/docs/customization/overview) | undefined</code>

  Optional object to style your components. Will only affect [Clerk components](/docs/components/overview) and not [Account Portal](/docs/account-portal/overview) pages.

  ***

  * `defaultOpen`
  * `boolean`

  Controls whether the `<UserButton />` should open by default during the first render.

  ***

  * `showName`
  * `boolean`

  Controls if the user name is displayed next to the user image button.

  ***

  * `signInUrl`
  * `string`

  The full URL or path to navigate to when the **Add another account** button is clicked. It's recommended to use [the environment variable](/docs/deployments/clerk-environment-variables#sign-in-and-sign-up-redirects) instead.

  ***

  * `userProfileMode`
  * `'modal' | 'navigation'`

  Controls whether selecting the **Manage your account** button will cause the <SDKLink href="/docs/:sdk:/components/user/user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>\<UserProfile /></SDKLink> component to open as a modal, or if the browser will navigate to the `userProfileUrl` where `<UserProfile />` is mounted as a page. Defaults to: `'modal'`.

  ***

  * `userProfileProps`
  * `object`

  Specify options for the underlying <SDKLink href="/docs/:sdk:/components/user/user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>\<UserProfile /></SDKLink> component. For example: `{additionalOAuthScopes: {google: ['foo', 'bar'], github: ['qux']}}`.

  ***

  * `userProfileUrl`
  * `string`

  The full URL or path leading to the user management interface.

  ***

  * `fallback?`
  * `ReactNode`

  An optional element to be rendered while the component is mounting.
</Properties>

## Usage with frameworks

In the following example, `<UserButton />` is mounted inside a header component, which is a common pattern on many websites and applications. When the user is signed in, they will see their avatar and be able to open the popup menu.

<Tabs items={["Next.js", "React", "Astro", "Expo", "Remix", "Tanstack React Start", "Vue"]}>
  <Tab>
    <CodeBlockTabs options={["App Router", "Pages Router"]}>
      ```tsx {{ filename: 'layout.tsx', mark: [8] }}
      import { ClerkProvider, SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'

      function Header() {
        return (
          <header style={{ display: 'flex', justifyContent: 'space-between', padding: 20 }}>
            <h1>My App</h1>
            <SignedIn>
              <UserButton />
            </SignedIn>
            <SignedOut>
              <SignInButton />
            </SignedOut>
          </header>
        )
      }

      export default function RootLayout({ children }: { children: React.ReactNode }) {
        return (
          <html lang="en">
            <ClerkProvider>
              <Header />
              {children}
            </ClerkProvider>
          </html>
        )
      }
      ```

      ```jsx {{ filename: 'userButtonExample.tsx', mark: [8] }}
      import { ClerkProvider, SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'

      function Header() {
        return (
          <header style={{ display: 'flex', justifyContent: 'space-between', padding: 20 }}>
            <h1>My App</h1>
            <SignedIn>
              <UserButton />
            </SignedIn>
            <SignedOut>
              <SignInButton />
            </SignedOut>
          </header>
        )
      }

      function MyApp({ pageProps }) {
        return (
          <ClerkProvider {...pageProps}>
            <Header />
          </ClerkProvider>
        )
      }

      export default MyApp
      ```
    </CodeBlockTabs>
  </Tab>

  <Tab>
    ```tsx {{ filename: 'src/App.tsx' }}
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/clerk-react'

    export default function App() {
      return (
        <header>
          <SignedIn>
            <UserButton />
          </SignedIn>
          <SignedOut>
            <SignInButton />
          </SignedOut>
        </header>
      )
    }
    ```
  </Tab>

  <Tab>
    ```astro {{ filename: 'pages/index.astro' }}
    ---
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/astro/components'
    ---

    <SignedIn>
      <UserButton />
    </SignedIn>
    <SignedOut>
      <SignInButton />
    </SignedOut>
    ```
  </Tab>

  <Tab>
    > \[!NOTE]
    > This component can be used in Expo Web projects, but won't work in native environments (iOS or Android). For native apps, use the supported native components instead.

    ```jsx {{ filename: '/app/user-button.web.tsx' }}
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/clerk-expo/web'

    export default function Header() {
      return (
        <header>
          <SignedIn>
            <UserButton />
          </SignedIn>
          <SignedOut>
            <SignInButton />
          </SignedOut>
        </header>
      )
    }
    ```
  </Tab>

  <Tab>
    ```tsx {{ filename: 'router/index.tsx' }}
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/remix'
    import { getAuth } from '@clerk/remix/ssr.server'
    import { LoaderFunction, redirect } from '@remix-run/node'

    export const loader: LoaderFunction = async (args) => {
      const { userId } = await getAuth(args)

      if (!userId) {
        return redirect('/sign-in')
      }

      return {
        props: {
          userId,
        },
      }
    }

    export default function Index() {
      return (
        <header>
          <SignedIn>
            <UserButton />
          </SignedIn>
          <SignedOut>
            <SignInButton />
          </SignedOut>
        </header>
      )
    }
    ```
  </Tab>

  <Tab>
    ```tsx {{ filename: 'app/routes/index.tsx' }}
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/tanstack-react-start'
    import { createFileRoute } from '@tanstack/react-router'

    export const Route = createFileRoute('/')({
      component: Home,
    })

    function Home() {
      return (
        <header>
          <SignedIn>
            <UserButton />
          </SignedIn>
          <SignedOut>
            <SignInButton />
          </SignedOut>
        </header>
      )
    }
    ```
  </Tab>

  <Tab>
    ```vue {{ filename: 'header.vue' }}
    <script setup>
    import { SignedIn, UserButton, SignInButton, SignedOut } from '@clerk/vue'
    </script>

    <template>
      <header>
        <SignedIn>
          <UserButton />
        </SignedIn>
        <SignedOut>
          <SignInButton />
        </SignedOut>
      </header>
    </template>
    ```
  </Tab>
</Tabs>

## Customization

To learn about how to customize Clerk components, see the [customization documentation](/docs/customization/overview).

You can also [add custom actions and links to the `<UserButton />` menu](/docs/customization/user-button).
