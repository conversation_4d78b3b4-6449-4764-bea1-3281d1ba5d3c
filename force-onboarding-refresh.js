// Quick debug script to force refresh onboarding state
// Run this in browser console on the onboarding page

console.log('🔍 Checking current onboarding state...');

// Check current user state
if (window.__clerk?.user) {
  const user = window.__clerk.user;
  console.log('Current user metadata:', {
    publicMetadata: user.publicMetadata,
    unsafeMetadata: user.unsafeMetadata,
    onboardingComplete: user.unsafeMetadata?.onboardingComplete
  });
  
  // Check session token
  if (window.__clerk?.session) {
    console.log('Session claims:', window.__clerk.session.sessionClaims);
  }
  
  // Force refresh user data
  console.log('🔄 Forcing user data refresh...');
  user.reload().then(() => {
    console.log('✅ User data refreshed');
    console.log('New metadata:', {
      publicMetadata: user.publicMetadata,
      unsafeMetadata: user.unsafeMetadata,
      onboardingComplete: user.unsafeMetadata?.onboardingComplete
    });
    
    // If onboarding is actually complete, redirect to dashboard
    if (user.unsafeMetadata?.onboardingComplete) {
      console.log('✅ Onboarding is complete, redirecting to dashboard...');
      const locale = window.location.pathname.split('/')[1] || 'en';
      window.location.href = `/${locale}/dashboard`;
    }
  }).catch(console.error);
} else {
  console.log('❌ No Clerk user found');
}
