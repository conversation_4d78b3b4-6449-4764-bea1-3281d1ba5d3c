# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
debug-logs/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.env*

# firebase
firebase-debug.log
firestore-debug.log
# Sentry Config File
.env.sentry-build-plugin
.env

# oauth logs
oauth_callback_data.json
oauth_logs/oauth_callback_data.json

/docs/openai-agents-js
docs/openai-agents-js