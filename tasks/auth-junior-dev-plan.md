# Supabase to Clerk Authentication Migration Plan - Junior Developer Guide

## Executive Summary

This document provides a step-by-step implementation plan to migrate the existing Next.js application from Supabase Auth to Clerk authentication while maintaining all current functionality. The migration will preserve:

- Google OAuth functionality
- User profiles and metadata
- Protected routes and middleware
- Current UI/UX patterns
- Database operations (using Supabase for data, Clerk for auth)

**Key Environment Variables** (already configured):
```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_cmlnaHQtZ3VsbC0zNi5jbGVyay5hY2NvdW50cy5kZXYk
CLERK_SECRET_KEY=sk_test_QxlSenARA3WVuE4vpAdyNB33s5NqYmGM8MhvrtWqGF
```

## Current State Analysis

### Existing Supabase Auth Implementation
- **Auth Service**: `src/features/auth/services/auth.service.ts`
- **Auth Actions**: `src/features/auth/actions/auth.actions.ts`
- **Auth Hooks**: `src/features/auth/hooks/use-auth.ts`
- **Auth Provider**: `src/providers/auth-session-provider.tsx`
- **Middleware**: Uses `src/features/auth/utils/middleware.utils.ts`
- **Protected Routes**: Dashboard routes in `src/app/(dashboard)/`
- **User Profiles**: Complete user profile system in `src/features/user-auth-data/`

### Dependencies Analysis
- **Keep**: `@supabase/supabase-js` (for database operations)
- **Remove**: `@supabase/ssr` (auth only)
- **Add**: `@clerk/nextjs` (already installed ✅)

## Implementation Plan

### Phase 1: Clerk Setup and Configuration (1-2 hours)

#### Task 1.1: Update Middleware
**File**: `middleware.ts`
**Action**: Replace current middleware with Clerk middleware integration

**Current code location**: Line 1-171
**Replace entire file with**:
```typescript
/**
 * Next.js Middleware for i18n routing, SEO optimization, and Clerk authentication
 * Handles locale detection, redirects, SEO headers, and route protection
 */

import { NextRequest, NextResponse } from 'next/server';
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';

const locales = ['en', 'pt', 'es'] as const;

// Paths that should not be internationalized
const excludedPaths = [
  '/api',
  '/images',
  '/icons',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/_next',
  '/monitoring', // Sentry tunnel
  '/sso-callback', // Clerk SSO callback
];

// Protected routes that require authentication
const isProtectedRoute = createRouteMatcher([
  '/dashboard(.*)',
  '/profile(.*)',
  '/api/protected(.*)',
  '/(en|pt|es)/dashboard(.*)',
  '/(en|pt|es)/profile(.*)',
]);

// Public routes that should redirect authenticated users
const isPublicRoute = createRouteMatcher([
  '/login',
  '/register',
  '/forgot-password',
  '/(en|pt|es)/login',
  '/(en|pt|es)/register',
  '/(en|pt|es)/forgot-password',
]);

export default clerkMiddleware(async (auth, request) => {
  const { pathname } = request.nextUrl;

  // Debug logging for all requests
  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Processing request:', { pathname });
  }

  // Skip excluded paths
  if (excludedPaths.some(path => pathname.startsWith(path))) {
    if (process.env.NODE_ENV === 'development') {
      console.log('[middleware] Skipping excluded path:', pathname);
    }
    return NextResponse.next();
  }

  // Handle authentication for protected routes
  if (isProtectedRoute(request)) {
    await auth.protect();
  }

  // Redirect authenticated users away from public auth pages
  if (isPublicRoute(request)) {
    const { userId } = await auth();
    if (userId) {
      // Extract locale from current path if present
      const pathSegments = pathname.split('/');
      const potentialLocale = pathSegments[1];
      const locale = locales.includes(potentialLocale as any) ? potentialLocale : 'en';
      return NextResponse.redirect(new URL(`/${locale}/dashboard`, request.url));
    }
  }

  // Handle i18n routing
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Locale check:', { 
      pathname, 
      pathnameHasLocale,
    });
  }

  if (pathnameHasLocale) {
    const locale = pathname.split('/')[1] as typeof locales[number];
    const response = NextResponse.next();
    response.headers.set('x-locale', locale);
    response.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');
    return response;
  }

  // For root URLs without locale, handle redirect with authentication
  return await handleRootUrlRedirect(request, pathname);
});

/**
 * Parse Accept-Language header to get preferred locale
 */
function parseAcceptLanguage(acceptLanguage: string): SupportedLocale {
  const supportedLocales: SupportedLocale[] = ['en', 'pt', 'es'];

  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        const code = langCode?.substring(0, 2)?.toLowerCase() || '';
        const priority = weight ? parseFloat(weight.split('=')[1] || '1.0') : 1.0;
        return { code, priority };
      })
      .sort((a, b) => b.priority - a.priority);

    for (const lang of languages) {
      if (supportedLocales.includes(lang.code as SupportedLocale)) {
        return lang.code as SupportedLocale;
      }
    }
  } catch (error) {
    // If parsing fails, fall back to default
  }

  return 'en';
}

/**
 * Handle root URL redirects with Edge Runtime compatible locale detection
 */
async function handleRootUrlRedirect(request: NextRequest, pathname: string): Promise<NextResponse> {
  let preferredLocale: SupportedLocale = 'en';

  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    preferredLocale = cookieLocale as SupportedLocale;
    if (process.env.NODE_ENV === 'development') {
      console.log('[middleware] Using cookie locale preference:', preferredLocale);
    }
  } else {
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      preferredLocale = parseAcceptLanguage(acceptLanguage);
      if (process.env.NODE_ENV === 'development') {
        console.log('[middleware] Using browser locale detection:', {
          'accept-language': acceptLanguage,
          'detected-locale': preferredLocale
        });
      }
    }
  }

  const response = NextResponse.redirect(new URL(`/${preferredLocale}${pathname === '/' ? '' : pathname}`, request.url));
  response.headers.set('x-detected-locale', preferredLocale);
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Redirecting to:', `/${preferredLocale}${pathname === '/' ? '' : pathname}`);
  }

  return response;
}

export const config = {
  matcher: [
    '/((?!.*\\..*|_next).*)',
    '/',
    '/(api|trpc)(.*)',
  ],
};
```

#### Task 1.2: Create SSO Callback Page
**File**: `src/app/sso-callback/page.tsx` (new file)
**Action**: Create callback page for OAuth redirects

```typescript
import { AuthenticateWithRedirectCallback } from '@clerk/nextjs';

export default function SSOCallback() {
  return <AuthenticateWithRedirectCallback />;
}
```

### Phase 2: Create Clerk Service Layer (2-3 hours)

#### Task 2.1: Create Clerk Auth Actions
**File**: `src/features/auth/actions/clerk-auth.actions.ts` (new file)
**Action**: Create server actions for Clerk authentication

```typescript
"use server";

import { auth, currentUser } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { getServerLogger } from '@/lib/logger';

const logger = getServerLogger('ClerkAuthActions');

interface AuthActionState {
  success: boolean;
  message: string | null;
  errorFields?: Record<string, string> | null;
  redirectTo?: string;
  user?: any;
}

export async function getCurrentUser() {
  try {
    const user = await currentUser();
    logger.info('Retrieved current user', { 
      userId: user?.id ? `${user.id.substring(0, 6)}...` : 'none' 
    });
    return user;
  } catch (error) {
    logger.error('Failed to get current user', { error });
    return null;
  }
}

export async function getAuthSession() {
  try {
    const { userId, sessionId } = await auth();
    logger.info('Retrieved auth session', { 
      userId: userId ? `${userId.substring(0, 6)}...` : 'none',
      hasSession: !!sessionId 
    });
    return { userId, sessionId };
  } catch (error) {
    logger.error('Failed to get auth session', { error });
    return { userId: null, sessionId: null };
  }
}

export async function signOutAction(): Promise<void> {
  logger.info('User signing out');
  redirect('/sign-in');
}
```

#### Task 2.2: Create Clerk Auth Service
**File**: `src/features/auth/services/clerk-auth.service.ts` (new file)
**Action**: Create service layer for Clerk operations

```typescript
"use server";

import { auth, currentUser } from '@clerk/nextjs/server';
import { getServerLogger } from '@/lib/logger';
import { cache } from 'react';

const logger = getServerLogger('ClerkAuthService');

interface UserProfile {
  id: string;
  email: string | undefined;
  firstName: string | null;
  lastName: string | null;
  imageUrl: string;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthState {
  isAuthenticated: boolean;
  userId: string | null;
  sessionId: string | null;
  error: string | null;
}

// Cached version to prevent redundant calls within the same request
const getCachedUserProfile = cache(async (): Promise<{ user: UserProfile | null; error: string | null }> => {
  try {
    const user = await currentUser();
    
    if (!user) {
      logger.info('No authenticated user found');
      return { user: null, error: null };
    }

    const userProfile: UserProfile = {
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress,
      firstName: user.firstName,
      lastName: user.lastName,
      imageUrl: user.imageUrl,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    logger.info('Retrieved user profile', { 
      userId: `${user.id.substring(0, 6)}...`,
      email: user.emailAddresses[0]?.emailAddress ? `${user.emailAddresses[0].emailAddress.substring(0, 3)}...` : 'none'
    });
    
    return { user: userProfile, error: null };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to get user profile', { error: errorMessage });
    return { user: null, error: errorMessage };
  }
});

export async function getCurrentUserProfile() {
  return getCachedUserProfile();
}

export async function getAuthenticationStatus(): Promise<AuthState> {
  try {
    const { userId, sessionId } = await auth();
    
    return {
      isAuthenticated: !!userId,
      userId,
      sessionId,
      error: null
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to get auth status', { error: errorMessage });
    return {
      isAuthenticated: false,
      userId: null,
      sessionId: null,
      error: errorMessage
    };
  }
}
```

#### Task 2.3: Create Clerk Auth State Service
**File**: `src/features/auth/services/clerk-auth-state.service.ts` (new file)
**Action**: Replace existing auth-state.service.ts functionality

```typescript
'use server';

import { auth, currentUser } from '@clerk/nextjs/server';
import { getServerLogger } from '@/lib/logger';
import { cache } from 'react';

const logger = getServerLogger('ClerkAuthStateService');

export type ClerkAuthStateResult = {
  user: any | null;
  error?: Error;
};

/**
 * Cached version of Clerk auth state retrieval to prevent redundant calls
 * Uses React's cache() to deduplicate calls within the same request
 */
const getCachedClerkAuthState = cache(async (): Promise<ClerkAuthStateResult> => {
  try {
    const user = await currentUser();
    
    if (user?.id) {
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      logger.info('Clerk auth state retrieved successfully', {
        userId: maskedUserId,
        operation: 'getServerClerkAuthState'
      });
    } else {
      logger.info('Clerk auth state retrieved - no user session', {
        operation: 'getServerClerkAuthState'
      });
    }
    
    return { user };
  } catch (err) {
    // Handle static generation errors gracefully
    if (err instanceof Error && (err.message.includes('Dynamic server usage') || err.message.includes('cookies'))) {
      logger.info('Static generation detected in Clerk auth state service', {
        operation: 'getServerClerkAuthState'
      });
      return { user: null, error: err };
    }

    logger.error('Critical error in Clerk auth state service', {
      error: err instanceof Error ? err.message : String(err),
      operation: 'getServerClerkAuthState'
    });
    return { user: null, error: err instanceof Error ? err : new Error(String(err)) };
  }
});

/**
 * Single source of truth for server-side Clerk authentication state
 * Replacement for getServerAuthState() from auth-state.service.ts
 */
export async function getServerClerkAuthState(): Promise<ClerkAuthStateResult> {
  return getCachedClerkAuthState();
}
```

### Phase 3: Update Root Layout and Providers (1 hour)

#### Task 3.1: Update Root Layout
**File**: `src/app/layout.tsx`
**Action**: Wrap application with ClerkProvider and update auth state service

**Find and replace**:
```typescript
// FIND THIS (around line 9):
import { createClient } from '@/lib/supabase/server';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';

// REPLACE WITH:
import { getServerClerkAuthState } from '@/features/auth/services/clerk-auth-state.service';
import { ClerkProvider } from '@clerk/nextjs';
```

**Find and replace**:
```typescript
// FIND THIS (around line 40):
    const { user: authUser, error } = await getServerAuthState();

// REPLACE WITH:
    const { user: authUser, error } = await getServerClerkAuthState();
```

**Find and replace**:
```typescript
// FIND THIS (around line 68):
  return (
    <html lang="en" suppressHydrationWarning>

// REPLACE WITH:
  return (
    <ClerkProvider>
      <html lang="en" suppressHydrationWarning>
```

**Find and replace**:
```typescript
// FIND THIS (at the end):
    </html>
  );

// REPLACE WITH:
      </html>
    </ClerkProvider>
  );
```

#### Task 3.2: Create Clerk Auth Session Provider
**File**: `src/providers/clerk-auth-session-provider.tsx` (new file)
**Action**: Create new provider for Clerk authentication state

```typescript
'use client';

import { useUser } from '@clerk/nextjs';
import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';
import * as Sentry from '@sentry/nextjs';

interface ClerkAuthSessionContextType {
  user: any | null;
  isLoading: boolean;
  error: Error | null;
}

const ClerkAuthSessionContext = createContext<ClerkAuthSessionContextType | undefined>(
  undefined,
);

export const ClerkAuthSessionProvider = ({
  children,
  preloadedUser = null
}: {
  children: ReactNode;
  preloadedUser?: any | null;
}) => {
  const { user: clerkUser, isLoaded, isSignedIn } = useUser();
  const [user, setUser] = useState<any | null>(preloadedUser);
  const [isLoading, setIsLoading] = useState<boolean>(!preloadedUser && !isLoaded);
  const [error, setError] = useState<Error | null>(null);
  
  const shouldLog = process.env.NODE_ENV === 'development' && 
                    process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true';
  
  useEffect(() => {
    if (isLoaded) {
      setIsLoading(false);
      if (isSignedIn && clerkUser) {
        setUser(clerkUser);
        if (shouldLog) {
          console.log('[ClerkAuthSessionProvider] User signed in:', clerkUser.id);
        }
      } else {
        setUser(null);
        if (shouldLog) {
          console.log('[ClerkAuthSessionProvider] User signed out or not authenticated');
        }
      }
    }
  }, [isLoaded, isSignedIn, clerkUser, shouldLog]);
  
  return (
    <ClerkAuthSessionContext.Provider value={{ user, isLoading, error }}>
      {children}
    </ClerkAuthSessionContext.Provider>
  );
};

export const useClerkAuthSession = (): ClerkAuthSessionContextType => {
  const context = useContext(ClerkAuthSessionContext);
  if (context === undefined) {
    throw new Error('useClerkAuthSession must be used within a ClerkAuthSessionProvider');
  }
  return context;
};
```

### Phase 4: Update Authentication Components (3-4 hours)

#### Task 4.1: Create Clerk Auth Hook
**File**: `src/features/auth/hooks/use-clerk-auth.ts` (new file)
**Action**: Create new auth hook for Clerk

```typescript
'use client';

import { useClerkAuthSession } from '@/providers/clerk-auth-session-provider';
import { useUserProfileQuery } from '@/features/user-auth-data/hooks/use-user-profile-query';
import { useMemo, useEffect, useRef, useCallback } from 'react';
import * as Sentry from '@sentry/nextjs';
import { type UserProfile } from '@/features/user-auth-data/schemas';

interface ClerkAuthState {
  user: any | null;
  profile: UserProfile | null | undefined;
  authUser: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  profileError: Error | null;
  retry: () => void;
  clearErrors: () => void;
}

/**
 * Primary hook for Clerk authentication state
 * Replacement for useAuth() hook
 */
export function useClerkAuth(): ClerkAuthState {
  const debugAuth = process.env.NODE_ENV === 'development' && 
                    process.env['NEXT_PUBLIC_DEBUG_AUTH'] === 'true';
  
  const retryCountRef = useRef(0);
  const maxRetries = 3;
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  
  const { 
    user: sessionUser, 
    isLoading: isSessionLoading, 
    error: sessionError 
  } = useClerkAuthSession();
  
  const { 
    data: profileData, 
    isLoading: isProfileLoading,
    error: profileError,
    refetch: refetchProfile
  } = useUserProfileQuery(
    sessionUser?.id,
    { 
      enabled: !!sessionUser?.id,
      refetchOnReconnect: true,
    }
  );
  
  const retry = useCallback(() => {
    if (retryCountRef.current < maxRetries) {
      retryCountRef.current++;
      const delay = Math.min(1000 * Math.pow(2, retryCountRef.current - 1), 10000);
      
      retryTimeoutRef.current = setTimeout(() => {
        if (sessionUser?.id && profileError) {
          refetchProfile();
        }
      }, delay);
      
      if (debugAuth) {
        console.log(`[useClerkAuth] Retry scheduled in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);
      }
    }
  }, [sessionUser?.id, profileError, refetchProfile, debugAuth]);
  
  const clearErrors = useCallback(() => {
    retryCountRef.current = 0;
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
  }, []);
  
  useEffect(() => {
    if (profileData && !profileError) {
      retryCountRef.current = 0;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    }
  }, [profileData, profileError]);
  
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);
  
  useEffect(() => {
    if (sessionError) {
      if (debugAuth) {
        console.error('[useClerkAuth] Session error:', sessionError.message);
      }
      Sentry.captureException(sessionError, {
        tags: { component: 'useClerkAuth', type: 'sessionError' },
        extra: { 
          userId: sessionUser?.id ? `${sessionUser.id.substring(0, 6)}...` : 'none',
          operation: 'useClerkAuth'
        }
      });
    }
  }, [sessionError, sessionUser?.id, debugAuth]);
  
  useEffect(() => {
    if (profileError && sessionUser) {
      if (debugAuth) {
        console.error('[useClerkAuth] Profile error:', profileError);
      }
      Sentry.captureException(profileError, {
        tags: { component: 'useClerkAuth', type: 'profileError' },
        extra: { 
          userId: `${sessionUser.id.substring(0, 6)}...`,
          operation: 'useClerkAuth'
        }
      });
    }
  }, [profileError, sessionUser?.id, debugAuth]);
  
  return useMemo(() => {
    const isAuthenticated = !!sessionUser && !isSessionLoading;
    const isLoading = isSessionLoading || (isAuthenticated && isProfileLoading);
    
    const authUser = sessionUser && profileData ? {
      ...sessionUser,
      ...profileData,
    } : null;
    
    const authState: ClerkAuthState = {
      user: sessionUser,
      profile: profileData,
      authUser,
      isAuthenticated,
      isLoading,
      error: sessionError,
      profileError,
      retry,
      clearErrors
    };
    
    return authState;
  }, [
    sessionUser?.id,
    profileData?.id,
    isSessionLoading, 
    isProfileLoading, 
    sessionError?.message,
    profileError?.message,
    retry,
    clearErrors
  ]);
}
```

#### Task 4.2: Update Login Form Component
**File**: `src/features/auth/components/login-form.tsx`
**Action**: Replace Supabase actions with Clerk components

**Find and replace the entire component**:
```typescript
"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Input, Button, Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui";
import { useToast } from "@/hooks";
import { Loader2, Eye, EyeOff } from "lucide-react";
import * as Sentry from '@sentry/nextjs';
import { useSearchParams } from "next/navigation";
import { useSignIn } from '@clerk/nextjs';

function SubmitButton({ isPending }: { isPending: boolean }) {
  return (
    <Button type="submit" className="w-full" disabled={isPending}>
      {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      Login
    </Button>
  );
}

const USER_FACING_ERROR_SUBSTRINGS = [
  "invalid email address",
  "password is required",
  "invalid login credentials",
  "please check your credentials",
  "oauth_init_failed",
  "oauth_no_url"
];

export default function LoginForm(): JSX.Element {
  const { toast } = useToast();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signIn, isLoaded, setActive } = useSignIn();
  
  const [isPending, setIsPending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (!searchParams) return;
    
    const oauthError = searchParams.get('error');
    const oauthMessage = searchParams.get('message');
    if (oauthError) {
      let title = "OAuth Sign-In Failed";
      if (oauthError.includes('google')) title = "Google Sign-In Failed";
      
      toast({
        title: title,
        description: oauthMessage || "An error occurred during OAuth Sign-In.",
        variant: "destructive",
      });
      Sentry.captureMessage(`OAuth Error on Login Page: ${oauthError}`, {
        level: 'warning',
        extra: { errorMessage: oauthMessage },
      });
    }
  }, [searchParams, toast]);

  const handlePasswordSubmit = async (formData: FormData) => {
    if (!isLoaded) return;
    
    setIsPending(true);
    setError(null);

    try {
      const email = formData.get("email") as string;
      const password = formData.get("password") as string;

      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });
        toast({
          title: "Success!",
          description: "Successfully signed in",
        });
        router.push('/dashboard');
      } else {
        console.error("Sign-in incomplete:", result);
        setError("Sign-in failed. Please check your credentials.");
      }
    } catch (err: any) {
      const errorMessage = err.errors?.[0]?.message || err.message || 'An unexpected error occurred';
      setError(errorMessage);
      toast({
        title: "Login Failed",
        description: errorMessage,
        variant: "destructive",
      });

      const isUserFacingError = USER_FACING_ERROR_SUBSTRINGS.some(sub =>
        errorMessage.toLowerCase().includes(sub)
      );
      if (!isUserFacingError) {
        Sentry.captureMessage('Login failed with unexpected error', {
          level: 'error',
          extra: {
            action: 'clerk-signIn',
            error: errorMessage,
            emailUsed: (document.getElementById('email') as HTMLInputElement)?.value?.substring(0,3) + '...'
          }
        });
      }
    } finally {
      setIsPending(false);
    }
  };

  const handleOAuthSignIn = async (provider: 'oauth_google' | 'oauth_microsoft') => {
    if (!isLoaded) return;

    try {
      await signIn.authenticateWithRedirect({
        strategy: provider,
        redirectUrl: '/sso-callback',
        redirectUrlComplete: '/dashboard',
      });
    } catch (error) {
      console.error(`${provider} redirect error:`, error);
      toast({
        title: "OAuth Error",
        description: `Failed to redirect to ${provider === 'oauth_google' ? 'Google' : 'Microsoft'}`,
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Welcome back</CardTitle>
          <CardDescription>
            Sign in to your account to continue
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form action={handlePasswordSubmit} className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                placeholder="Enter your email"
                disabled={isPending}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="password" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Password
              </label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  placeholder="Enter your password"
                  disabled={isPending}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isPending}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" aria-hidden="true" />
                  ) : (
                    <Eye className="h-4 w-4" aria-hidden="true" />
                  )}
                  <span className="sr-only">
                    {showPassword ? "Hide password" : "Show password"}
                  </span>
                </Button>
              </div>
            </div>
            {error && (
              <div className="text-sm text-red-600 mt-2">
                {error}
              </div>
            )}
            <SubmitButton isPending={isPending} />
          </form>
          
          <div className="mt-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Or continue with
                </span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <Button
                variant="outline"
                onClick={() => handleOAuthSignIn('oauth_google')}
                disabled={isPending}
              >
                <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                  <path
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    fill="#4285F4"
                  />
                  <path
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    fill="#34A853"
                  />
                  <path
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    fill="#FBBC05"
                  />
                  <path
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    fill="#EA4335"
                  />
                </svg>
                Google
              </Button>
              <Button
                variant="outline"
                onClick={() => handleOAuthSignIn('oauth_microsoft')}
                disabled={isPending}
              >
                <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                  <path fill="#f25022" d="M1 1h10v10H1z"/>
                  <path fill="#00a4ef" d="M13 1h10v10H13z"/>
                  <path fill="#7fba00" d="M1 13h10v10H1z"/>
                  <path fill="#ffb900" d="M13 13h10v10H13z"/>
                </svg>
                Microsoft
              </Button>
            </div>
          </div>
          
          <div className="mt-4 text-center text-sm">
            Don't have an account?{" "}
            <Link href="/register" className="underline">
              Sign up
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
```

### Phase 5: Update Dashboard Components (2-3 hours)

#### Task 5.1: Update Dashboard Layout
**File**: `src/app/(dashboard)/layout.tsx`
**Action**: Update to use Clerk auth state service

**Find and replace**:
```typescript
// FIND THIS (around line 2):
import { getServerAuthWithProfilePrefetch } from '@/lib/auth/server-auth.utils';

// REPLACE WITH:
import { getServerClerkAuthWithProfilePrefetch } from '@/lib/auth/server-clerk-auth.utils';
```

**Find and replace**:
```typescript
// FIND THIS (around line 35):
  const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({

// REPLACE WITH:
  const { user, dehydratedState, error } = await getServerClerkAuthWithProfilePrefetch({
```

#### Task 5.2: Create Server Clerk Auth Utils
**File**: `src/lib/auth/server-clerk-auth.utils.ts` (new file)
**Action**: Create utility functions for server-side Clerk auth

```typescript
'use server';

import { dehydrate, QueryClient } from '@tanstack/react-query';
import { cache } from 'react';
import { getServerClerkAuthState } from '@/features/auth/services/clerk-auth-state.service';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import { getServerLogger } from '@/lib/logger';

const logger = getServerLogger('ServerClerkAuthUtils');

interface ServerClerkAuthOptions {
  prefetchProfile?: boolean;
  profileTimeout?: number;
  profileStaleTime?: number;
  requestId?: string;
  debugMode?: boolean;
}

interface ServerClerkAuthResult {
  user: any | null;
  dehydratedState: any;
  error: Error | null;
}

/**
 * Cached server-side auth state with profile prefetching for Clerk
 * Replacement for getServerAuthWithProfilePrefetch
 */
export const getServerClerkAuthWithProfilePrefetch = cache(async (
  options: ServerClerkAuthOptions = {}
): Promise<ServerClerkAuthResult> => {
  const {
    prefetchProfile = false,
    profileTimeout = 1000,
    profileStaleTime = 5 * 60 * 1000,
    requestId = 'unknown',
    debugMode = false
  } = options;

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: profileStaleTime,
        retry: false, // Don't retry on server-side
      },
    },
  });

  let user = null;
  let authError = null;

  try {
    // Get auth state from Clerk
    const { user: authUser, error } = await getServerClerkAuthState();
    user = authUser;
    authError = error;

    if (debugMode) {
      console.log(`[ServerClerkAuthUtils] Auth state retrieved (${requestId}):`, { 
        hasUser: !!user, 
        userId: user?.id ? `${user.id.substring(0, 6)}...` : 'none',
        hasError: !!error
      });
    }

    // Prefetch profile if user exists and prefetching is enabled
    if (user?.id && prefetchProfile) {
      const startTime = Date.now();
      
      try {
        const profilePromise = getCurrentUserProfile(user.id);
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Profile fetch timeout')), profileTimeout)
        );
        
        const profileData = await Promise.race([profilePromise, timeoutPromise]);
        const fetchTime = Date.now() - startTime;
        
        if (profileData) {
          await queryClient.prefetchQuery({
            queryKey: ['user-profile', user.id],
            queryFn: () => profileData,
            staleTime: profileStaleTime,
          });
          
          if (debugMode) {
            console.log(`[ServerClerkAuthUtils] Profile prefetched successfully (${requestId}):`, {
              fetchTime: `${fetchTime}ms`,
              userId: `${user.id.substring(0, 6)}...`
            });
          }
          
          logger.info('Profile prefetched successfully', {
            userId: `${user.id.substring(0, 6)}...`,
            fetchTime,
            requestId
          });
        }
      } catch (profileError) {
        const fetchTime = Date.now() - startTime;
        
        if (debugMode) {
          console.warn(`[ServerClerkAuthUtils] Profile prefetch failed (${requestId}):`, {
            error: profileError instanceof Error ? profileError.message : String(profileError),
            fetchTime: `${fetchTime}ms`,
            userId: `${user.id.substring(0, 6)}...`
          });
        }
        
        logger.warn('Profile prefetch failed - continuing without profile', {
          userId: `${user.id.substring(0, 6)}...`,
          error: profileError instanceof Error ? profileError.message : String(profileError),
          fetchTime,
          requestId
        });
      }
    } else if (!prefetchProfile && debugMode) {
      console.log(`[ServerClerkAuthUtils] Profile prefetch disabled (${requestId})`);
    } else if (!user?.id && debugMode) {
      console.log(`[ServerClerkAuthUtils] No authenticated user found. Skipping profile prefetch (${requestId})`);
    }
  } catch (err) {
    authError = err instanceof Error ? err : new Error(String(err));
    
    if (debugMode) {
      console.error(`[ServerClerkAuthUtils] Critical error (${requestId}):`, {
        error: authError.message,
        stack: authError.stack
      });
    }
    
    logger.error('Critical error in server Clerk auth utils', {
      error: authError.message,
      stack: authError.stack,
      requestId
    });
  } finally {
    if (debugMode) {
      console.log(`[ServerClerkAuthUtils] Completed (${requestId}):`, {
        hasUser: !!user,
        hasError: !!authError,
        prefetchProfile
      });
    }
    
    logger.info('ServerClerkAuthUtils: No authenticated user found. Skipping profile prefetch.', {
      requestId
    });
  }

  return {
    user,
    dehydratedState: dehydrate(queryClient),
    error: authError
  };
});
```

#### Task 5.3: Update Dashboard User Menu
**File**: `src/features/dashboard/components/dashboard-user-menu.tsx`
**Action**: Update to use Clerk auth hook

**Find and replace**:
```typescript
// FIND THIS (around line 54):
import { useAuth } from "@/features/auth/hooks";

// REPLACE WITH:
import { useClerkAuth } from "@/features/auth/hooks/use-clerk-auth";
```

**Find and replace**:
```typescript
// FIND THIS (around line 70):
  const { user, profile, isLoading } = useAuth();

// REPLACE WITH:
  const { user, profile, isLoading } = useClerkAuth();
```

#### Task 5.4: Update Sign Out Action
**File**: `src/features/auth/actions/clerk-sign-out.action.ts` (new file)
**Action**: Create Clerk sign-out action

```typescript
'use server';

import { redirect } from 'next/navigation';
import { getServerLogger } from '@/lib/logger';

const logger = getServerLogger('ClerkSignOutAction');

/**
 * Server action for Clerk user sign-out
 * Handles session termination and redirection
 */
export async function clerkSignOutAction() {
  try {
    logger.info('User signing out via Clerk');
    // Clerk handles sign-out via client-side components
    // This action just handles the redirect
    redirect('/login');
  } catch (error) {
    logger.error('Error during Clerk sign-out action', {
      error: error instanceof Error ? error.message : String(error)
    });
    // Still redirect even if there's an error
    redirect('/login');
  }
}
```

### Phase 6: Update User Profile Integration (2 hours)

#### Task 6.1: Update User Profile Service
**File**: `src/features/user-auth-data/services/profile.service.ts`
**Action**: Update to work with Clerk user IDs

**Find this function (around line 20-50) and update**:
```typescript
// FIND THIS FUNCTION:
export async function getCurrentUserProfile(userId: string) {

// UPDATE THE FUNCTION TO:
export async function getCurrentUserProfile(clerkUserId: string) {
  const supabase = await createClient();
  
  try {
    // Query using clerk_user_id instead of id
    const { data: profileData, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('clerk_user_id', clerkUserId) // Changed from 'id' to 'clerk_user_id'
      .maybeSingle();
      
    if (profileError) {
      const maskedUserId = `${clerkUserId.substring(0, 6)}...`;
      
      logger.warn('Error fetching user profile', {
        clerkUserId: maskedUserId, // Updated variable name
        error: profileError.message,
        code: profileError.code,
        operation: 'getCurrentUserProfile'
      });
      return null;
    }
    
    if (!profileData) {
      const maskedUserId = `${clerkUserId.substring(0, 6)}...`;
      logger.info('No profile data found for user', {
        clerkUserId: maskedUserId, // Updated variable name
        operation: 'getCurrentUserProfile'
      });
      return null;
    }
    
    // Return profile data with Clerk user ID
    return {
      ...profileData,
      id: clerkUserId, // Use Clerk user ID as the profile ID
    };
  } catch (error) {
    const maskedUserId = `${clerkUserId.substring(0, 6)}...`;
    logger.error('Unexpected error fetching user profile', {
      clerkUserId: maskedUserId, // Updated variable name
      error: error instanceof Error ? error.message : String(error),
      operation: 'getCurrentUserProfile'
    });
    return null;
  }
}
```

#### Task 6.2: Update Database Schema (SQL)
**Action**: Add clerk_user_id column to user_profiles table

**Create and run this SQL migration**:
```sql
-- Add clerk_user_id column to user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN clerk_user_id TEXT;

-- Create index for clerk_user_id for better query performance
CREATE INDEX idx_user_profiles_clerk_user_id ON user_profiles(clerk_user_id);

-- Update existing records to use Supabase user ID as temporary clerk_user_id
-- Note: This will need to be updated when users sign in with Clerk
UPDATE user_profiles 
SET clerk_user_id = id 
WHERE clerk_user_id IS NULL;
```

### Phase 7: Update Import Statements and Clean Up (1 hour)

#### Task 7.1: Update Auth Hook Index
**File**: `src/features/auth/hooks/index.ts`
**Action**: Export new Clerk auth hook

**Add to the file**:
```typescript
export { useClerkAuth } from './use-clerk-auth';
export { useClerkAuth as useAuth } from './use-clerk-auth'; // Alias for backward compatibility
```

#### Task 7.2: Update Provider Index
**File**: `src/providers/index.ts` (create if doesn't exist)
**Action**: Export new provider

```typescript
export { ClerkAuthSessionProvider } from './clerk-auth-session-provider';
```

#### Task 7.3: Update Auth Actions Index
**File**: `src/features/auth/actions/index.ts`
**Action**: Export new Clerk actions

**Add to the file**:
```typescript
export * from './clerk-auth.actions';
export { clerkSignOutAction } from './clerk-sign-out.action';
```

### Phase 8: Testing and Validation (2 hours)

#### Task 8.1: Create Migration Test Script
**File**: `test-clerk-migration.ts` (in root directory)
**Action**: Create comprehensive test script

```typescript
#!/usr/bin/env tsx
/**
 * Clerk Migration Test Script
 * Tests all Clerk configuration to ensure migration is successful
 * 
 * Usage: npx tsx test-clerk-migration.ts
 */

import { config } from 'dotenv';

// Load environment variables from .env.local
config({ path: '.env.local' });

interface TestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  details?: string;
}

class ClerkMigrationTester {
  private results: TestResult[] = [];
  private clerkPublishableKey: string;
  private clerkSecretKey: string;

  constructor() {
    this.clerkPublishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';
    this.clerkSecretKey = process.env.CLERK_SECRET_KEY || '';
  }

  private addResult(name: string, status: 'PASS' | 'FAIL' | 'WARN', message: string, details?: string) {
    this.results.push({ name, status, message, details });
  }

  private async testEnvironmentVariables() {
    console.log('🔍 Testing Clerk Environment Variables...\n');
    
    // Test publishable key
    if (this.clerkPublishableKey) {
      if (this.clerkPublishableKey.startsWith('pk_test_')) {
        this.addResult('Publishable Key', 'PASS', 'Valid test publishable key found');
      } else if (this.clerkPublishableKey.startsWith('pk_live_')) {
        this.addResult('Publishable Key', 'WARN', 'Production publishable key detected');
      } else {
        this.addResult('Publishable Key', 'FAIL', 'Invalid publishable key format');
      }
    } else {
      this.addResult('Publishable Key', 'FAIL', 'NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY not found');
    }

    // Test secret key
    if (this.clerkSecretKey) {
      if (this.clerkSecretKey.startsWith('sk_test_')) {
        this.addResult('Secret Key', 'PASS', 'Valid test secret key found');
      } else if (this.clerkSecretKey.startsWith('sk_live_')) {
        this.addResult('Secret Key', 'WARN', 'Production secret key detected');
      } else {
        this.addResult('Secret Key', 'FAIL', 'Invalid secret key format');
      }
    } else {
      this.addResult('Secret Key', 'FAIL', 'CLERK_SECRET_KEY not found');
    }

    // Test Supabase keys (should still exist)
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    if (supabaseUrl) {
      this.addResult('Supabase URL', 'PASS', 'Supabase URL found (for database operations)');
    } else {
      this.addResult('Supabase URL', 'FAIL', 'NEXT_PUBLIC_SUPABASE_URL not found');
    }

    if (supabaseAnonKey) {
      this.addResult('Supabase Anon Key', 'PASS', 'Supabase anon key found (for database operations)');
    } else {
      this.addResult('Supabase Anon Key', 'FAIL', 'NEXT_PUBLIC_SUPABASE_ANON_KEY not found');
    }
  }

  private async testClerkAPI() {
    console.log('🔗 Testing Clerk API Connection...\n');
    
    try {
      const response = await fetch(`https://api.clerk.com/v1/users`, {
        headers: {
          'Authorization': `Bearer ${this.clerkSecretKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        this.addResult('Clerk API', 'PASS', 'Successfully connected to Clerk API');
      } else if (response.status === 401) {
        this.addResult('Clerk API', 'FAIL', 'Invalid Clerk secret key');
      } else {
        this.addResult('Clerk API', 'FAIL', `Clerk API error: ${response.status}`);
      }
    } catch (error) {
      this.addResult('Clerk API', 'FAIL', 'Failed to connect to Clerk API', error instanceof Error ? error.message : String(error));
    }
  }

  private printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 MIGRATION TEST RESULTS');
    console.log('='.repeat(60));

    let passCount = 0;
    let warnCount = 0;
    let failCount = 0;

    this.results.forEach(result => {
      const icon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
      console.log(`${icon} ${result.name}: ${result.message}`);
      if (result.details) {
        console.log(`   Details: ${result.details}`);
      }

      if (result.status === 'PASS') passCount++;
      else if (result.status === 'WARN') warnCount++;
      else failCount++;
    });

    console.log('\n' + '='.repeat(60));
    console.log(`📈 Summary: ${passCount} passed, ${warnCount} warnings, ${failCount} failed`);
    
    if (failCount === 0) {
      console.log('🎉 All tests passed! Clerk migration is ready.');
      console.log('\n📋 Next Steps:');
      console.log('1. Start your development server: npm run dev');
      console.log('2. Test sign-in flow at http://localhost:9002/login');
      console.log('3. Test Google OAuth functionality');
      console.log('4. Remove old Supabase auth dependencies');
    } else {
      console.log('🔧 Please fix the failed tests before proceeding with migration.');
    }
  }

  async runAllTests() {
    console.log('🚀 Starting Clerk Migration Tests...\n');
    
    await this.testEnvironmentVariables();
    await this.testClerkAPI();
    
    this.printResults();
  }
}

// Run the tests
const tester = new ClerkMigrationTester();
tester.runAllTests().catch(console.error);
```

#### Task 8.2: Test Migration Steps
**Action**: Follow these manual testing steps

1. **Run the test script**:
   ```bash
   npx tsx test-clerk-migration.ts
   ```

2. **Start the development server**:
   ```bash
   npm run dev
   ```

3. **Test authentication flows**:
   - Visit `http://localhost:9002/login`
   - Try email/password login
   - Try Google OAuth login
   - Test protected routes
   - Test sign-out functionality

4. **Verify database integration**:
   - Check that user profiles load correctly
   - Verify that Clerk user IDs are being stored

### Phase 9: Clean Up (1 hour)

#### Task 9.1: Remove Old Supabase Auth Files
**Action**: Delete or rename old auth files

**Files to remove/archive**:
- `src/features/auth/services/auth.service.ts` → Archive as `auth.service.ts.backup`
- `src/features/auth/services/auth-state.service.ts` → Archive as `auth-state.service.ts.backup`
- `src/features/auth/utils/middleware.utils.ts` → Archive as `middleware.utils.ts.backup`
- `src/providers/auth-session-provider.tsx` → Archive as `auth-session-provider.tsx.backup`

#### Task 9.2: Update Package Dependencies
**Action**: Remove unused Supabase auth dependencies

**In package.json, remove**:
```json
"@supabase/ssr": "^0.4.0"
```

**Run**:
```bash
npm uninstall @supabase/ssr
```

#### Task 9.3: Update Documentation
**Action**: Update README and documentation files

**Add to README.md**:
```markdown
## Authentication

This application uses Clerk for authentication with Google OAuth support.

### Environment Variables

```env
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_publishable_key
CLERK_SECRET_KEY=your_secret_key
```

### Development Setup

1. Install dependencies: `npm install`
2. Set up environment variables in `.env.local`
3. Run development server: `npm run dev`
4. Visit `http://localhost:9002` to test authentication
```

## Potential Issues and Solutions

### Issue 1: User Profile Migration
**Problem**: Existing users may not have `clerk_user_id` set
**Solution**: Create a migration script to update existing profiles when users first log in with Clerk

### Issue 2: OAuth Redirect URLs
**Problem**: Google OAuth redirects may not work immediately
**Solution**: Update Clerk dashboard with correct redirect URLs for your environment

### Issue 3: Database Permissions
**Problem**: Supabase Row Level Security (RLS) may block access with Clerk user IDs
**Solution**: Update RLS policies to check `clerk_user_id` instead of `auth.uid()`

### Issue 4: Session Management
**Problem**: Users may experience session conflicts during migration
**Solution**: Clear all browser storage and cookies during testing

## Testing Checklist

- [ ] Environment variables configured
- [ ] Clerk API connection working
- [ ] Login form loads correctly
- [ ] Email/password authentication works
- [ ] Google OAuth authentication works
- [ ] Protected routes redirect to login when not authenticated
- [ ] Dashboard loads for authenticated users
- [ ] User profile data loads correctly
- [ ] Sign-out functionality works
- [ ] Session persistence works across page refreshes

## Migration Notes

- **Database**: Supabase is still used for data storage, only authentication is moved to Clerk
- **User IDs**: Clerk user IDs are stored in the `clerk_user_id` column
- **OAuth**: Google OAuth is configured in the Clerk dashboard
- **Middleware**: Route protection is handled by Clerk middleware
- **Components**: UI components use Clerk hooks and components

This migration maintains all existing functionality while providing better authentication features through Clerk.
