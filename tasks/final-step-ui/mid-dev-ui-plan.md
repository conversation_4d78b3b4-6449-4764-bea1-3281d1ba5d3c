# Mid-Level UI Development Plan: Final Recipe Components Refactoring

## Executive Summary

After analyzing the standalone HTML reference (`tasks\final-step-ui\standalone-v1.html`) and the current React components, there are **significant UI discrepancies** that need to be addressed. The current React components follow a different design pattern than the intended standalone HTML reference.

**Key UI Gaps Identified:**
- ✅ **Timeline Navigation**: Current implementation is close but missing HTML-specific styling
- ❌ **Protocol Cards**: Missing droplet visualizer, collapsible sections, and recipe visual layout
- ❌ **Flip Cards**: Missing flip card animation for protocol summary
- ❌ **Safety Warnings**: Missing dynamic accordions with proper color coding
- ❌ **Tab Navigation**: Missing sticky tabs with proper styling

## Detailed UI Analysis

### Reference Design Analysis (standalone-v1.html)

The standalone HTML follows this structure:
```
1. Sticky Tab Navigation (4 tabs: Overview, Recipes, Studies, Security)
2. Overview Tab:
   - User Profile Card (left) + Therapeutic Strategy Card (right)
   - Protocol Summary with Flip Cards (3 cards with flip animation)
3. Recipes Tab:
   - Timeline Navigation (left sidebar) + Protocol Cards (right content)
   - Protocol Cards with: Time Badge, Droplet Visualizer, Quick Info, Collapsible Sections
4. Security Tab:
   - Dynamic accordion warnings with color coding
```

### Current React Implementation Gap Analysis

#### 1. Tab Navigation ⚠️ PARTIALLY IMPLEMENTED
**Current:** Basic tab switching
**Missing:** Sticky positioning, backdrop blur, proper icons, styling

#### 2. Timeline Navigation ✅ MOSTLY CORRECT
**Current:** Timeline with dots and cards
**Missing:** HTML-specific styling, active-box highlighting

#### 3. Protocol Cards ❌ MAJOR GAPS
**Current:** Basic recipe display
**Missing:** 
- Droplet visualizer with animations
- Time badges overlay
- Collapsible sections (Como Usar, Instruções de Preparo, Como Funciona)
- Quick info grid
- Protocol header styling

#### 4. Protocol Summary Cards ❌ MISSING FLIP ANIMATION
**Current:** Static summary cards
**Missing:** CSS flip card animation with front/back views

#### 5. Safety Warnings ⚠️ BASIC IMPLEMENTATION
**Current:** Static warnings display
**Missing:** Dynamic accordion with color-coded warnings

## Component-Specific Refactoring Plan

### Task 1: Tab Navigation Enhancement ⏱️ 45 minutes
**File:** `src/features/create-recipe/components/final-recipes-display.tsx`
**Complexity:** Medium

#### 1.1 Update Tab Navigation Structure
```tsx
// Replace current tab navigation with sticky design
<nav className="tabs-nav sticky top-0 z-10 bg-background/80 backdrop-blur-sm">
  <TabButton
    active={activeTab === 'overview'}
    onClick={() => switchTab('overview')}
    icon={<InfoIcon />}
    label={t('tabs.overview')}
  />
  {/* Similar for other tabs */}
</nav>
```

#### 1.2 Add Tab Button Component
```tsx
interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

const TabButton = ({ active, onClick, icon, label }: TabButtonProps) => (
  <button
    className={cn(
      "tab-button flex items-center gap-2 px-3 py-3 border-none bg-transparent cursor-pointer",
      "text-base font-medium text-muted-foreground border-b-2 border-transparent",
      "transform translate-y-0.5 transition-all duration-200 ease-in-out",
      "hover:text-primary",
      active && "text-primary font-semibold border-b-primary"
    )}
    onClick={onClick}
  >
    {icon}
    {label}
  </button>
);
```

### Task 2: Protocol Cards Major Refactoring ⏱️ 3 hours
**File:** `src/features/create-recipe/components/recipe-protocol-card.tsx`
**Complexity:** High

#### 2.1 Add Droplet Visualizer Component
```tsx
interface DropletVisualizerProps {
  oils: Array<{
    name_localized: string;
    drops: number;
    colorClass: string;
  }>;
}

const DropletVisualizer = ({ oils }: DropletVisualizerProps) => {
  return (
    <div className="droplet-visualizer flex justify-center items-end gap-1 h-10 pb-4 mb-2">
      {oils.map((oil) => 
        Array.from({ length: oil.drops }).map((_, index) => (
          <div
            key={`${oil.name_localized}-${index}`}
            className={cn(
              "droplet w-2 rounded-full animate-float",
              oil.colorClass,
              `h-${Math.min(6, oil.drops)}` // Height based on drop count
            )}
            style={{
              animationDelay: `${index * 0.1}s`
            }}
          />
        ))
      )}
    </div>
  );
};
```

#### 2.2 Add Quick Info Grid
```tsx
const QuickInfoGrid = ({ recipe }: { recipe: FinalRecipeProtocol }) => (
  <div className="quick-info grid grid-cols-4 gap-4 py-4">
    <div className="info-item text-center">
      <div className="label text-xs text-muted-foreground font-normal uppercase tracking-wide">
        Total Gotas
      </div>
      <div className="value text-lg font-bold text-foreground">
        {recipe.formulation.total_drops}
      </div>
    </div>
    {/* Similar for other info items */}
  </div>
);
```

#### 2.3 Add Collapsible Sections
```tsx
const CollapsibleSection = ({ 
  title, 
  subtitle, 
  icon, 
  children 
}: {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  children: React.ReactNode;
}) => (
  <Accordion type="single" collapsible>
    <AccordionItem value="item-1">
      <AccordionTrigger className="collapsible-strip bg-gradient-to-r from-primary to-primary/80 text-primary-foreground px-6 py-4 hover:from-primary/80 hover:to-primary">
        <div className="flex items-center gap-3">
          {icon}
          <div className="text-left">
            <div className="title font-semibold">{title}</div>
            <div className="subtitle text-sm opacity-80">{subtitle}</div>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent className="collapsible-content bg-muted/30 px-6 py-5">
        {children}
      </AccordionContent>
    </AccordionItem>
  </Accordion>
);
```

### Task 3: Flip Cards Implementation ⏱️ 2 hours  
**File:** `src/features/create-recipe/components/protocol-summary-card.tsx`
**Complexity:** Medium-High

#### 3.1 Add Flip Card Structure
```tsx
interface FlipCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
}

const FlipCard = ({ timeSlot, recipe, onViewDetails }: FlipCardProps) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const config = getTimeSlotConfig(timeSlot);

  return (
    <div className="h-96 flip-card flex flex-col">
      <div className={cn(
        "flip-card-inner flex-1 relative w-full h-full transition-transform duration-800",
        "transform-style-preserve-3d",
        isFlipped && "rotate-y-180"
      )}>
        {/* Front face */}
        <div className="flip-card-front absolute w-full h-full backface-hidden rounded-2xl overflow-hidden">
          <div className={cn(
            "bg-gradient-to-br p-8 flex flex-col items-center justify-between text-white",
            "shadow-2xl h-full", config.gradientClass
          )}>
            {/* Front content */}
          </div>
        </div>
        
        {/* Back face */}
        <div className="flip-card-back absolute w-full h-full backface-hidden rotate-y-180 rounded-2xl">
          <div className="bg-card p-6 overflow-y-auto relative rounded-2xl border shadow-lg h-full">
            {/* Back content */}
          </div>
        </div>
      </div>
    </div>
  );
};
```

#### 3.2 Add Flip Animation CSS
```css
.flip-card {
  perspective: 1000px;
}

.flip-card-inner {
  transform-style: preserve-3d;
}

.flip-card-front,
.flip-card-back {
  backface-visibility: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

.rotate-y-180 {
  transform: rotateY(180deg);
}
```

### Task 4: Safety Warnings Enhancement ⏱️ 1 hour
**File:** `src/features/create-recipe/components/safety-warnings.tsx`
**Complexity:** Medium

#### 4.1 Add Dynamic Color-Coded Warnings
```tsx
const getWarningColor = (type: string) => {
  switch (type) {
    case 'patch_test': return 'yellow';
    case 'storage': return 'blue';
    case 'alert': return 'red';
    case 'legal': return 'gray';
    default: return 'gray';
  }
};

const DynamicWarningsAccordion = ({ warnings }: { warnings: SafetyWarning[] }) => (
  <Accordion type="multiple">
    {warnings.map((warning, index) => {
      const color = getWarningColor(warning.type);
      return (
        <AccordionItem key={index} value={`warning-${index}`}>
          <AccordionTrigger className={cn(
            "px-4 py-3 font-medium",
            `bg-${color}-50 border-${color}-200 text-${color}-800`
          )}>
            {warning.title_localized}
          </AccordionTrigger>
          <AccordionContent className={cn(
            "px-4 py-3",
            `bg-${color}-25 border-${color}-100`
          )}>
            <div dangerouslySetInnerHTML={{ __html: warning.warning_text_localized }} />
          </AccordionContent>
        </AccordionItem>
      );
    })}
  </Accordion>
);
```

### Task 5: Timeline Navigation Styling ⏱️ 30 minutes
**File:** `src/features/create-recipe/components/recipe-timeline-navigation.tsx`
**Complexity:** Low

#### 5.1 Update Timeline Styling to Match HTML
```tsx
// Add these classes to match standalone HTML exactly
<div className="timeline relative">
  <div className="timeline-line absolute left-4 top-6 bottom-6 w-0.5 bg-border before:content-[''] before:absolute before:left-4 before:top-6 before:bottom-6 before:w-0.5 before:bg-border"></div>
  
  {timeSlots.map((timeSlot) => (
    <div className={cn(
      "timeline-item relative pl-10 pb-8 cursor-pointer transition-all duration-200",
      activeProtocol === timeSlot && "active active-box"
    )}>
      <div className={cn(
        "dot absolute left-4 top-6 transform -translate-x-1/2 w-5 h-5",
        "rounded-full border-3 transition-all duration-300",
        activeProtocol === timeSlot 
          ? "border-primary bg-primary" 
          : "border-border bg-background"
      )} />
      {/* Timeline item content */}
    </div>
  ))}
</div>
```

## CSS Additions Required

### Add these animations to globals.css:
```css
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

.animate-float {
  animation: float 2s ease-in-out infinite;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Droplet colors */
.droplet.lavanda { background-color: #a78bfa; }
.droplet.olibano { background-color: #fb923c; }
.droplet.copaiba { background-color: #4ade80; }
.droplet.bergamota { background-color: #f59e0b; }
.droplet.alecrim { background-color: #16a34a; }
.droplet.hortela { background-color: #2dd4bf; }

/* Timeline active state */
.timeline-item.active-box > div[class*='bg-'] {
  box-shadow: 0 2px 8px 0 rgba(56, 178, 172, 0.07), 0 0 0 2px #38b2ac22;
  background-color: #f0fdfa !important;
  border: 1.5px solid #5eead4;
  transition: box-shadow 0.18s, border 0.18s, background 0.18s;
}
```

## Component Dependencies

### Required Components from shadcn/ui:
- `Accordion` (for collapsible sections)
- `AccordionContent`
- `AccordionItem` 
- `AccordionTrigger`

### New Utility Functions Needed:
```tsx
// utils/oil-colors.ts
export const getOilColorClass = (oilName: string): string => {
  const colorMap: Record<string, string> = {
    'lavanda': 'droplet.lavanda',
    'olibano': 'droplet.olibano', 
    'copaiba': 'droplet.copaiba',
    'bergamota': 'droplet.bergamota',
    'alecrim': 'droplet.alecrim',
    'hortela': 'droplet.hortela'
  };
  
  const normalizedName = oilName.toLowerCase().replace(/[^a-z]/g, '');
  return colorMap[normalizedName] || 'bg-gray-400';
};
```

## Implementation Order & Dependencies

### Phase 1: Foundation (Day 1)
1. Tab Navigation Enhancement
2. Timeline Navigation Styling 
3. CSS Animations Setup

### Phase 2: Core Components (Day 2)
1. Protocol Cards Refactoring
2. Droplet Visualizer Implementation
3. Collapsible Sections

### Phase 3: Advanced Features (Day 3)  
1. Flip Cards Implementation
2. Safety Warnings Enhancement
3. Integration Testing

## Risk Assessment

**MEDIUM RISK** - Significant UI changes but well-isolated components

**Potential Issues:**
- CSS animation compatibility across browsers
- Flip card animation performance on mobile
- Theme integration with existing design system

**Mitigation:**
- Progressive enhancement approach
- Fallback for animations
- Thorough testing on mobile devices

## Testing Requirements

### UI Testing Checklist:
- [ ] Tab navigation sticky behavior
- [ ] Timeline active state highlighting  
- [ ] Droplet visualizer animations
- [ ] Flip card animation smooth transitions
- [ ] Collapsible sections expand/collapse
- [ ] Safety warnings color coding
- [ ] Mobile responsiveness
- [ ] Theme compatibility (dark/light modes)

## Estimated Implementation Time

**Total:** 3 days (24 hours)
- Tab Navigation: 45 minutes
- Timeline Navigation: 30 minutes  
- Protocol Cards: 3 hours
- Flip Cards: 2 hours
- Safety Warnings: 1 hour
- CSS Animations: 1 hour
- Integration & Testing: 16.75 hours

## Success Criteria

1. ✅ UI matches standalone HTML reference design exactly
2. ✅ All animations work smoothly across devices
3. ✅ Component performance remains optimal
4. ✅ Theme system integration preserved
5. ✅ Accessibility standards maintained
6. ✅ Mobile responsiveness achieved

This plan provides a comprehensive roadmap to transform the current React components to match the standalone HTML reference while maintaining code quality and performance standards.
