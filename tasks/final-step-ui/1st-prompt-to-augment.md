We changed the JSON schema in `src\features\create-recipe\prompts\final-recipes.yaml` to ensure it complies with OpenAI's structured output requirements and that it would fit properly the 'tasks\final-step-ui\standalone-v1.html' template.

Now we have to refactored our final step at 'http://localhost:9002/dashboard/create-recipe/final-recipes' to match more properly the 'tasks\final-step-ui\standalone-v1.html' UI.

AI Responses with correct JSON SCHEMA
JSON OUTPUT EXAMPLE FROM AI:
'tasks\final-step-ui\final-recipes-morning-1753406523978-output.json'
'tasks\final-step-ui\final-recipes-mid-day-1753406525896-output.json'
'tasks\final-step-ui\final-recipes-night-1753406525309-output.json'

This package includes the final JSON schema, architectural design, requirements, and a detailed mapping of the JSON to our  HTML template that now needs to be imported to the files that might be affected by it.
- 'src\features\create-recipe\components\final-recipes-display.tsx'
- 'src\features\create-recipe\components\safety-warnings.tsx'
- 'src\features\create-recipe\components\recipe-protocol-card.tsx'
- 'src\features\create-recipe\components\recipe-timeline-navigation.tsx'
- 'src\features\create-recipe\components\protocol-summary-card.tsx.'

The final end result should look like the 'tasks\final-step-ui\standalone-v1.html' template.

Of course. Let's break this down.

### Executive Summary: Is the Schema Enough?

**Yes, the JSON schema is very well-designed and covers about 95% of what's needed to populate the HTML and translated to the final step.** It's robust and provides almost all the necessary data points.

However, there are a few minor gaps and important implementation details to consider:

1.  **Aggregation is Required:** The "Visão Geral" tab summarizes information from all three protocols (Matinal, Diurno, Noturno). Since you'll receive three separate JSON responses, the application's backend or frontend will need to **aggregate** data from all three (e.g., collect all unique `therapeutic_properties_targeted` and `application_type_localized`) to build that summary tab.
2.  **Minor Data Gap:** A specific field in the HTML is not present in the schema (e.g., `Gravidade` for the user profile (can be removed for the final step result).
3.  **Static vs. Dynamic Content:** Some parts of your HTML are static (like the preparation checklist and safety warnings) but the schema provides data arrays for them. You should update the HTML to add the generated sections dynamically for maximum flexibility.
4.  **"Estudos Científicos" Tab:** This tab is empty in the HTML and the schema has no corresponding data. This is the largest gap and will be addressed only on future implementations. PLease, ignore this tab for now.

---

### Detailed Mapping: Populating the HTML from the JSON

Here is a detailed breakdown of which JSON fields map to which UI elements. This assumes correctly we have **three separate JSON objects in parallel calls**, one for each protocol.

#### Tab 1: Visão Geral (`#overview`)

This tab requires data aggregated from **all three** JSON responses, plus the `echo` block which will be the same in each.

**1. Perfil do Usuário Card (`.info-card`)**
*   **Condição:** `echo.health_concern_input` (e.g., "Dores de cabeça crônicas")
*   **Idade:** `echo.user_info_input.age_specific` + " " + `echo.user_info_input.age_unit` (e.g., "3 anos")
*   **Gênero:** `echo.user_info_input.gender` (e.g., "Feminino")
*   **Gravidade:** **[GAP]** This field is not in the schema and should be deleted on the UI.
*   **Causas Identificadas (Tags):** The schema provides `echo.selected_cause_ids`. You will need a separate lookup table/service to map these UUIDs to their localized names (e.g., `uuid_for_stress` -> "Estresse"). CHECK WHAT IS CURRENTLY IMPLEMENTED RIGHT NOW ON THE FINAL STEP UI.
*   **Sintomas (Tags):** Same as above. Use `echo.selected_symptom_ids` and a lookup to get the names.

**2. Estratégia Terapêutica Card (`.info-card`)**
*   **Propriedades Terapêuticas:**
    *   **Logic:** Collect `data.recipe_protocol.therapeutic_properties_targeted` arrays from all three JSON responses.
    *   Create a unique list of these properties, using `property_id` as the key Same as above. Use `property_id` and a lookup to get the names.
    *   For each unique property, display:
        *   `<strong>`: `name of property` (e.g., "Calmante")
        *   `<p>`: `description` (e.g., "Reduz ansiedade")
        we have this information in store already somewhere
*   **Métodos de Aplicação:**
    *   **Logic:** Collect `data.recipe_protocol.application_type_localized` from all three JSON responses.
    *   Display the unique values (e.g., "Tópica (roll-on)", "Aromática (difusão)").

**3. Resumo dos Protocolos (Flip Cards)**
Each card is populated by its corresponding JSON response.

*   **Matinal Card (`#flipResumoMatinal`)**
    *   **Front:**
        *   `<h3>`: `data.recipe_protocol.time_of_day_localized` (e.g., "Matinal")
        *   `<div> (Sinergia para)`: `data.recipe_protocol.recipe_theme_localized` (e.g., "Foco & Calma")
    *   **Back:**
        *   `<strong>Objetivo:</strong>`: `data.recipe_protocol.holistic_benefit_localized` (e.g., "Gestão de estresse e prevenção")
        *   `<ul> (Ingredients list)`: Loop through `data.recipe_protocol.ingredients.essential_oils`. For each `oil`:
            *   `<span> (Name)`: `oil.name_localized`
            *   `<span> (Drops)`: `oil.drops` + " gotas"
        *   `<strong>Preparo rápido:</strong>`: `data.recipe_protocol.preparation_summary_localized`
        *   `<strong>Como Usar:</strong>`: `data.recipe_protocol.usage_summary_localized`

*(Repeat the same mapping for the Diurno and Noturno flip cards using their respective JSON responses.)*

---

#### Tab 2: Receitas (`#recipes`)

This tab is the primary destination for a single `recipe_protocol` object. The `switchProtocol()` function will show the card corresponding to the selected JSON.

**1. Timeline Navigation (`.timeline`)**
*   **Logic:** Create one `.timeline-item` for each of the three JSON responses.
*   `data-protocol` attribute: derive from `data.recipe_protocol.time_slot` (e.g., "morning" -> "matinal").
*   `<p> (Time Range)`: `data.recipe_protocol.time_range_localized` (e.g., "07:00 - 09:00")
*   `<h4> (Title)`: "Protocolo " + `data.recipe_protocol.time_of_day_localized`
*   `<p> (Description)`: `data.recipe_protocol.description_localized`

**2. Protocol Card (`#protocol-matinal`, etc.)**
*   **Time Badge:**
    *   `<span> (Time)`: `data.recipe_protocol.time_range_localized`
    *   `<span> (Type)`: `data.recipe_protocol.application_type_localized`
*   **Protocol Header:**
    *   `.protocol-title`: `data.recipe_protocol.time_of_day_localized`
    *   `.protocol-subtitle`: `data.recipe_protocol.recipe_theme_localized`
*   **Droplet Visualizer:** This is visual. The number of droplets per oil comes from `data.recipe_protocol.ingredients.essential_oils[i].drops`. The color class (`lavanda`, `olibano`) would need to be mapped in the frontend based on `name_localized`.
*   **Quick Info Bar:**
    *   `Total Gotas`: `data.recipe_protocol.formulation.total_drops`
    *   `Diluição`: `data.recipe_protocol.formulation.dilution_percentage` + "%"
    *   `Tamanho`: `data.recipe_protocol.formulation.bottle_size_ml` + "ml"
    *   `Tampa`: `data.recipe_protocol.formulation.bottle_type_localized`
*   **Ingredients Grid:**
    *   **Essential Oils:** Loop through `data.recipe_protocol.ingredients.essential_oils`. For each `oil`:
        *   `.ingredient-badge`: `oil.drops` + " gotas"
        *   `.name`: `oil.name_localized`
        *   `.botanical`: `oil.scientific_name`
    *   **Carrier Oil:**
        *   **Recommended:** `data.recipe_protocol.ingredients.carrier_oil.recommended.name_localized` and `...properties_localized`.
        *   **Alternative:** `data.recipe_protocol.ingredients.carrier_oil.alternative.name_localized` and `...properties_localized`.
*   **Collapsible: "Como Usar"**
    *   **Logic:** Loop through `data.recipe_protocol.usage_instructions_localized`. For each `instruction`:
        *   `<h5>`: `instruction.method`
        *   `<p>`: `instruction.description`
        *   `<span> (Tag)`: `instruction.frequency`
*   **Collapsible: "Instruções de Preparo"**
    *   **[IMPROVEMENT]** The HTML has a static checklist. You should generate the `<label>` elements dynamically by looping through the `data.recipe_protocol.preparation_steps_localized` array.
*   **Collapsible: "Como Funciona"**
    *   **Logic:** Loop through `data.recipe_protocol.oil_rationales`. For each `rationale`:
        *   `<h5> (Oil Name)`: `rationale.name_localized`
        *   `Tags`: Loop through `rationale.properties` array to create the purple/orange/green tags.
        *   `<p> (Rationale)`: `rationale.rationale_localized`
    *   **Sinergia e Efeito Combinado:** **[GAP]** The schema does not have a dedicated field for this combined summary paragraph. The `ritual_suggestion_localized` field could potentially be used here, or you may need to add a new field to the schema (e.g., `synergy_rationale_localized`).

---

#### Tab 3: Estudos Científicos (`#studies`)

*   **[GAP]** This section is entirely unaddressed by the schema and will be addressed only on future implementations. PLease, ignore this tab for now.

---

#### Tab 4: Segurança (`#security`)

This tab can be agrregated from all three JSON responses. The warnings are populated from the `safety_warnings` array, which should be consistent across all three JSON responses.

*   **Logic:**
    *   **[IMPROVEMENT]** Instead of only the static `<details>` blocks in the HTML, you should dynamically add and generate them by looping through the `safety_warnings` array.
    *   The `item.type` field (e.g., "patch_test", "storage", "alert", "legal") can be used to apply the correct color styling (yellow, blue, red, gray).
    *   `<summary>`: `item.title_localized`
    *   `<p>` and other content inside `<details>`: `item.warning_text_localized` (This might contain HTML or Markdown that you'll need to render).

CONSTRAINTS:
- Do not change the themed colors, and do not hardcode any colors or styles on the components in order to respect our design system theme.
- Update the required translation files necessary to the updated and refactored final step. Remember that we have many components on that final step that needs to be translated.
- Do not break our 'npm run build' proccess that respect no dinamycally static pages (Generating static pages should be 100%)
- Respect our DRY, YAGNI and KISS concept for the project.
- Create requirements files using the EARS concept.