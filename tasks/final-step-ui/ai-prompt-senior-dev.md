# AI Prompt: Senior Developer - Final Recipe UI Implementation

## Context & Background

You are a **Senior Frontend Developer** tasked with creating a comprehensive implementation plan for the Final Recipe UI components. This project involves refactoring React TypeScript components to match a standalone HTML reference design.

### Files to Analyze (All in `tasks\final-step-ui\`):
1. `1st-prompt-to-augment.md` - Original requirements and JSON schema mapping
2. `2nd-prompt.md` - Updated technical requirements 
3. `instructions.md` - Implementation guidance
4. `junior-dev-plan.md` - Junior developer analysis (95% accurate)
5. `mid-dev-plan.md` - Mid-level UI implementation plan (**PRIMARY FOCUS**)
6. `mid-dev-ui-plan.md` - Detailed UI component refactoring plan (**PRIMARY FOCUS**)
7. `standalone-v1.html` - Reference design to match exactly
8. Sample JSON outputs: `final-recipes-morning-*.json`, `final-recipes-mid-day-*.json`, `final-recipes-night-*.json`

## Your Task

Create a **Senior Developer Implementation Plan** (`senior-dev-plan.md`) that:

### 1. **Validates & Enhances the Mid-Level Plans**
- Review `mid-dev-plan.md` and `mid-dev-ui-plan.md` for technical accuracy
- Identify any architectural improvements or missing considerations
- Validate component dependencies and integration points
- Assess performance implications and optimization opportunities

### 2. **Creates a Production-Ready Implementation Strategy**
- Break down the work into **Sprint-ready tickets** with clear acceptance criteria
- Define **Definition of Done** for each component
- Establish **testing strategy** (unit, integration, e2e, visual regression)
- Create **deployment & rollback plan**
- Document **monitoring & observability** requirements

### 3. **Architectural Decisions & Technical Improvements**
- Evaluate the current component architecture for scalability
- Recommend any design pattern improvements (composition, hooks, etc.)
- Identify potential performance bottlenecks and solutions
- Define **error boundaries** and **fallback UI** strategies
- Plan for **accessibility compliance** (WCAG 2.1 AA)

### 4. **Enhanced Color System for Droplet Visualizer**

**IMPORTANT REQUIREMENT CHANGE**: The droplet colors should NOT be based on oil names but follow a **numbered sequence system**:

```typescript
// Use theme variables from src/styles/globals.css
const DROPLET_COLORS = [
  'hsl(var(--primary))',      // 1st oil - primary color
  'hsl(var(--chart-1))',      // 2nd oil - chart-1 
  'hsl(var(--chart-2))',      // 3rd oil - chart-2
  'hsl(var(--chart-3))',      // 4th oil - chart-3  
  'hsl(var(--chart-4))',      // 5th oil - chart-4
  'hsl(var(--chart-5))',      // 6th oil - chart-5
  'hsl(var(--accent))',       // 7th oil - accent
  'hsl(var(--secondary))',    // 8th oil - secondary  
  'hsl(var(--muted))',        // 9th oil - muted
  'hsl(var(--destructive))'   // 10th oil - destructive (max oils supported)
];

// Usage: First oil in recipe gets index 0, second gets index 1, etc.
const getDropletColor = (oilIndex: number): string => {
  return DROPLET_COLORS[oilIndex % DROPLET_COLORS.length];
};
```

### 5. **Risk Assessment & Mitigation**
- Identify **technical risks** and mitigation strategies
- Plan for **browser compatibility** (especially CSS animations)
- Define **graceful degradation** for older browsers
- Establish **performance budgets** and monitoring
- Create **rollback procedures** for each component

### 6. **Team Coordination & Dependencies**
- Map **external dependencies** (design system, UI library updates)
- Define **API contract requirements** (if any backend changes needed)
- Identify **cross-team coordination** points
- Plan **knowledge transfer** and documentation needs

## Technical Context

### Current Tech Stack:
- **React 18+ with TypeScript**
- **Tailwind CSS** with custom theme variables
- **shadcn/ui components** (Accordion, etc.)
- **Zustand** for state management
- **Next.js** with static generation requirements
- **i18n** internationalization support

### Current Components Status:
- `final-recipes-display.tsx` - Main container ✅ Working, needs UI updates
- `safety-warnings.tsx` - Basic implementation ⚠️ Needs enhancement  
- `recipe-protocol-card.tsx` - Basic structure ❌ Major refactoring needed
- `recipe-timeline-navigation.tsx` - Close to target ✅ Minor styling updates
- `protocol-summary-card.tsx` - Missing flip animation ❌ New implementation needed

### Key Requirements:
- **Exact UI match** to `standalone-v1.html`
- **Static generation compatibility** (no dynamic imports)
- **Theme system integration** (light/dark mode support)
- **Mobile responsiveness**
- **Accessibility compliance**
- **Performance optimization**

## Deliverables Expected

Your `senior-dev-plan.md` should include:

### 1. Executive Summary
- High-level assessment of mid-level plans
- Key architectural decisions and rationales
- Risk level assessment and mitigation overview

### 2. Technical Architecture
- Component hierarchy and data flow validation
- State management strategy review
- Performance optimization plan
- Error handling and boundary strategy

### 3. Implementation Roadmap
- **Sprint 1**: Foundation (CSS setup, base components)
- **Sprint 2**: Core Features (droplet visualizer, collapsibles) 
- **Sprint 3**: Advanced Features (flip cards, animations)
- **Sprint 4**: Polish & Testing (accessibility, performance)

### 4. Detailed Ticket Breakdown
Each ticket should include:
- **Title & Description**
- **Acceptance Criteria** (specific, measurable)
- **Technical Requirements**
- **Dependencies** (other tickets, external)
- **Estimated Story Points**
- **Testing Requirements**
- **Definition of Done**

### 5. Quality Assurance Plan
- **Testing Strategy** (unit, integration, visual regression)
- **Performance Benchmarks** (Core Web Vitals targets)
- **Accessibility Checklist** (WCAG 2.1 AA compliance)
- **Browser Support Matrix**
- **Mobile Testing Requirements**

### 6. Deployment & Operations
- **Deployment Strategy** (feature flags, gradual rollout)
- **Monitoring & Observability** (error tracking, performance)
- **Rollback Procedures**
- **Documentation Requirements**

### 7. Enhanced Color System Implementation
- **Complete utility functions** for droplet color management
- **CSS custom properties integration** with theme variables
- **Dark/light mode compatibility** testing plan
- **Accessibility color contrast** validation

## Success Criteria

Your senior plan should be:
- ✅ **Production-ready**: Clear path to deployment
- ✅ **Risk-mitigated**: All major risks identified and planned for
- ✅ **Team-executable**: Clear tickets any developer can pick up
- ✅ **Quality-assured**: Comprehensive testing and validation plan
- ✅ **Performance-optimized**: Meets Core Web Vitals targets
- ✅ **Accessible**: WCAG 2.1 AA compliant
- ✅ **Maintainable**: Clean architecture and documentation

## Important Notes

- **UI Fidelity**: The final implementation must match `standalone-v1.html` exactly
- **Theme Integration**: All colors must use CSS custom properties from globals.css
- **Build Compatibility**: Must work with `npm run build` for static generation
- **Component Isolation**: Each component should be testable in isolation
- **Progressive Enhancement**: Graceful degradation for animations and advanced features

Create a plan that a development team can execute confidently with clear ownership, timelines, and success metrics.
