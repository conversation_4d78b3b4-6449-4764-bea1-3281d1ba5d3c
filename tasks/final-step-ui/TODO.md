# Essential Implementation Plan: Final Recipe UI Fixes

## Overview
Based on the analysis from mid-dev-plan.md, mid-dev-ui-plan.md, and senior-dev-plan.md, this document provides a focused implementation checklist that follows YAGNI principles - implementing only what's essential to match the standalone HTML reference design.

## Constraints
- No test file creation
- No performance optimizations  
- No monitoring implementation
- No memoization or caching
- Focus only on essential fixes needed
- **NO HARDCODED COLORS**: Use only CSS custom properties and theme classes (hsl(var(--primary)), bg-card, text-foreground, etc.)
- **NO HARDCODED TEXT**: All user-facing text must use translation keys (t('namespace:key')) or API-provided localized values
- **DESIGN SYSTEM COMPLIANCE**: All styling must use existing theme variables from globals.css

## Core Issue Analysis
**Status**: The architecture is 95% correct. Only essential UI styling updates needed to match standalone-v1.html reference design.

**Key Finding**: Data flow, streaming, and component structure are working correctly. Only missing:
1. UUID mapping for causes/symptoms in overview tab
2. Droplet visualizer styling updates
3. CSS flip card animations
4. Dynamic safety warnings accordion

---

## Essential Tasks (Priority Order)

### Task 1: UUID Mapping Fix (30 minutes) 🎯 CRITICAL
**File**: `src/features/create-recipe/components/final-recipes-display.tsx`
**Issue**: Echo data contains UUIDs but UI needs localized names
**Location**: Lines 495-520 (User Profile section)
**Reference**: Tab 1: Visão Geral (`#overview`) from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Add UUID lookup functions inside OverviewTab component:
```tsx
const getCauseNameById = useCallback((causeId: string) => {
  return selectedCauses.find(cause => cause.cause_id === causeId)?.cause_name || t('create-recipe:steps.final-recipes.overview.unknownCause');
}, [selectedCauses, t]);

const getSymptomNameById = useCallback((symptomId: string) => {
  return selectedSymptoms.find(symptom => symptom.symptom_id === symptomId)?.symptom_name || t('create-recipe:steps.final-recipes.overview.unknownSymptom');
}, [selectedSymptoms, t]);
```

2. Extract echo data after therapeuticStrategy useMemo:
```tsx
const echoData = React.useMemo(() => {
  const firstAvailableRecipe = generatedRecipes[0];
  return firstAvailableRecipe?.echo;
}, [generatedRecipes]);
```

3. Update causes display section (around line 496) using theme classes:
```tsx
{(selectedCauses.length > 0 || echoData?.selected_cause_ids) && (
  <div className="pt-2">
    <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
      {t('create-recipe:steps.final-recipes.overview.userProfile.identifiedCauses')}
    </span>
    <div className="flex flex-wrap gap-1 mt-1">
      {/* Show from selectedCauses array */}
      {selectedCauses.map((cause) => (
        <span key={cause.cause_id} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
          {cause.cause_name}
        </span>
      ))}
      {/* Show from echo data UUIDs */}
      {echoData?.selected_cause_ids?.map((causeId) => (
        <span key={causeId} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
          {getCauseNameById(causeId)}
        </span>
      ))}
    </div>
  </div>
)}
```

4. Update symptoms display section (around line 508) with similar pattern using theme classes:
```tsx
{(selectedSymptoms.length > 0 || echoData?.selected_symptom_ids) && (
  <div className="pt-2">
    <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
      {t('create-recipe:steps.final-recipes.overview.userProfile.identifiedSymptoms')}
    </span>
    <div className="flex flex-wrap gap-1 mt-1">
      {/* Show from selectedSymptoms array */}
      {selectedSymptoms.map((symptom) => (
        <span key={symptom.symptom_id} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
          {symptom.symptom_name}
        </span>
      ))}
      {/* Show from echo data UUIDs */}
      {echoData?.selected_symptom_ids?.map((symptomId) => (
        <span key={symptomId} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
          {getSymptomNameById(symptomId)}
        </span>
      ))}
    </div>
  </div>
)}
```

### Task 2: Recipe Timeline Navigation Update (45 minutes) 🎯 HIGH
**File**: `src/features/create-recipe/components/recipe-timeline-navigation.tsx`
**Issue**: Timeline needs to match standalone HTML structure and data mapping
**Reference**: Tab 2: Receitas (`#recipes`) Timeline Navigation from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Update timeline items to use JSON data structure:
```tsx
// Map from JSON data.recipe_protocol for each time slot
{timeSlots.map((timeSlot) => {
  const recipe = finalRecipes.find(r => r.time_slot === timeSlot);
  return (
    <div key={timeSlot} className="timeline-item" data-protocol={timeSlot}>
      <div className="dot"></div>
      <div className="content">
        <p className="time-range">{recipe?.time_range_localized}</p>
        <h4>{t('create-recipe:steps.final-recipes.timeline.protocol')} {recipe?.time_of_day_localized}</h4>
        <p className="description">{recipe?.description_localized}</p>
      </div>
    </div>
  );
})}
```

2. Add active state styling to match standalone HTML (using theme variables):
```css
.timeline-item.active-box > div[class^='bg-'] {
  box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.13);
  background-color: hsl(var(--primary) / 0.05) !important;
  border: 1.5px solid hsl(var(--primary) / 0.3);
  transition: box-shadow 0.18s, border 0.18s, background 0.18s;
}
```

### Task 3: Protocol Summary Card Flip Implementation (1 hour) 🎭 HIGH
**File**: `src/features/create-recipe/components/protocol-summary-card.tsx`
**Issue**: Need to implement flip card with front/back content mapping
**Reference**: Tab 1: Resumo dos Protocolos (Flip Cards) from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Update front card content:
```tsx
{/* Front Card */}
<div className="flip-card-front">
  <h3>{recipe.time_of_day_localized}</h3>
  <div className="synergy-label">{t('create-recipe:steps.final-recipes.protocolSummary.synergyFor')}</div>
  <div className="theme">{recipe.recipe_theme_localized}</div>
</div>
```

2. Update back card content with mapped data:
```tsx
{/* Back Card */}
<div className="flip-card-back">
  <strong>{t('create-recipe:steps.final-recipes.protocolSummary.objective')}:</strong> {recipe.holistic_benefit_localized}
  
  <ul className="ingredients-list">
    {recipe.ingredients.essential_oils.map((oil) => (
      <li key={oil.oil_id}>
        <span>{oil.name_localized}</span>
        <span>{oil.drops} {t('create-recipe:steps.final-recipes.protocolSummary.drops')}</span>
      </li>
    ))}
  </ul>
  
  <strong>{t('create-recipe:steps.final-recipes.protocolSummary.quickPreparation')}:</strong> {recipe.preparation_summary_localized}
  <strong>{t('create-recipe:steps.final-recipes.protocolSummary.howToUse')}:</strong> {recipe.usage_summary_localized}
</div>
```

### Task 4: Recipe Protocol Card Complete Refactor (2 hours) 🔧 CRITICAL
**File**: `src/features/create-recipe/components/recipe-protocol-card.tsx`
**Issue**: Component needs complete structure update to match standalone HTML
**Reference**: Tab 2: Protocol Card (`#protocol-matinal`, etc.) from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Update Time Badge structure:
```tsx
<div className="time-badge">
  <span>{recipe.time_range_localized}</span>
  <span>{recipe.application_type_localized}</span>
</div>
```

2. Update Protocol Header:
```tsx
<div className="protocol-header">
  <h2 className="protocol-title">{recipe.time_of_day_localized}</h2>
  <p className="protocol-subtitle">{recipe.recipe_theme_localized}</p>
</div>
```

3. Implement Droplet Visualizer with JSON mapping (INDEX-BASED colors):
```tsx
import { getDropletColor } from '../utils/droplet-colors';

<div className="droplet-visualizer">
  {recipe.ingredients.essential_oils.map((oil, oilIndex) => (
    Array.from({ length: oil.drops }).map((_, dropIndex) => (
      <div
        key={`${oil.oil_id}-${dropIndex}`}
        className="droplet"
        style={{
          backgroundColor: getDropletColor(oilIndex), // Uses index, not oil name
          height: `${12 + Math.random() * 8}px`,
          animationDelay: `${(oilIndex * oil.drops + dropIndex) * 0.1}s`
        }}
      />
    ))
  ))}
</div>
```

4. Update Quick Info Grid:
```tsx
<div className="quick-info grid grid-cols-4 gap-4">
  <div>
    <div className="label">{t('create-recipe:steps.final-recipes.protocol.totalDrops')}</div>
    <div className="value">{recipe.formulation.total_drops}</div>
  </div>
  <div>
    <div className="label">{t('create-recipe:steps.final-recipes.protocol.dilution')}</div>
    <div className="value">{recipe.formulation.dilution_percentage}%</div>
  </div>
  <div>
    <div className="label">{t('create-recipe:steps.final-recipes.protocol.size')}</div>
    <div className="value">{recipe.formulation.bottle_size_ml}ml</div>
  </div>
  <div>
    <div className="label">{t('create-recipe:steps.final-recipes.protocol.cap')}</div>
    <div className="value">{recipe.formulation.bottle_type_localized}</div>
  </div>
</div>
```

5. Update Ingredients Grid with JSON data:
```tsx
{/* Essential Oils */}
{recipe.ingredients.essential_oils.map((oil) => (
  <div key={oil.oil_id} className="ingredient-item">
    <div className="ingredient-badge">{oil.drops} {t('create-recipe:steps.final-recipes.protocol.drops')}</div>
    <div className="ingredient-details">
      <div className="name">{oil.name_localized}</div>
      <div className="botanical">{oil.scientific_name}</div>
    </div>
  </div>
))}

{/* Carrier Oil */}
<div className="carrier-oil-section">
  <h4>{t('create-recipe:steps.final-recipes.protocol.recommendedCarrierOil')}</h4>
  <div className="name">{recipe.ingredients.carrier_oil.recommended.name_localized}</div>
  <div className="properties">{recipe.ingredients.carrier_oil.recommended.properties_localized}</div>
  
  <h4>{t('create-recipe:steps.final-recipes.protocol.alternative')}</h4>
  <div className="name">{recipe.ingredients.carrier_oil.alternative.name_localized}</div>
  <div className="properties">{recipe.ingredients.carrier_oil.alternative.properties_localized}</div>
</div>
```

6. Implement Collapsible Sections with JSON data:

**Como Usar Section:**
```tsx
<details>
  <summary className="collapsible-strip">{t('create-recipe:steps.final-recipes.protocol.howToUse')}</summary>
  <div className="collapsible-content">
    {recipe.usage_instructions_localized.map((instruction, index) => (
      <div key={index} className="usage-item">
        <h5>{instruction.method}</h5>
        <p>{instruction.description}</p>
        <span className="frequency-tag">{instruction.frequency}</span>
      </div>
    ))}
  </div>
</details>
```

**Instruções de Preparo Section (Dynamic Checklist):**
```tsx
<details>
  <summary className="collapsible-strip">{t('create-recipe:steps.final-recipes.protocol.preparationInstructions')}</summary>
  <div className="collapsible-content">
    <div className="checklist">
      {recipe.preparation_steps_localized.map((step, index) => (
        <label key={index} className="checklist-item">
          <input type="checkbox" />
          <span>{step}</span>
        </label>
      ))}
    </div>
  </div>
</details>
```

**Como Funciona Section:**
```tsx
<details>
  <summary className="collapsible-strip">{t('create-recipe:steps.final-recipes.protocol.howItWorks')}</summary>
  <div className="collapsible-content">
    {recipe.oil_rationales.map((rationale, index) => (
      <div key={index} className="oil-rationale">
        <h5>{rationale.name_localized}</h5>
        <div className="properties-tags">
          {rationale.properties.map((property) => (
            <span key={property} className="property-tag">{property}</span>
          ))}
        </div>
        <p>{rationale.rationale_localized}</p>
      </div>
    ))}
    
    {/* Synergy section - use ritual_suggestion_localized as mentioned in gap analysis */}
    <div className="synergy-section">
      <h5>{t('create-recipe:steps.final-recipes.protocol.synergyAndCombinedEffect')}</h5>
      <p>{recipe.ritual_suggestion_localized}</p>
    </div>
  </div>
</details>
```

### Task 5: Safety Warnings Dynamic Accordion (45 minutes) 📋 HIGH
**File**: `src/features/create-recipe/components/safety-warnings.tsx`
**Issue**: Need dynamic accordion using JSON safety_warnings array
**Reference**: Tab 4: Segurança (`#security`) from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Map safety warnings with type-based color styling (using theme variables):
```tsx
const getWarningColorClass = (type: string) => {
  switch (type) {
    case 'patch_test': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'storage': return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'alert': return 'bg-red-50 border-red-200 text-red-800';
    case 'legal': return 'bg-muted border-border text-muted-foreground';
    default: return 'bg-muted border-border text-muted-foreground';
  }
};

const SafetyWarningsAccordion = ({ warnings }: { warnings: SafetyWarning[] }) => {
  return (
    <div className="safety-warnings space-y-2">
      {warnings.map((warning, index) => (
        <details key={index} className={`warning-item border rounded-lg p-4 ${getWarningColorClass(warning.type)}`}>
          <summary className="cursor-pointer font-medium">{warning.title_localized}</summary>
          <div className="mt-2" dangerouslySetInnerHTML={{ __html: warning.warning_text_localized }} />
        </details>
      ))}
    </div>
  );
};
```

### Task 6: Final Recipes Display Overview Tab Enhancement (1 hour) � HIGH
**File**: `src/features/create-recipe/components/final-recipes-display.tsx`
**Issue**: Overview tab needs complete data aggregation and user profile mapping
**Reference**: Tab 1: Visão Geral (`#overview`) - User Profile & Therapeutic Strategy from 1st-prompt-to-augment.md

**Implementation Steps**:
1. Enhance User Profile Card mapping (using theme classes only):
```tsx
{/* User Profile Card - Complete mapping with theme classes */}
<div className="bg-card border border-border rounded-lg p-6 shadow-sm">
  <h3 className="text-lg font-semibold text-foreground mb-4">{t('create-recipe:steps.final-recipes.overview.userProfile.title')}</h3>
  
  <div className="profile-data space-y-2">
    <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.condition')}:</strong> <span className="text-muted-foreground">{echoData?.health_concern_input}</span></div>
    <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.age')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input.age_specific} {echoData?.user_info_input.age_unit}</span></div>
    <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.gender')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input.gender}</span></div>
    {/* Remove Gravidade field as noted in gap analysis */}
  </div>
  
  {/* Enhanced Causes mapping */}
  {(selectedCauses.length > 0 || echoData?.selected_cause_ids) && (
    <div className="causes-section mt-4">
      <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">{t('create-recipe:steps.final-recipes.overview.userProfile.identifiedCauses')}</span>
      <div className="flex flex-wrap gap-1 mt-2">
        {selectedCauses.map((cause) => (
          <span key={cause.cause_id} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
            {cause.cause_name}
          </span>
        ))}
        {echoData?.selected_cause_ids?.map((causeId) => (
          <span key={causeId} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
            {getCauseNameById(causeId)}
          </span>
        ))}
      </div>
    </div>
  )}
  
  {/* Enhanced Symptoms mapping */}
  {(selectedSymptoms.length > 0 || echoData?.selected_symptom_ids) && (
    <div className="symptoms-section mt-4">
      <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">{t('create-recipe:steps.final-recipes.overview.userProfile.symptoms')}</span>
      <div className="flex flex-wrap gap-1 mt-2">
        {selectedSymptoms.map((symptom) => (
          <span key={symptom.symptom_id} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
            {symptom.symptom_name}
          </span>
        ))}
        {echoData?.selected_symptom_ids?.map((symptomId) => (
          <span key={symptomId} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
            {getSymptomNameById(symptomId)}
          </span>
        ))}
      </div>
    </div>
  )}
</div>
```

2. Enhance Therapeutic Strategy Card with aggregated data (using theme classes):
```tsx
{/* Therapeutic Strategy Card - Aggregated from all three protocols */}
<div className="bg-card border border-border rounded-lg p-6 shadow-sm">
  <h3 className="text-lg font-semibold text-foreground mb-4">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.title')}</h3>
  
  {/* Therapeutic Properties - aggregated */}
  <div className="therapeutic-properties mb-4">
    <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.properties')}</h4>
    <div className="space-y-2">
      {uniqueTherapeuticProperties.map((property) => (
        <div key={property.property_id} className="property-item">
          <div className="font-medium text-foreground">{getPropertyNameById(property.property_id)}</div>
          <p className="text-sm text-muted-foreground">{getPropertyDescriptionById(property.property_id)}</p>
        </div>
      ))}
    </div>
  </div>
  
  {/* Application Methods - aggregated */}
  <div className="application-methods">
    <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.applicationMethods')}</h4>
    <div className="flex flex-wrap gap-2">
      {uniqueApplicationMethods.map((method) => (
        <span key={method} className="inline-block px-3 py-1 text-sm font-medium bg-accent/10 text-accent-foreground rounded-full border border-accent/20">
          {method}
        </span>
      ))}
    </div>
  </div>
</div>
```

### Task 7: Droplet Color System (30 minutes) 🎨 MEDIUM
**File**: `src/features/create-recipe/utils/droplet-colors.ts` (new file)
**Issue**: Need consistent color system using existing CSS variables - INDEX-BASED only
**Reference**: Enhanced droplet color system from senior-dev-plan.md

**Implementation Steps**:
1. Create new utility file with index-based color system:
```tsx
// File: src/features/create-recipe/utils/droplet-colors.ts
export const DROPLET_COLORS = [
  'hsl(var(--primary))',      // Oil index 0
  'hsl(var(--chart-1))',      // Oil index 1
  'hsl(var(--chart-2))',      // Oil index 2
  'hsl(var(--chart-3))',      // Oil index 3
  'hsl(var(--chart-4))',      // Oil index 4
  'hsl(var(--chart-5))',      // Oil index 5
  'hsl(var(--accent))',       // Oil index 6
  'hsl(var(--secondary))',    // Oil index 7
  'hsl(var(--muted))',        // Oil index 8
  'hsl(var(--destructive))'   // Oil index 9
] as const;

export const getDropletColor = (oilIndex: number): string => {
  return DROPLET_COLORS[oilIndex % DROPLET_COLORS.length];
};
```

**Note**: Colors are assigned by oil position/index in the array, NOT by oil name. Oil at index 3 will always have the same color regardless of whether it's "Lavanda" or "Bergamota".

### Task 8: CSS Flip Card Animation (30 minutes) 🎭 MEDIUM
**File**: `src/styles/globals.css`
**Issue**: Missing flip card CSS for protocol summary cards
**Reference**: Design system compliant styles (NO hardcoded colors)

**Implementation Steps**:
1. Add flip card styles using theme variables only:
```css
/* Essential CSS additions - using theme variables only */
.flip-card {
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card.is-flipped .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 1rem;
  overflow: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* Droplet animation */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

.droplet {
  width: 8px;
  border-radius: 50%;
  animation: float 2s ease-in-out infinite;
}
```

**Note**: All colors will come from the index-based droplet color system using CSS custom properties only.
1. Add flip card styles matching design system (NO hardcoded colors):
```css
/* Flip card animation styles - using theme variables only */
.flip-card {
  perspective: 1000px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-card.is-flipped .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 1rem;
  overflow: hidden;
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* Droplet animation - using theme variables */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

.droplet {
  width: 8px;
  border-radius: 50%;
  animation: float 2s ease-in-out infinite;
}

/* Timeline styles - using theme variables */
.timeline {
  position: relative;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 1rem;
  top: 1.5rem;
  bottom: 1.5rem;
  width: 3px;
  background-color: hsl(var(--border));
}

.timeline-item .dot {
  position: absolute;
  left: 1rem;
  top: 1.5rem;
  transform: translateX(-50%);
  width: 1.25rem;
  height: 1.25rem;
  border-radius: 9999px;
  background-color: hsl(var(--background));
  border: 3px solid hsl(var(--border));
  transition: all 0.3s ease;
}

.timeline-item.active .dot {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary));
}

/* Active timeline highlighting - using theme variables */
.timeline-item.active-box > div[class^='bg-'] {
  box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.13);
  background-color: hsl(var(--primary) / 0.05) !important;
  border: 1.5px solid hsl(var(--primary) / 0.3);
  transition: box-shadow 0.18s, border 0.18s, background 0.18s;
}

/* Protocol card styles - using theme variables */
.protocol-card {
  background: hsl(var(--card));
  border-radius: 20px;
  box-shadow: 0 8px 24px hsl(var(--foreground) / 0.12);
  overflow: hidden;
  max-width: 550px;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  border: 1px solid hsl(var(--border));
}

.protocol-header {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  color: hsl(var(--primary-foreground));
  padding: 24px 24px 60px;
  position: relative;
}

.time-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: hsl(var(--primary-foreground) / 0.2);
  color: hsl(var(--primary-foreground));
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 10;
}

.protocol-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.protocol-subtitle {
  font-size: 1rem;
  opacity: 0.9;
}

.recipe-visual {
  background: hsl(var(--card));
  margin: -40px 24px 20px;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 16px hsl(var(--foreground) / 0.08);
  position: relative;
  z-index: 5;
}

.droplet-visualizer {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 4px;
  height: 40px;
  padding-bottom: 16px;
  margin-bottom: 8px;
}

.ingredient-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.ingredient-badge {
  flex-shrink: 0;
  text-align: center;
  font-size: 0.8rem;
  font-weight: 600;
  background-color: hsl(var(--primary) / 0.1);
  color: hsl(var(--primary));
  border-radius: 8px;
  width: 60px;
  padding: 8px 4px;
}

.ingredient-details .name {
  font-weight: 600;
  color: hsl(var(--foreground));
}

.ingredient-details .botanical {
  font-size: 0.8rem;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.collapsible-strip {
  list-style: none;
  cursor: pointer;
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
  color: hsl(var(--primary-foreground));
  padding: 16px 24px;
  transition: background 0.3s ease;
}

.collapsible-strip:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.8), hsl(var(--primary)));
}

.collapsible-strip .title {
  font-weight: 600;
}

.collapsible-strip .subtitle {
  font-size: 0.8rem;
  opacity: 0.8;
}

.collapsible-content {
  background-color: hsl(var(--muted) / 0.3);
  padding: 20px 24px;
}

.collapsible-strip::-webkit-details-marker {
  display: none;
}

.collapsible-strip .arrow {
  transition: transform 0.3s ease;
}

details[open] .collapsible-strip .arrow {
  transform: rotate(90deg);
}
```

---

## Implementation Order & Time Estimates

**Total Time**: 8 hours (comprehensive component mapping)

### Phase 1: Critical Data Mapping (2.5 hours)
1. ✅ Task 1: UUID Mapping Fix (30 minutes)
2. ✅ Task 6: Final Recipes Display Overview Tab Enhancement (1 hour)
3. ✅ Task 2: Recipe Timeline Navigation Update (45 minutes)
4. ✅ Task 7: Droplet Color System (30 minutes)

### Phase 2: Component Structure Updates (4 hours)
5. ✅ Task 4: Recipe Protocol Card Complete Refactor (2 hours)
6. ✅ Task 3: Protocol Summary Card Flip Implementation (1 hour)
7. ✅ Task 5: Safety Warnings Dynamic Accordion (45 minutes)
8. ✅ Task 8: CSS Flip Card Animation (30 minutes)

### Phase 3: Testing & Validation (30 minutes)
9. ✅ Component Integration Testing (15 minutes)
10. ✅ JSON Data Flow Validation (15 minutes)

---

## File References & JSON Mapping

### Files to Modify with Detailed Mapping:
1. **src/features/create-recipe/components/final-recipes-display.tsx** 
   - UUID mapping for causes/symptoms
   - Overview tab User Profile & Therapeutic Strategy cards
   - Data aggregation from all three time slots

2. **src/features/create-recipe/components/recipe-timeline-navigation.tsx**
   - Timeline structure mapping from JSON data
   - Active state styling
   - Protocol switching functionality

3. **src/features/create-recipe/components/protocol-summary-card.tsx**
   - Flip card front/back content mapping
   - JSON data binding for all time slots

4. **src/features/create-recipe/components/recipe-protocol-card.tsx**
   - Complete component restructure to match standalone HTML
   - Time badge, header, droplet visualizer
   - Quick info grid and ingredients mapping
   - Collapsible sections with dynamic content

5. **src/features/create-recipe/components/safety-warnings.tsx**
   - Dynamic accordion from safety_warnings array
   - Type-based color coding

6. **src/features/create-recipe/utils/droplet-colors.ts** (new file)
   - Color utility functions
   - Oil name to color mapping

7. **src/styles/globals.css**
   - Complete CSS from standalone HTML
   - Flip card, timeline, protocol card styles

### Reference Files & JSON Structure:
- **tasks/final-step-ui/standalone-v1.html** - Target design reference
- **tasks/final-step-ui/1st-prompt-to-augment.md** - Detailed JSON to HTML mapping
- **tasks/final-step-ui/final-recipes-morning-1753406523978-output.json** - JSON schema sample
- **src/features/create-recipe/prompts/final-recipes.yaml** - Updated JSON schema

### Key JSON Data Structure Expected:
```json
{
  "agentResult": {
    "finalOutput": {
      "data": {
        "recipe_protocol": {
          "time_slot": "morning|midday|night",
          "time_of_day_localized": "Manhã|Diurno|Noturno",
          "time_range_localized": "6h às 9h",
          "recipe_theme_localized": "Theme name",
          "description_localized": "Description",
          "holistic_benefit_localized": "Benefits",
          "application_type_localized": "Application method",
          "formulation": {
            "total_drops": 15,
            "dilution_percentage": 1,
            "bottle_size_ml": 10,
            "bottle_type_localized": "bottle type"
          },
          "ingredients": {
            "essential_oils": [
              {
                "oil_id": "uuid",
                "name_localized": "Oil name",
                "scientific_name": "Scientific name",
                "drops": 5
              }
            ],
            "carrier_oil": {
              "recommended": { "name_localized": "", "properties_localized": "" },
              "alternative": { "name_localized": "", "properties_localized": "" }
            }
          },
          "usage_instructions_localized": [
            {
              "method": "Method name",
              "description": "How to use",
              "frequency": "Frequency"
            }
          ],
          "preparation_steps_localized": ["Step 1", "Step 2"],
          "oil_rationales": [
            {
              "name_localized": "Oil name",
              "properties": ["property1", "property2"],
              "rationale_localized": "Why this oil"
            }
          ]
        }
      },
      "safety_warnings": [
        {
          "type": "patch_test|storage|alert|legal",
          "title_localized": "Warning title",
          "warning_text_localized": "Warning content"
        }
      ],
      "echo": {
        "health_concern_input": "User condition",
        "user_info_input": {
          "age_specific": 25,
          "age_unit": "anos",
          "gender": "Feminino"
        },
        "selected_cause_ids": ["uuid1", "uuid2"],
        "selected_symptom_ids": ["uuid3", "uuid4"]
      }
    }
  }
}
```

---

## Success Criteria

### Must Have (Essential):
- [ ] UUID mapping shows proper cause/symptom names
- [ ] Droplet visualizer uses theme colors
- [ ] Flip card animation works smoothly
- [ ] Safety warnings display dynamically
- [ ] No Hardcoded texts in it
- [ ] All texts are translated or coming from API
- [ ] No Hardcoded colors in it
- [ ] All colors are coming from the theme
- [ ] Namespaces translations are used and updated with our components fields to match the new UI

### Validation Steps:
1. Navigate to `/dashboard/create-recipe/final-recipes`
2. Complete wizard flow to trigger recipe generation
3. Verify Overview tab shows proper names (not UUIDs)
4. Check droplet colors match theme
5. Test flip card animations
6. Confirm safety warnings accordion works

**Note**: Follow the senior-dev-plan.md approach - minimal essential changes only, using existing patterns and components where possible.
