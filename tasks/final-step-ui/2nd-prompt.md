## Task: Refactor Final Recipe Step UI Components to Match JSON Schema

### Objective
Refactor the final recipe step UI at `http://localhost:9002/dashboard/create-recipe/final-recipes` to consume and display data from the updated JSON schema defined in `src\features\create-recipe\prompts\final-recipes.yaml`. The refactored components must match the design and functionality of the reference template `tasks\final-step-ui\standalone-v1.html`.

### Context
- **JSON Schema**: Located in `src\features\create-recipe\prompts\final-recipes.yaml` (complies with OpenAI structured output requirements)
- **Reference Design**: `tasks\final-step-ui\standalone-v1.html` (target UI design to match)
- **Sample AI Outputs**: 
  - `tasks\final-step-ui\final-recipes-morning-1753406523978-output.json`
  - `tasks\final-step-ui\final-recipes-mid-day-1753406525896-output.json`
  - `tasks\final-step-ui\final-recipes-night-1753406525309-output.json`

### Components to Refactor
The following React components need to be updated to consume the new JSON schema:
1. `src\features\create-recipe\components\final-recipes-display.tsx`
2. `src\features\create-recipe\components\safety-warnings.tsx`
3. `src\features\create-recipe\components\recipe-protocol-card.tsx`
4. `src\features\create-recipe\components\recipe-timeline-navigation.tsx`
5. `src\features\create-recipe\components\protocol-summary-card.tsx`

### Key Requirements

#### Data Handling
- **Three Protocol System**: Handle three separate JSON responses (morning, midday, night protocols)
- **Data Aggregation**: Implement logic to aggregate data from all three protocols for the "Visão Geral" tab
- **Dynamic Content Generation**: Replace static HTML elements with dynamic rendering based on JSON arrays (preparation steps, safety warnings, etc.)
- **UUID Mapping**: Implement lookup functionality for `selected_cause_ids` and `selected_symptom_ids` to display localized names

#### UI Tabs Implementation
1. **Visão Geral Tab**: Aggregate data from all three protocols
2. **Receitas Tab**: Display individual protocol details with timeline navigation
3. **Estudos Científicos Tab**: Skip implementation (future feature)
4. **Segurança Tab**: Dynamic safety warnings from JSON data

#### Technical Constraints
- **Design System**: Preserve existing themed colors and design system - no hardcoded styles
- **Build Process**: Maintain compatibility with `npm run build` for static page generation (100% static)
- **Architecture Principles**: Follow DRY, YAGNI, and KISS principles
- **Internationalization**: Update translation files for all new/modified text content
- **Requirements Documentation**: Create requirements using EARS (Easy Approach to Requirements Syntax) format

#### Data Mapping Specifications
- **User Profile**: Map `echo.health_concern_input`, `echo.user_info_input.age_specific`, `echo.user_info_input.gender`
- **Therapeutic Strategy**: Aggregate `therapeutic_properties_targeted` and `application_type_localized` from all protocols
- **Protocol Cards**: Map individual protocol data including ingredients, formulation details, usage instructions
- **Safety Warnings**: Dynamic generation from `safety_warnings` array with type-based styling

#### Deliverables
1. Refactored React components with new JSON schema integration
2. Updated translation files for internationalization
3. Requirements documentation using EARS format
4. Functional UI matching the reference template design
5. Maintained build process compatibility

### Success Criteria
- All components render correctly with sample JSON data
- UI matches the reference template design and functionality
- Build process remains functional for static page generation
- All text content is properly internationalized
- Code follows established architecture principles