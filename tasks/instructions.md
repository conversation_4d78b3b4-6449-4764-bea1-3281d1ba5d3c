On my overlay at final recipe step when i click [Copy JSON (minimal)]

I get a lot of fields that are currently not present in the information inputed to the 'src\features\create-recipe\prompts\final-recipes.yaml' which are really important for the AI to perform a crucial cross information process.

Each essential oil are currently being populated with:
###
Oil ID: 8af64ea7-75eb-4f4f-a568-4e0b20758060

English Name: Chamomile

Botanical Name: Matricaria chamomilla

Localized Name: Camomila

Safety Data: {"internal_use":{"id":"247f091e-d55c-4a76-aaef-ea4485457b63","code":"NON_INGESTIBLE","name":"Not for Internal Use","guidance":null,"description":"Essential oils in this category are not recommended or are unsafe for internal consumption. They are intended for topical application (diluted) or aromatic diffusion only."},"dilution":{"id":"3404c0a7-6158-4236-bbd9-724728538c3d","name":"[N] Neat","ratio":"1:1","description":"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.","percentage_max":0.5,"percentage_min":0},"phototoxicity":{"id":"ae18d720-4473-479e-b9f3-7fa65192d639","status":"Non-Phototoxic","guidance":"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.","description":"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."},"pregnancy_nursing":[],"child_safety":[],"internal_use_id":"247f091e-d55c-4a76-aaef-ea4485457b63","dilution_id":"3404c0a7-6158-4236-bbd9-724728538c3d","phototoxicity_id":"ae18d720-4473-479e-b9f3-7fa65192d639","pregnancy_nursing_ids":[],"child_safety_ids":[]}
###

But i want more information from each oil from the [Copy JSON (minimal)], not only the safety.

All fiedls from the [Copy JSON (minimal)] are here at 'tasks\copy-json-mininal-from-overlay.md' as an example of how it looks after it is populated.

Each oil on the prompt file should also be populated with this values also:
- "final_relevance_score": 80,

And with each properties that they will addresses, one or multiple:
      "properties": [
        {
          "property_id": "b9e7a512-0f4b-4f42-aa4a-8524a7c515f2",
          "match_rationale_localized": "Útil para massagem abdominal, aliviando cólicas e promovendo o relaxamento muscular da região digestiva.",
          "relevancy_to_property_score": 3,
          "recommendation_instance_score": 9.6
        },
        {
          "property_id": "e4f7d3a7-9f89-4c2a-8b4f-5241d1f0b3c7",
          "match_rationale_localized": "Possui ação anti-inflamatória e calmante, ajudando no alívio de cólicas e inflamações intestinais.",
          "relevancy_to_property_score": 4,
          "recommendation_instance_score": 7.2
        },
        {
          "property_id": "f7a2e7b8-8b5d-488a-86f9-65eac9464b0f",
          "match_rationale_localized": "Copaíba pode ser usada para massagens abdominais para alívio de cólicas e desconfortos intestinais, auxiliando na regulação digestiva.",
          "relevancy_to_property_score": 3,
          "recommendation_instance_score": 5.4
        }
      ]
    },


This fields will help the ai take more informed and educated suggestions for the final recipe

###########

IN other words:

I need to enhance the essential oil data structure that gets passed to the AI in the recipe creation system's final step. Currently, there's a discrepancy between the data available in the UI (debug data that I created in order to see the information) and what I need to gets sent to the AI prompt.

**Current Issue:**
- The [Copy JSON (minimal)] button in the overlay shows complete essential oil data including `final_relevance_score` and detailed `properties` arrays
- However, the AI prompt in `src\features\create-recipe\prompts\final-recipes.yaml` only receives basic essential oil information (name, safety data, etc.)
- This data mismatch limits the AI's ability to make informed therapeutic property cross-references during recipe generation

**Specific Data Missing from AI Prompt:**
The essential oil objects in the final-recipes.yaml prompt need to include these fields that are already available in the [Copy JSON (minimal)] output:

1. **Final Relevance Score:**
   ```json
   "final_relevance_score": 80
   ```

2. **Properties Array with detailed therapeutic information:**
   ```json
   "properties": [
     {
       "property_id": "b9e7a512-0f4b-4f42-aa4a-8524a7c515f2",
       "match_rationale_localized": "Útil para massagem abdominal, aliviando cólicas e promovendo o relaxamento muscular da região digestiva.",
       "relevancy_to_property_score": 3,
       "recommendation_instance_score": 9.6
     }
   ]
   ```

**Required Action:**
1. Locate the code that transforms essential oil data for the final recipe AI prompt
2. Modify the data transformation logic to include the `final_relevance_score` and `properties` array fields
3. Ensure the enhanced data structure gets properly populated into the final-recipes.yaml prompt file
4. Verify that the AI receives the same comprehensive therapeutic property data neste inside each oil that's available in the [Copy JSON (minimal)] functionality

**Expected Outcome:**
The AI will have access to detailed therapeutic property scoring and rationales for each essential oil, enabling more precise and educated recipe recommendations based on cross-referenced therapeutic benefits.

**Key Files to Investigate:**
- `src\features\create-recipe\prompts\final-recipes.yaml` (current prompt structure)
- `tasks\copy-json-mininal-from-overlay.md` (reference for complete data structure)
- Data transformation code that populates the final recipe prompt

- `src/features/create-recipe/utils/api-data-transform.ts` (createStreamRequest)
- `src/app/api/ai/streaming/route.ts` (prepareTemplateVariables)
- `src/lib/ai/utils/prompt-manager.ts` (template processing)
