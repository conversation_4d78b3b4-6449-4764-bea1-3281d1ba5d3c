CONSTRAINTS:
- Do not break our 'npm run build' proccess that respect no dinamycally static pages (Generating static pages should be 100%)
- Respect our DRY, YAGNI and KISS concept for the project.
- Create requirements files using the EARS concept.

I need to understand the whole data flow between the:
'src\features\create-recipe\prompts\final-recipes.yaml'
'src\features\create-recipe\components\final-recipes-display.tsx'
'src/features/create-recipe/components/recipe-debug-overlay.tsx'

This files my be helpful:
- `src/features/create-recipe/utils/oil-data-enrichment.ts` - Added formatting utilities
- `src/features/create-recipe/prompts/final-recipes.yaml` - Fixed template structure
- `src/features/create-recipe/components/final-recipes-display.tsx` - Fixed data pipeline
- `src/features/create-recipe/components/recipe-debug-overlay.tsx` - DRY implementation (The information needed are all in here already as a preview debug step that I created in order to view the necessary items need for the prompt)

since those files were a part of a previous fix that we did. Fix was documented in files at: '.kiro\specs\nested-handlebars-template-processing', please read them all to see if it helps.



Safety information is not being populated to my prompt. The way is handled it is correct, but it is passing only the IDS to the 

suggested_oils:

Oil ID: 2aaae25e-8d9a-43cd-8963-4b3abd2a242e
English Name: Lavender
Localized Name: Lavanda
Scientific Name: Lavandula angustifolia
Final Relevance Score: 66
Safety Data: {"internal_use":"2343a180-7dd2-45b1-a402-065b1bf2bd7c","dilution":"3404c0a7-6158-4236-bbd9-724728538c3d","phototoxicity":"ae18d720-4473-479e-b9f3-7fa65192d639","pregnancy_nursing":["1ae12b7d-04e1-4306-b218-7e4bd7b0865b","3735d6e6-89be-4887-901e-968170842c18","7e4dbc14-030e-4163-bbb4-d1bba8bfa73b"],"child_safety":[]} ***(only IDS are being passed)***
Therapeutic Property Contexts: { - Property ID: a1e2f3c4-b5d6-7e8f-9a0b-c1d2e3f4a5b6
Match Rationale: Lavanda é altamente eficaz para promover relaxamento mental e físico, diminuir o estresse e aliviar tensões musculares que agravam dores de cabeça.
Property Relevancy Score: 5/5
Recommendation Instance Score: 25
Property ID: c3f4e5d6-e7a8-9b0c-1d2f-3a4b5c6d7e8f
Match Rationale: Lavanda é amplamente reconhecida por suas propriedades calmantes e relaxantes, eficaz para aliviar o estresse e ajudar no relaxamento muscular, o que pode reduzir dores de cabeça tensionais.
Property Relevancy Score: 5/5
Recommendation Instance Score: 16
Property ID: d4f5a6b7-c8d9-0e1f-2a3b-4c5d6e7f8a9b
Match Rationale: Indicado para relaxamento e alívio da tensão muscular que contribui para dores de cabeça, podendo ser usado em massagens e difusores.
Property Relevancy Score: 5/5
Recommendation Instance Score: 16

The only ID behavior is wrong since by doing like this, my AI would not be able to understand what those IDS are. you need to pass the complete object so the AI can read the other fields that are more informative than just the ID.

The expected behavior is to send to the AI prompt variable likes this:
Safety Data: {"internal_use":{"id":"2343a180-7dd2-45b1-a402-065b1bf2bd7c","code":"FOOD_GRADE_EO","name":"Safe for Internal Use","guidance":null,"description":"Essential oils in this category are generally recognized or have specific indications suggesting they may be safe for internal use when following appropriate dilutions, dosage, and professional guidance."},"dilution":{"id":"3404c0a7-6158-4236-bbd9-724728538c3d","name":"[N] Neat","ratio":"1:1","description":"Neat. Essential oils categorized as “neat” can be applied topically without dilution on most people. These oils are considered mild and generally do not induce skin sensitivity. Examples of “neat” essential oils are frankincense, lavender, melaleuca, melissa, and sandalwood.","percentage_max":0.5,"percentage_min":0},"phototoxicity":{"id":"ae18d720-4473-479e-b9f3-7fa65192d639","status":"Non-Phototoxic","guidance":"No specific sun exposure restrictions related to phototoxicity are typically required after topical application. However, always follow general safety guidelines for dilution and application for all essential oils.","description":"Generally not known to cause phototoxic reactions when skin is exposed to UV light after topical application, when used appropriately."},"pregnancy_nursing":[{"id":"1ae12b7d-04e1-4306-b218-7e4bd7b0865b","code":"","name":"","description":"","usage_guidance":"","status_description":"pregnancy-safe-100"},{"id":"3735d6e6-89be-4887-901e-968170842c18","code":null,"name":"","description":"","usage_guidance":null,"status_description":"pregnancy-safe-3months"},{"id":"7e4dbc14-030e-4163-bbb4-d1bba8bfa73b","code":null,"name":null,"description":null,"usage_guidance":null,"status_description":"pregnancy-hora-do-parto"}],"child_safety":[],"internal_use_id":"2343a180-7dd2-45b1-a402-065b1bf2bd7c","dilution_id":"3404c0a7-6158-4236-bbd9-724728538c3d","phototoxicity_id":"ae18d720-4473-479e-b9f3-7fa65192d639","pregnancy_nursing_ids":["1ae12b7d-04e1-4306-b218-7e4bd7b0865b","3735d6e6-89be-4887-901e-968170842c18","7e4dbc14-030e-4163-bbb4-d1bba8bfa73b"],"child_safety_ids":[]}

- `src/features/create-recipe/components/recipe-debug-overlay.tsx` - DRY implementation (The information needed are all in here already as a preview debug step that I created in order to view the necessary items need for the prompt)

It is located in line 418 at                  {JSON.stringify(therapeuticProperties, null, 2)}
But we need to extract the safety information from there or the same source place since we have a DRY concept in place. Only the IDS are not enough to pass to the AI

There are fields that are already available at the UI when the call is made to create the final recipe, the only issue is how we are handling the data to pass those fields. We need to pass the complete safety data, not just the flattened IDS.

