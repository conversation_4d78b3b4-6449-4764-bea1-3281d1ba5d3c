I need to analyze and fix the data flow issue in the recipe creation feature where safety information is being passed as IDs only instead of complete objects to the AI prompt.

**Problem**: The safety data in the final recipe prompt is currently being flattened to only IDs (e.g., `"internal_use":"2343a180-7dd2-45b1-a402-065b1bf2bd7c"`), but the AI needs the complete objects with descriptive fields like `name`, `description`, `guidance`, etc.

**Key Files to Analyze**:
1. `src/features/create-recipe/prompts/final-recipes.yaml` - The prompt template that receives the data
2. `src/features/create-recipe/components/final-recipes-display.tsx` - The component that processes and sends data to the prompt
3. `src/features/create-recipe/components/recipe-debug-overlay.tsx` - Contains the complete safety data objects (line 418: `{JSON.stringify(therapeuticProperties, null, 2)}`) - this is our DRY source of truth, you need to get and handle the data from the same source as this file. If it is handled here, create a util to do the job to reuse it to the prompt. [important: this file will be deleted in the future]

**Supporting Files**:
- `src/features/create-recipe/utils/oil-data-enrichment.ts` - Formatting utilities
- `.kiro/specs/nested-handlebars-template-processing/*` - Previous fix documentation

**Expected Behavior**: Transform the safety data from ID-only format to complete objects before passing to the AI prompt, ensuring the AI receives descriptive information like:
```json
"internal_use": {
  "id": "2343a180-7dd2-45b1-a402-065b1bf2bd7c",
  "code": "FOOD_GRADE_EO", 
  "name": "Safe for Internal Use",
  "description": "Essential oils in this category are generally recognized..."
}
```

**Requirements**:
- Follow DRY principle: Use the same data source as `recipe-debug-overlay.tsx` 
- Maintain YAGNI, DRY and KISS principles
- Ensure the complete safety objects (not just IDs) are passed to the final recipe prompt
- Preserve existing functionality while enriching the data structure

Please trace the data flow from the debug overlay (which has the complete data) to the final prompt (which currently only gets IDs) and implement the fix to pass complete safety objects.

A more crude but somehow informative prompt is also available to get more detail at: tasks\safety-data\1st-prompt-to-augment.md