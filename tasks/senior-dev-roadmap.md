# Senior Dev Implementation Plan: Enhanced Essential Oil Data for Final Recipe AI

## Problem Analysis

The AI prompt for final recipe generation receives simplified essential oil data, missing crucial therapeutic information that's already computed and available in the debug overlay. The [Copy JSON (minimal)] functionality demonstrates the complete data structure exists, but this enhanced data isn't being passed to the AI prompt.

## Root Cause Analysis

1. **Data Source Identified**: The debug overlay (`recipe-debug-overlay.tsx`) contains the complete transformation logic that generates the enhanced oil structure with `final_relevance_score` and `properties` arrays
2. **Data Flow Gap**: The `createStreamRequest` function receives `PropertyOilSuggestions[]` and creates simplified oil objects, but doesn't apply the same enrichment logic used by the debug overlay
3. **Missing Transformation**: The scoring and property aggregation logic exists in the debug overlay but isn't reused for the AI prompt

## Implementation Strategy

### Task 1: Extract Data Transformation Logic from Debug Overlay

**Objective**: Move the oil enrichment logic from the debug overlay to a reusable utility function

**Current Location**: `src/features/create-recipe/components/recipe-debug-overlay.tsx` (lines ~100-200)

**New Location**: `src/features/create-recipe/utils/recipe-oil-data-enrichment.ts`

**Logic to Extract**:
```typescript
// Extract this logic from the debug overlay:
// 1. Build oil properties mapping from therapeuticProperties
// 2. Collect unique oils from all properties
// 3. Calculate final_relevance_score using enrichRecommendationDataWithScores
// 4. Merge properties array into each oil
// 5. Process safety data references
```

**New Utility Function**:
```typescript
export function enrichOilsForAI(
  therapeuticProperties: TherapeuticProperty[]
): EnrichedOilForAI[] {
  // Move the exact logic from debug overlay here
  // This ensures the AI gets the same data structure as Copy JSON (minimal)
}
```

### Task 2: Update API Data Transform Logic

**File**: `src/features/create-recipe/utils/api-data-transform.ts`

**Function**: `createStreamRequest`

**Current Issue**: Creates simplified oil objects from `PropertyOilSuggestions[]`

**Enhanced Logic**:
```typescript
import { enrichOilsForAI } from './oil-data-enrichment';

if (step === 'final-recipes' && additionalData) {
  const { timeSlot, suggestedOils } = additionalData;

  // Use the same enrichment logic as the debug overlay
  const enrichedOils = enrichOilsForAI(suggestedOils);

  const finalData = {
    ...data,
    time_of_day: timeSlot,
    suggested_oils: enrichedOils
  };

  return {
    feature,
    step,
    data: finalData
  };
}
```

### Task 3: Update YAML Template Structure

**File**: `src/features/create-recipe/prompts/final-recipes.yaml`

**Section**: `user_message` template

**Enhancement**: Update the oil rendering to match the debug overlay structure:

```yaml
- `suggested_oils`:
  {{#each suggested_oils}}
  ###
  Oil ID: {{oil_id}}
  English Name: {{name_english}}
  Botanical Name: {{name_botanical}}
  Localized Name: {{name_localized}}
  Final Relevance Score: {{final_relevance_score}}
  Safety Data: {{safety}}
  
  Therapeutic Properties Addressed: //(this is syntactically correct, do not try to change it)
  {{#each properties}}
  - Property ID: {{property_id}}
    Match Rationale: {{match_rationale_localized}}
    Property Relevancy Score: {{relevancy_to_property_score}}/5
    Recommendation Instance Score: {{recommendation_instance_score}}
  {{#unless @last}}
  ---
  {{/unless}}
  {{/each}}
```

### Task 4: Verify Handlebars Template Processing

**File**: `src/lib/ai/utils/prompt-manager.ts`

**Objective**: Ensure the nested Handlebars loops process the enhanced oil structure correctly

**Critical Template Processing Requirements**:

1. **Nested Loop Support**: The template uses `{{#each suggested_oils}}` containing `{{#each properties}}` - verify the prompt manager handles nested loops correctly

2. **Object Property Access**: Verify that nested object properties like `{{properties.match_rationale_localized}}` are processed correctly

3. **Conditional Rendering**: The `{{#unless @last}}` directive must work within nested loops to add separators between properties

**Verification Steps**:

```typescript
// Add debug logging in prompt-manager.ts processTemplate method
console.log('🔍 [Template Debug] Processing template variables:', {
  suggested_oils_count: variables.suggested_oils?.length || 0,
  first_oil_properties_count: variables.suggested_oils?.[0]?.properties?.length || 0,
  sample_property: variables.suggested_oils?.[0]?.properties?.[0] || null
});

// After template processing, log a sample of the processed output
console.log('🔍 [Template Debug] Processed template sample:', 
  processedTemplate.substring(processedTemplate.indexOf('suggested_oils'), 
  processedTemplate.indexOf('suggested_oils') + 500)
);
```

**Template Processing Test Cases**:

1. **Empty Properties Array**: Ensure oils with no properties don't break the template
2. **Single Property**: Verify single property renders without separators
3. **Multiple Properties**: Confirm separators (`---`) appear between properties but not after the last one
4. **Null/Undefined Values**: Handle missing rationales or scores gracefully
5. **Special Characters**: Ensure Portuguese text in rationales renders correctly

**Expected Template Output Sample**:
```
Oil ID: 7fd15cfa-5a18-4e12-ba6b-6da0d62d70bc
English Name: Fennel
Botanical Name: Foeniculum vulgare
Localized Name: Erva-doce
Final Relevance Score: 122.2
Safety Data: {...}

Therapeutic Properties Addressed:
- Property ID: d4e561f8-3a4e-4b67-8e35-3710a632b7d3
  Match Rationale: O óleo essencial de Erva-doce é indicado para auxiliar na digestão...
  Property Relevancy Score: 5/5
  Recommendation Instance Score: 25
---
- Property ID: a1c56f9e-2b4f-4d77-9e39-4e6483b7e2d1
  Match Rationale: Funcho é conhecido por sua eficácia em aliviar gases...
  Property Relevancy Score: 4/5
  Recommendation Instance Score: 20
```

### Task 5: Verify Data Source Consistency

**Objective**: Ensure the extracted utility produces identical results to the debug overlay

**Verification Steps**:
1. Compare output of new `enrichOilsForAI()` function with debug overlay's `minimalData.oils`
2. Verify `final_relevance_score` calculations match
3. Confirm `properties` arrays contain identical data structure
4. Test safety data processing consistency

### Task 6: Update Debug Overlay to Use Shared Logic

**File**: `src/features/create-recipe/components/recipe-debug-overlay.tsx`

**Objective**: Replace inline logic with the extracted utility function

**Change**: 
```typescript
// Replace the inline oil enrichment logic with:
import { enrichOilsForAI } from '../utils/oil-data-enrichment';

// In the component:
const enrichedOils = enrichOilsForAI(therapeuticProperties);
const minimalData = {
  // ... other fields
  oils: enrichedOils,
  // ... rest of structure
};
```

## Verification Plan

### 1. Utility Function Verification
- Extract the oil enrichment logic from debug overlay into `oil-data-enrichment.ts`
- Test that `enrichOilsForAI()` produces identical output to debug overlay's current logic
- Verify the function handles edge cases (missing properties, null values, etc.)

### 2. Data Transformation Verification
- Confirm `createStreamRequest` uses the new utility function correctly
- Verify the `suggested_oils` array in API requests contains:
  - `final_relevance_score` field (calculated using the same scoring system)
  - `properties` array with therapeutic details per oil
  - Properly processed safety data references

### 3. Template Processing Verification
- Implement the debug logging from Task 4 in `prompt-manager.ts`
- Verify nested Handlebars loops (`{{#each suggested_oils}}` → `{{#each properties}}`) work correctly
- Test that `{{#unless @last}}` conditional separators work within nested loops
- Confirm Portuguese text in `match_rationale_localized` renders without encoding issues
- Validate that missing/null property values don't break template processing

### 4. AI Response Quality Verification
- Generate final recipes and examine the AI's rationale
- Verify the AI references specific therapeutic properties and scores for individual oils
- Confirm the AI provides more detailed and informed recommendations based on property-specific data

### 5. Debug Overlay Consistency
- Update debug overlay to use the shared utility function
- Verify Copy JSON (minimal) still produces identical output after refactoring
- Ensure no regression in debug functionality

## Key Differences from Junior Approach

1. **Source Analysis**: Identified that the complete data transformation logic already exists in the debug overlay
2. **Code Reuse**: Extracts existing logic into a reusable utility instead of recreating it
3. **Data Consistency**: Ensures AI receives identical data structure to what's shown in debug overlay
4. **Maintainability**: Creates a single source of truth for oil enrichment logic
5. **Future-Proof**: Prepares for debug overlay deletion by extracting the logic

## Expected Outcome

The AI will receive the exact same comprehensive therapeutic property data that's currently available in the Copy JSON (minimal) functionality, enabling:

- **Precise Oil Selection**: AI can reference `final_relevance_score` for each oil
- **Detailed Property Context**: AI has access to `match_rationale_localized` for each oil-property relationship
- **Scoring-Based Decisions**: AI can use `relevancy_to_property_score` and `recommendation_instance_score` for informed recommendations
- **Enhanced Personalization**: AI can cross-reference therapeutic benefits with specific scoring data

This implementation ensures the AI has the same rich data context that developers currently see in the debug overlay, significantly improving recipe quality and therapeutic accuracy.