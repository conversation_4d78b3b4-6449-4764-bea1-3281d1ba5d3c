# Conversation Handover Prompt Generator

## Purpose
This prompt helps you create a focused summary to continue discussions in a new conversation context while preserving essential details and lessons learned.

## Instructions
Fill in the sections below based on your current conversation, then use the generated prompt to start a new focused discussion.

---

## Context Summary Template

### Current Discussion Overview
**Main Topic:** [Brief description of what you've been working on]

**Current Status:** [Where you are in the process - planning, development, debugging, etc.]

### Key Technical Areas Covered
Replace the placeholders below with specific topics from your conversation:

- **[Primary Focus Area 1]** - [Brief description of current state/progress]
- **[Secondary Focus Area 2]** - [Brief description of current state/progress] 
- **[Additional Area 3]** - [Brief description of current state/progress]

### Critical Information to Preserve

#### ✅ What's Working
- [Successful approaches, solutions, or decisions made]
- [Confirmed requirements or specifications]

#### ❌ Issues Encountered
- [Specific problems, bugs, or obstacles faced]
- [Failed approaches or dead ends]

#### 🔧 Solutions Applied
- [How issues were resolved or partially addressed]
- [Workarounds currently in place]

#### 📚 Lessons Learned
- [Key insights or best practices discovered]
- [Things to avoid or be cautious about]

#### 📁 Important Files/Resources
**Parent Files:** [Main configuration, schema, or architectural files]
**Child Files:** [Component, module, or dependent files]
**External Resources:** [APIs, libraries, documentation links]

---

## Generated Handover Prompt

Copy and use this in your new conversation:

```
I'm continuing a technical discussion from a previous conversation. Here's the essential context:

**Project:** [Your main topic/project]

**Current Focus:** We've been working on [specific areas you listed above], and I need to continue with focused discussion on these aspects:

• **[Primary Focus Area 1]** - [current state/specific needs]
• **[Secondary Focus Area 2]** - [current state/specific needs]
• **[Additional Area 3]** - [current state/specific needs]

**Key Context:**
- ✅ **Working Solutions:** [list main working approaches]
- ❌ **Known Issues:** [list main problems to avoid/address]
- 🔧 **Current Status:** [where you left off]
- 📁 **Important Files:** [list critical files/resources]

**Next Steps Needed:** [LEAVE IT A 'NEED TO COMPLETE' SPACE BECAUSE THIS IS FOR THE USER TO COMPLETE ON THE NEXT CONVERSATION, IT IS NOT DEFINED AT THE TIME WHEN THIS SUMMARY HANDOVER PROMPT IS BEING CREATED]

Please acknowledge this context and let me know you're ready to continue with [specific next action/question].
```

---

## Usage Tips

1. **Be Specific:** Replace all bracketed placeholders with actual details from your conversation
2. **Prioritize:** Focus on the 3-5 most important aspects rather than everything discussed
3. **Include Context:** Briefly explain why each area is important for the next phase
4. **Test the Prompt:** Make sure someone unfamiliar with your project could understand the basics from your summary

## Instructions
Fill in the sections below based on your current conversation, then use the generated prompt to start a new focused discussion. Note: The "Next Steps Needed" section is intentionally left blank for you to complete when starting your new conversation.