1. Create a 1st prompt to augment - save
 - Enhance this prompt with AugmentCode
Example: `prompt-tests\1st-prompt-to-augment.md`

2. Save the 2nd prompt to improve - save
Example: `prompt-tests\2n-prompt-enhanced.md`

3. Write a new prompt to analyze what is in the 1st promp, on the 2nd prompt, all the data required, and in the second prompt, in order to create a 'junior-dev-plan.md'
Example: `prompt-tests\initial-prompt.md`

4. Now you ask Gemini CLI to create the 'junior-dev-plan.md'
Example: `prompt-tests\junior-dev-plan.md`

5. a) Now you iterate the 2nd-review.md until you are happy with the 'mid-dev-plan.md'
Example: `prompt-tests\mid-dev-plan.md`

5. b) If not happy, delete junior-dev-plan and go back to step 5, until you are happy with the `mid-dev-plan.md`.


