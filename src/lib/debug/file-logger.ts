import { promises as fs } from 'fs';
import { join, dirname } from 'path';

async function ensureLogsDir(logDirectory: string) {
  try {
    await fs.mkdir(logDirectory, { recursive: true });
  } catch (error) {
    if (error instanceof Error) {
      const nodeError = error as NodeJS.ErrnoException;
      if (nodeError.code !== 'EEXIST') {
        console.error('Failed to create logs directory:', error);
      }
    } else {
      console.error('An unknown error occurred while creating logs directory:', error);
    }
  }
}

export class FileLogger {
  private logFile: string;
  private rawDataFile: string;
  private logs: string[] = [];
  private rawData: any[] = [];
  private isInitialized = false;
  private logDirectory: string;
  private mode: 'transactional' | 'streaming';
  private isProduction: boolean;

  constructor(options: { logDirectory: 'input' | 'output', fileName: string, mode: 'transactional' | 'streaming' }) {
    const { logDirectory, fileName, mode } = options;
    this.isProduction = process.env.NODE_ENV === 'production';
    this.logDirectory = join(process.cwd(), 'debug-logs', logDirectory);
    this.mode = mode;

    if (mode === 'streaming') {
      this.logFile = join(this.logDirectory, `${fileName}.log`);
      this.rawDataFile = join(this.logDirectory, `${fileName}-raw.json`);
    } else {
      this.logFile = join(this.logDirectory, fileName);
      this.rawDataFile = ''; // Not used in transactional mode
    }
    
    if (!this.isProduction) {
      this.initialize().catch(console.error);
    } else {
      this.isInitialized = true;
    }
  }

  private async initialize() {
    try {
      if (this.mode === 'streaming') {
        await this.writeToFile(this.logFile, '=== LOGGING SESSION STARTED ===\n');
        await this.appendRawData({ sessionStart: new Date().toISOString() });
      }
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize FileLogger:', error);
    }
  }

  private async writeToFile(filePath: string, content: string) {
    if (this.isProduction) {
      return;
    }
    
    try {
      await fs.mkdir(dirname(filePath), { recursive: true });
      
      if (this.mode === 'streaming') {
        await fs.appendFile(filePath, content, 'utf-8');
      } else {
        await fs.writeFile(filePath, content, 'utf-8');
      }
    } catch (error) {
      console.error(`Failed to write to ${filePath}:`, error);
    }
  }

  async log(data: any) {
    if (this.isProduction) {
      return;
    }
    
    if (this.mode === 'transactional') {
      await this.writeToFile(this.logFile, JSON.stringify(data, null, 2));
    } else {
      console.warn('log() method is only available in transactional mode.');
    }
  }

  async writeLog(message: string) {
    if (this.mode !== 'streaming') return;
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    this.logs.push(logEntry);
    
    if (this.isInitialized) {
      await this.writeToFile(this.logFile, logEntry);
    }
    
    console.log(`[FILE LOGGER] ${message}`);
  }

  private async appendRawData(data: any) {
    if (this.mode !== 'streaming') return;
    const timestamp = new Date().toISOString();
    const entry = {
      timestamp,
      data
    };
    
    this.rawData.push(entry);
    
    if (this.isInitialized) {
      await this.writeToFile(
        this.rawDataFile, 
        JSON.stringify(entry, null, 2) + '\n---\n'
      );
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.groupCollapsed(`[STREAMING RAW] ${data?.type || 'data'}`);
      console.log('Timestamp:', timestamp);
      console.groupEnd();
    }
  }

  async writeRawData(data: any) {
    await this.appendRawData(data);
  }

  // Method to get all logs (useful for debugging)
  getAllLogs() {
    if (this.mode !== 'streaming') return [];
    return [...this.logs];
  }
  
  // Method to get all raw data (useful for debugging)
  getAllRawData() {
    if (this.mode !== 'streaming') return [];
    return [...this.rawData];
  }

  logAgentResult(result: any) {
    if (this.mode !== 'streaming') return;
    this.writeLog('=== AGENT RESULT ANALYSIS ===');
    
    // Log basic properties
    this.writeLog(`Result keys: ${Object.keys(result).join(', ')}`);
    this.writeLog(`Has newItems: ${!!result.newItems}`);
    this.writeLog(`Has finalOutput: ${!!result.finalOutput}`);
    
    // Log newItems in detail
    if (result.newItems && Array.isArray(result.newItems)) {
      this.writeLog(`NewItems count: ${result.newItems.length}`);
      
      result.newItems.forEach((item: any, index: number) => {
        this.writeLog(`--- NEW ITEM ${index} ---`);
        this.writeLog(`Type: ${item.type || 'unknown'}`);
        this.writeLog(`Role: ${item.role || 'unknown'}`);
        
        if (item.tool_calls && Array.isArray(item.tool_calls)) {
          this.writeLog(`Tool calls: ${item.tool_calls.length}`);
          item.tool_calls.forEach((call: any, callIndex: number) => {
            this.writeLog(`  Tool call ${callIndex}: ${call.function?.name || 'unknown'}`);
          });
        }
        
        if (item.content) {
          const contentPreview = typeof item.content === 'string' 
            ? item.content.substring(0, 200) 
            : JSON.stringify(item.content).substring(0, 200);
          this.writeLog(`Content preview: ${contentPreview}...`);
        }
      });
      
      // Save full newItems to raw data file
      this.writeRawData({
        type: 'newItems',
        count: result.newItems.length,
        items: result.newItems
      });
    }
    
    // Log finalOutput
    if (result.finalOutput) {
      this.writeLog('=== FINAL OUTPUT ===');
      this.writeLog(`Final output type: ${typeof result.finalOutput}`);
      
      if (typeof result.finalOutput === 'object') {
        this.writeLog(`Final output keys: ${Object.keys(result.finalOutput).join(', ')}`);
        
        // Check for suggested_oils (updated for new schema)
        if (result.finalOutput.data?.suggested_oils) {
          const oils = result.finalOutput.data.suggested_oils;
          this.writeLog(`Suggested oils count: ${oils.length}`);
          
          oils.forEach((oil: any, index: number) => {
            this.writeLog(`  Oil ${index}: ${oil.name_localized || oil.name_english || 'unknown'}`);
            this.writeLog(`    Botanical: ${oil.name_botanical || 'unknown'}`);
          });
        }
        
        // Check for therapeutic_property_context
        if (result.finalOutput.data?.therapeutic_property_context) {
          const context = result.finalOutput.data.therapeutic_property_context;
          this.writeLog(`Property context: ${context.property_name_localized || context.property_name_english || 'unknown'}`);
        }
      }
      
      // Save full finalOutput to raw data file
      this.writeRawData({
        type: 'finalOutput',
        output: result.finalOutput
      });
    }
  }

  logStreamingEvent(event: any) {
    if (this.mode !== 'streaming') return;
    this.writeLog(`Streaming event: ${event.type || 'unknown'}`);
    this.writeRawData({
      type: 'streamingEvent',
      event
    });
  }

  logTextChunk(chunk: string) {
    if (this.mode !== 'streaming') return;
    this.writeLog(`Text chunk: ${chunk.length} chars`);
    this.writeRawData({
      type: 'textChunk',
      length: chunk.length,
      preview: chunk.substring(0, 100)
    });
  }

  close() {
    if (this.mode !== 'streaming') return;
    this.writeLog('=== LOGGING SESSION ENDED ===');
    this.writeRawData({ sessionEnd: new Date().toISOString() });
  }
}
