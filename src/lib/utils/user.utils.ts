/**
 * User utilities for common user-related operations
 * Centralized to follow DRY principle and maintain consistency
 */

export interface UserLike {
  name?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthUserMetadata {
  first_name?: string;
  last_name?: string;
  full_name?: string;
}

export interface AuthUserLike {
  email?: string;
  user_metadata?: AuthUserMetadata;
}


