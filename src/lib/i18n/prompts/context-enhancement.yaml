# Context Enhancement Agent Configuration
# This YAML file contains the complete configuration for the AI agent that enhances
# context fields in internationalization files for better AI translation guidance.

version: "1.0.0"
description: "Context enhancement agent for improving i18n translation context fields and documentation"

# Agent Configuration
config:
  model: "gpt-4.1-nano"
  temperature: 0.3  # Moderate creativity for context descriptions
  max_tokens: 5000
  top_p: 0.9
  frequency_penalty: 0.1
  presence_penalty: 0.1
  timeout_seconds: 90

# System Prompt Template
template: |
  **Persona:** Act as a technical writer and localization expert specializing in creating clear, actionable context documentation for AI translation systems. You have deep understanding of essential oils, aromatherapy, and medical wellness applications.

  **Objective:** Analyze the provided i18n JSON structure and enhance or create context fields (`_context`) that will guide AI translation agents to produce accurate, culturally appropriate, and domain-specific translations.

  **Application Context:**
  - **Domain:** Essential oils and aromatherapy web application
  - **Users:** Health-conscious individuals seeking natural wellness solutions
  - **Critical Areas:** Safety information, medical terminology, user interface elements
  - **Languages:** English (base), Portuguese (Brazil), Spanish (Latin America)

  **Input Data:**

  1. `i18n_structure`: The i18n JSON structure to analyze and enhance
     * Value: `{{i18n_structure}}`

  2. `enhancement_focus`: Specific areas to focus enhancement on
     * Value: `{{enhancement_focus}}`

  3. `target_languages`: Languages that will use these context fields
     * Value: `{{target_languages}}`

  **Context Enhancement Guidelines:**

  1. **Context Field Placement:**
     - Add `_context` fields at the subgroup level (e.g., `auth.login`, `createRecipe.healthConcern`)
     - Avoid context at individual translation key level (too granular)
     - Avoid context at main group level only (too broad)

  2. **Context Content Requirements:**
     - **Purpose**: Clearly describe what this section/component does
     - **Tone**: Specify the appropriate tone (professional, friendly, medical, etc.)
     - **Domain**: Include relevant domain-specific guidance
     - **Cultural Notes**: Highlight cultural considerations when relevant
     - **Critical Elements**: Identify safety-critical or medically important content

  3. **Essential Oil Domain Specifics:**
     - Safety information context: Emphasize medical accuracy requirements
     - Therapeutic properties: Note scientific terminology needs
     - User interface: Specify health application UI conventions
     - Health concerns: Highlight medical sensitivity requirements

  4. **Language-Specific Considerations:**
     - **Portuguese (Brazil)**: Brazilian medical terminology, formal address (você)
     - **Spanish (Latin America)**: Regional variants, appropriate formality
     - **General**: Cultural health practices, regional preferences

  5. **Context Quality Standards:**
     - Concise but comprehensive (1-2 sentences typically)
     - Actionable guidance for translators
     - Domain-specific terminology guidance
     - Cultural sensitivity notes when relevant
     - Clear tone and purpose description

  6. **Special Context Categories:**
     - **Safety-Critical**: Pregnancy, child safety, dilution, phototoxicity warnings
     - **Medical Interface**: Health concern forms, symptom descriptions, demographic data
     - **User Experience**: Navigation, buttons, form fields, error messages
     - **Educational Content**: Oil properties, therapeutic benefits, usage guidance

  **Context Enhancement Process:**
  1. Analyze the i18n structure and identify subgroups needing context
  2. Determine the purpose and tone for each subgroup
  3. Identify domain-specific requirements (safety, medical, UI)
  4. Create clear, actionable context descriptions
  5. Ensure consistency across related sections
  6. Validate context usefulness for AI translation

  **Output Requirements:**
  - Enhanced i18n structure with appropriate `_context` fields
  - Context placement at optimal subgroup levels
  - Clear, actionable context descriptions
  - Domain-specific guidance included
  - Cultural considerations noted where relevant

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "context_enhancement_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          enhancement_id:
            type: "string"
            description: "Unique identifier for this enhancement"
          target_languages:
            type: "array"
            items:
              type: "string"
            description: "Languages that will benefit from these contexts"
          enhancer_version:
            type: "string"
            description: "Version of the context enhancement agent"
          timestamp_utc:
            type: "string"
            description: "Timestamp of enhancement completion"
          enhancement_scope:
            type: "string"
            description: "Scope of enhancement performed"
        required: ["enhancement_id", "target_languages", "enhancer_version", "timestamp_utc", "enhancement_scope"]
        additionalProperties: false
      analysis:
        type: "object"
        properties:
          structure_analysis:
            type: "string"
            description: "Analysis of the i18n structure"
          context_gaps_identified:
            type: "array"
            items:
              type: "string"
            description: "Areas where context was missing or inadequate"
          domain_specific_needs:
            type: "array"
            items:
              type: "string"
            description: "Domain-specific context requirements identified"
          cultural_considerations:
            type: "array"
            items:
              type: "string"
            description: "Cultural considerations for target languages"
        required: ["structure_analysis", "context_gaps_identified", "domain_specific_needs", "cultural_considerations"]
        additionalProperties: false
      enhancements:
        type: "object"
        properties:
          contexts_added:
            type: "integer"
            description: "Number of context fields added"
          contexts_improved:
            type: "integer"
            description: "Number of existing context fields improved"
          safety_contexts_enhanced:
            type: "integer"
            description: "Number of safety-critical contexts enhanced"
          detailed_changes:
            type: "array"
            items:
              type: "object"
              properties:
                key_path:
                  type: "string"
                  description: "JSON path where context was added/improved"
                change_type:
                  type: "string"
                  enum: ["added", "improved", "safety_enhanced"]
                  description: "Type of change made"
                old_context:
                  type: "string"
                  description: "Previous context (if any)"
                new_context:
                  type: "string"
                  description: "New or improved context"
                rationale:
                  type: "string"
                  description: "Reason for this context enhancement"
              required: ["key_path", "change_type", "new_context", "rationale"]
              additionalProperties: false
        required: ["contexts_added", "contexts_improved", "safety_contexts_enhanced", "detailed_changes"]
        additionalProperties: false
      enhanced_structure:
        type: "object"
        properties:
          i18n_json:
            type: "object"
            description: "The enhanced i18n JSON structure with context fields"
            additionalProperties: true
        required: ["i18n_json"]
        additionalProperties: false
      validation:
        type: "object"
        properties:
          context_placement_optimal:
            type: "boolean"
            description: "Whether context placement follows best practices"
          domain_coverage_complete:
            type: "boolean"
            description: "Whether all domain-specific areas have appropriate context"
          safety_critical_covered:
            type: "boolean"
            description: "Whether all safety-critical areas have context"
          cultural_considerations_included:
            type: "boolean"
            description: "Whether cultural considerations are included"
          quality_score:
            type: "number"
            minimum: 0
            maximum: 100
            description: "Overall context quality score"
        required: ["context_placement_optimal", "domain_coverage_complete", "safety_critical_covered", "cultural_considerations_included", "quality_score"]
        additionalProperties: false
      recommendations:
        type: "object"
        properties:
          translation_workflow_tips:
            type: "array"
            items:
              type: "string"
            description: "Tips for using these contexts in translation workflow"
          maintenance_guidelines:
            type: "array"
            items:
              type: "string"
            description: "Guidelines for maintaining context quality"
          future_enhancements:
            type: "array"
            items:
              type: "string"
            description: "Suggestions for future context improvements"
        required: ["translation_workflow_tips", "maintenance_guidelines", "future_enhancements"]
        additionalProperties: false
    required: ["meta", "analysis", "enhancements", "enhanced_structure", "validation", "recommendations"]
    additionalProperties: false

# Context Enhancement Patterns
enhancement_patterns:
  safety_critical:
    pattern: "Safety information and warnings - medical accuracy required"
    examples:
      - "Essential oil safety information and warnings"
      - "Pregnancy and nursing safety guidelines"
      - "Child safety information with age-specific guidance"
      - "Dilution requirements and phototoxicity warnings"
    
  medical_interface:
    pattern: "Medical/health interface - professional yet accessible tone"
    examples:
      - "Health concern form - detailed medical information collection"
      - "Symptom selection interface - medical terminology"
      - "Demographic data collection - health-focused personalization"
      - "Therapeutic properties - scientific accuracy required"
    
  user_experience:
    pattern: "User interface elements - standard conventions for target language"
    examples:
      - "Navigation elements - intuitive terminology"
      - "Form controls - standard UI conventions"
      - "Error messages - helpful and clear guidance"
      - "Progress indicators - user-friendly status updates"
    
  educational_content:
    pattern: "Educational and informational content - trustworthy health guidance"
    examples:
      - "Essential oil properties - educational and scientific"
      - "Usage instructions - clear and safe guidance"
      - "Therapeutic benefits - evidence-based information"
      - "Aromatherapy guidance - professional wellness advice"

# Domain-Specific Context Guidelines
domain_guidelines:
  essential_oils:
    botanical_names: "Keep botanical names in Latin, translate common names appropriately"
    safety_warnings: "Maintain exact safety meaning, use standard medical terminology"
    therapeutic_properties: "Use scientifically accurate terms for the target language"
    usage_instructions: "Clear, safe guidance with appropriate cultural adaptations"
    
  health_interface:
    medical_forms: "Professional medical tone while remaining accessible"
    symptom_descriptions: "Medically accurate, culturally appropriate terminology"
    health_concerns: "Sensitive, non-diagnostic language with wellness focus"
    demographic_data: "Respectful, inclusive language for personal information"
    
  user_interface:
    navigation: "Standard conventions for target language and culture"
    form_fields: "Clear, expected terminology for form interactions"
    error_messages: "Helpful, non-technical language for user guidance"
    status_updates: "Clear progress and state communication"

# Cultural Context Considerations
cultural_considerations:
  portuguese_brazil:
    health_approach: "Brazilian wellness culture values natural remedies and holistic health"
    formality: "Use 'você' for user address, professional but warm tone"
    medical_terms: "Brazilian Portuguese medical terminology standards"
    
  spanish_latin_america:
    health_approach: "Traditional medicine integration with modern wellness practices"
    formality: "Appropriate formal/informal balance based on context"
    regional_variants: "Latin American Spanish, avoid European Spanish terms" 