{"title": "Server-Only i18n Test Page", "description": "This page validates that the server-only internationalization system is working correctly without FOUC or hydration mismatches.", "status": {"title": "✅ Server-Only i18n Working", "message": "This content was rendered on the server with translations loaded at build/render time. No client-side translation loading occurred."}, "current-locale": "Current Locale", "timestamp": "Rendered At", "features": {"server-only": {"title": "Server-Only Rendering", "description": "All translations are loaded and rendered on the server using React cache() for optimal performance."}, "no-fouc": {"title": "No FOUC", "description": "Content appears instantly in the correct language without any flash of untranslated content."}}, "view-examples": "View Examples"}