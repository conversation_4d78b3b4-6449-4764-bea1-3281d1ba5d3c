{"_ai_translation_instructions": {"target_language": "English (US)", "context": "Multi-step essential oil recipe creation wizard. Users provide health information to receive personalized aromatherapy recommendations.", "tone": "Supportive, encouraging, and professional. Like a knowledgeable health consultant guiding the user.", "notes": "Health and wellness terminology should be accurate but accessible. Emphasize personalization and care. Users may be in vulnerable states when seeking health solutions."}, "title": "Essential Oil Recipe Creator", "subtitle": "Get personalized essential oil recommendations for your health concerns", "chatInput": {"_context": "Chat-style input for health concerns - conversational interface for recipe creation", "title": "Create Your Recipe", "description": "Tell us about your health concern and we'll create a personalized essential oil recipe for you", "subtitle": "Be as specific as possible for the best recommendations", "placeholder": "Describe your health concern in detail...", "examples": {"headaches": "I have chronic headaches that worsen with stress and lack of sleep", "digestive": "Experiencing digestive issues with bloating after meals", "sleep": "Di<PERSON><PERSON><PERSON>y falling asleep and staying asleep, feeling anxious at bedtime", "tension": "Muscle tension in shoulders and neck from desk work"}, "characterCounter": "{count}/500", "validation": {"tooShort": "Please provide more details (minimum 3 characters)", "tooLong": "Please keep under 500 characters"}}, "healthConcern": {"_context": "Health concern form - detailed medical information collection for aromatherapy recommendations", "title": "What's your health concern?", "description": "Describe your health concern in detail. The more specific you are, the better we can help you find the right essential oils.", "label": "Health Concern", "placeholder": "For example: I've been experiencing anxiety and stress from work, especially during busy periods. I have trouble sleeping and feel tense throughout the day...", "characterCounter": "{count}/500", "minCharacters": "Minimum 3 characters required", "examples": {"title": "💡 Examples of good health concerns:", "headaches": "I have chronic headaches that worsen with stress and lack of sleep", "digestive": "Experiencing digestive issues with bloating after meals", "sleep": "Di<PERSON><PERSON><PERSON>y falling asleep and staying asleep, feeling anxious at bedtime", "tension": "Muscle tension in shoulders and neck from desk work"}}, "demographics": {"_context": "Demographics form - personal information for personalized essential oil recommendations", "title": "Tell us about yourself", "description": "This information helps us provide more personalized essential oil recommendations based on your demographics.", "gender": {"label": "Gender", "options": {"male": "Male", "female": "Female"}}, "ageCategory": {"label": "Age Category", "options": {"child": {"label": "Child (0-12)", "description": "Pediatric considerations"}, "teen": {"label": "Teen (13-17)", "description": "Adolescent development"}, "adult": {"label": "Adult (18-64)", "description": "General adult population"}, "senior": {"label": "Senior (65+)", "description": "Elderly considerations"}}}, "specificAge": {"label": "Specific Age: {age}", "unit": "Years old", "placeholder": "Select category", "instructions": {"withCategory": "Use the + and - buttons or the slider to adjust your age.", "selectFirst": "Please select an age category first."}}}, "symptoms": {"title": "What symptoms are you experiencing?", "description": "Based on your selected causes, here are potential symptoms. Select all that you're currently experiencing.", "selectedCauses": "Selected causes:", "tableHeader": "Symptoms", "selectionCounter": "Select 1-{count} symptoms that you're experiencing", "selected": "{selected}/{total} selected", "noSymptoms": "No potential symptoms found. Please go back to the causes step to generate them.", "missingData": "Missing Required Data"}, "steps": {"_context": "Recipe creation step navigation and descriptions", "health-concern": {"title": "Health Concern", "description": "Tell us about your health concern or goal", "placeholder": "Describe your health concern, symptoms, or wellness goals...", "examples": "Examples: headaches, stress, sleep issues, skin problems"}, "demographics": {"title": "Demographics", "description": "Help us personalize your recommendations"}, "causes": {"title": "Potential Causes", "description": "Select what might be contributing to your concern", "loading": "Analyzing your health concern...", "selectMultiple": "Select all that apply", "noResults": "No potential causes found. Please try rephrasing your health concern."}, "symptoms": {"title": "Symptoms", "description": "Select the symptoms you're experiencing", "loading": "Finding related symptoms...", "selectMultiple": "Select all that apply", "noResults": "No symptoms found. Please review your selected causes."}, "properties": {"title": "Therapeutic Properties", "description": "Based on your selections, these properties may help", "loading": "Determining therapeutic properties...", "oilSuggestions": "Oil Suggestions", "loadingOils": "Finding suitable oils...", "noOils": "No oils found for this property", "noProperties": "No therapeutic properties found. Please go back to the symptoms step.", "analyzing": "Analyzing properties to find the best essential oils... ({count}/{total} completed)", "recommendations": "Essential oil recommendations for your therapeutic properties {hasOils, select, true {ready below} other {loading...}}", "recommendationsReady": "Essential oil recommendations for your therapeutic properties ready below", "recommendationsLoading": "Essential oil recommendations for your therapeutic properties loading...", "reanalyze": "Re-analyze", "next": "Continue to Next Step", "analysisComplete": "Analysis complete"}, "oils": {"title": "Essential Oils", "description": "Your personalized essential oil recommendations"}, "final-recipes": {"_context": "Final step showing three time-specific essential oil protocols", "title": "Your Personalized Recipes", "description": "Three time-specific protocols tailored to your health concern", "loading": "Generating your personalized recipes...", "tabs": {"overview": "Overview", "recipes": "Recipes", "studies": "Scientific Studies", "security": "Safety"}, "overview": {"unknownCause": "Unknown cause", "unknownSymptom": "Unknown symptom", "userProfile": {"title": "User Profile", "condition": "Condition", "age": "Age", "gender": "Gender", "identifiedCauses": "Identified Causes", "symptoms": "Symptoms", "identifiedSymptoms": "Identified Symptoms", "notProvided": "Not provided"}, "therapeuticStrategy": {"title": "Therapeutic Strategy", "properties": "Therapeutic Properties", "applicationMethods": "Application Methods", "topical": "Topical (roll-on)", "aromatic": "Aromatic (diffusion)"}, "protocolSummary": {"title": "Protocol Summary", "viewRecipe": "View recipe →", "synergyFor": "Synergy for", "viewDetails": "View Details", "clickToView": "Click the button to see details", "close": "Close", "objective": "Objective", "drops": "drops", "quickPrep": "Quick preparation", "prepInstructions": "Mix the oils in a {size}ml roll-on bottle and fill with {carrierOil}. Shake well before use.", "howToUse": "How to Use"}}, "protocolSummary": {"synergyFor": "Synergy for", "objective": "Objective", "drops": "drops", "quickPreparation": "Quick preparation", "howToUse": "How to Use"}, "timeline": {"protocol": "Protocol"}, "protocols": {"morning": {"label": "Morning", "subtitle": "Morning Protocol", "timeRange": "7:00 AM - 9:00 AM", "purpose": "Focus & Calm", "description": "Focus and calm to start the day"}, "midDay": {"label": "Daytime", "subtitle": "Daytime Protocol", "timeRange": "12:00 PM - 2:00 PM", "purpose": "Immediate Pain Relief", "description": "Immediate pain relief"}, "night": {"label": "Evening", "subtitle": "Evening Protocol", "timeRange": "8:00 PM - 10:00 PM", "purpose": "Restorative Sleep", "description": "Restorative sleep and relaxation"}}, "recipeCard": {"totalDrops": "Total Drops", "dilution": "Dilution", "size": "Size", "dispenser": "Dispenser", "essentialOils": "Essential Oils", "carrierOil": "Carrier Oil", "recommended": "Recommended", "alternative": "Alternative", "quantity": "Quantity", "topicalUse": "Topical Use", "rollOn": "Roll-on", "dropper": "Dropper", "spray": "Spray", "drops": "drops", "of": "of", "ingredients": "Ingredients", "sections": {"usage": {"title": "How to Use", "subtitle": "Application methods and frequency"}, "preparation": {"title": "Preparation Instructions", "subtitle": "Step-by-step mixing guide", "ingredients": "Ingredients", "steps": "Preparation Steps", "completed": "completed"}, "science": {"title": "How It Works", "subtitle": "The science behind the oils", "rationale": "Scientific Rationale"}}}, "safety": {"title": "Safety Guidelines", "profile": "Safety Profile", "safeMessage": "Based on your age and profile, the recommended recipes are safe when used as directed.", "warnings": {"ageRestriction": "Age Restriction", "pregnancy": "Pregnancy/Nursing", "phototoxicity": "Photosensitivity", "dilution": "Dilution", "general": "General"}, "guidelines": {"title": "General Safety Guidelines", "beforeUse": {"title": "Before Use:", "patchTest": "Perform a sensitivity test on a small area of skin", "checkAllergies": "Check for known allergies to ingredients", "consultProfessional": "Consult a professional if pregnant or nursing", "adultSupervision": "Adult supervision required for children"}, "duringUse": {"title": "During Use:", "avoidEyes": "Avoid contact with eyes and mucous membranes", "noOpenWounds": "Do not apply to open wounds or irritated skin", "stopIfReaction": "Discontinue use if adverse reaction occurs", "stayHydrated": "Maintain adequate hydration"}, "storage": {"title": "Storage:", "coolDry": "Keep in cool, dry place", "protectLight": "Protect from direct sunlight", "darkGlass": "Use dark glass bottles", "keepAway": "Keep out of reach of children"}, "emergency": {"title": "Emergency:", "washIrritation": "If irritation occurs, wash with plenty of water", "seekHelp": "If accidentally ingested, seek medical help", "keepNumbers": "Keep emergency numbers handy", "documentReactions": "Document any adverse reactions"}}}, "studies": {"title": "Scientific Studies", "description": "This section will contain scientific references and studies that support the therapeutic recommendations.", "inDevelopment": "In development: Integration with scientific database in progress."}, "errors": {"generationFailed": "Failed to generate recipes. Please try again.", "missingData": "Missing required data for recipe generation", "recipeNotAvailable": "Recipe not available"}}}, "actions": {"_context": "Quick actions from progress sidebar", "saveProgress": "Save Progress", "viewPrevious": "View Previous Recipes", "startNew": "Start New Recipe"}, "navigation": {"_context": "Recipe creation progress and navigation elements", "breadcrumb": {"ariaLabel": "Recipe creation progress", "navigateTo": "Navigate to {step}", "progress": "Progress", "completed": "{completedCount} of {totalSteps} completed", "percentage": "{percentage}% complete"}, "compact": {"ariaLabel": "Recipe creation navigation"}, "progress": "Step {current} of {total}", "completedProgress": "{progress} of {total} completed", "currentStepAnnouncement": "Currently on {title}, step {progress} of 6", "buttons": {"previous": "Previous", "next": "Next", "complete": "Complete"}}, "summary": {"_context": "Recipe creation progress summary", "progress": "Progress", "completion": "{completed} of {total} completed", "percentComplete": "{percent}% complete"}, "wizard": {"_context": "Recipe creation wizard status and controls", "status": {"loading": "Loading..."}, "buttons": {"retry": "Retry"}}, "validation": {"_context": "Form validation messages for recipe creation process", "healthConcern": "Please describe your health concern", "demographics": "Please complete your demographic information", "causes": "Please select at least one potential cause", "symptoms": "Please select at least one symptom", "healthConcernRequired": "Health concern is required to proceed", "invalidCause": "Invalid cause data. Please refresh and try again.", "invalidSymptom": "Invalid symptom data. Please refresh and try again."}, "streaming": {"_context": "AI analysis status messages during recipe creation", "analyzing": "Analyzing your information...", "analyzingDescription": "We are analyzing your demographics and health concern to identify potential causes and provide personalized recommendations.", "error": "Analysis failed. Please try again.", "completed": "Analysis completed successfully!", "found": "found", "showingLatest": "Showing latest {maxVisible} of {total}", "modal": {"symptoms": {"title": "AI Analysis in Progress", "description": "Analyzing your selected causes to identify potential symptoms"}, "properties": {"title": "AI Analysis in Progress", "description": "Identifying therapeutic properties to address your symptoms"}}, "terminal": {"streaming": "streaming", "potentialCausesAnalysis": "Potential Causes Analysis", "potentialSymptomsAnalysis": "Potential Symptoms Analysis", "therapeuticPropertiesAnalysis": "Therapeutic Properties Analysis", "essentialOilsAnalysis": "Essential Oils Analysis", "causesSubtitle": "Understanding factors that may contribute to your symptoms.", "symptomsSubtitle": "Identifying symptoms that may manifest based on your selected causes.", "propertiesSubtitle": "Finding therapeutic properties to address your symptoms.", "oilsSubtitle": "Recommending essential oils with the identified properties."}, "loading": {"analyzingDemographics": "analyzing demographics...", "analyzingCauses": "analyzing selected causes...", "analyzingSymptoms": "analyzing symptoms...", "analyzingProperties": "analyzing properties..."}, "progress": {"analyzingMoreCauses": "Analyzing more potential causes...", "analyzingMoreSymptoms": "Analyzing more potential symptoms...", "analyzingMoreProperties": "Analyzing more therapeutic properties...", "analyzingMoreOils": "Analyzing more essential oils..."}, "status": {"aiProcessing": "AI is processing your information to find additional insights", "liveAnalysis": "Live analysis in progress", "initializing": "Initializing analysis engine...", "complete": "Analysis complete. Found {count} {type}.", "autoCloseMessage": "This window will close automatically in {seconds} seconds", "analysisCompleteMessage": "Analysis complete. You may now close this window."}, "states": {"loading": "loading", "streaming": "streaming", "completed": "completed", "error": "error"}}, "causesSelection": {"title": "What might be causing your health concern?", "description": "Based on your health concern, here are some potential causes. Select all that might apply to your situation.", "healthConcernLabel": "Your health concern:", "error": {"retry": "Retry", "noPotentialCauses": "Potential causes not found. Please go back to the demographics step to generate them.", "noCauseSelected": "Please select at least one potential cause.", "failedToAnalyze": "Failed to analyze symptoms. Please try again."}, "loading": {"aiAnalyzing": "AI is analyzing your information to identify potential causes...", "loadingCauses": "Loading potential causes...", "mayTakeMoment": "This may take a few moments as we generate personalized recommendations"}, "streaming": {"generating": "Generating potential causes... ({count} found so far)", "reviewWhileAnalyzing": "You can review the causes below while we continue analyzing"}, "selectionCounter": {"label": "Select 1-{count} causes that might apply to you", "selected": "{selected}/{total} selected"}, "tableHeader": "Potential Causes", "emptyState": {"noCauses": "No potential causes found. Please go back and check your health concern."}, "status": {"ready": "Ready to continue"}}, "therapeuticProperties": {"safetyInfo": "Safety Information", "suggestedOils": "Suggested Essential Oils", "loading": {"enriching": "Enriching oils with safety data..."}, "error": {"safety": "An error occurred while fetching safety data."}, "tableHeaders": {"essentialOil": "Essential Oil", "match": "Match", "safetyStatus": "Safety Status", "details": "Details"}, "empty": {"noOils": "No oil suggestions available for this property."}}, "genericStepSelector": {"title": "Select {displayName}", "description": "Based on your previous selections, here are potential {displayName}. Select all that apply to your situation.", "selectionText": "Select {min}-{max} {displayName} that might apply to you", "validationText": "{min}/{max} selected"}, "debug": {"_context": "Debug overlay for recipe creation process - development use only", "sections": {"healthConcern": "Health Concern", "demographics": "Demographics", "causes": "Causes", "symptoms": "Symptoms", "properties": "Properties", "metadata": "<PERSON><PERSON><PERSON>"}, "buttons": {"copyAll": "Copy All Data", "copyMinimal": "Copy Minimal Data", "copySolution1": "Copy Solution 1", "copySolution4": "Copy Solution 4", "copySolution1Inline": "Copy Solution 1 (Inline)", "copied": "Copied!", "close": "Close"}, "showOverlay": "Show Debug Overlay", "labels": {"currentStep": "Current Step", "completedSteps": "Completed Steps", "sessionId": "Session ID", "lastUpdated": "Last Updated", "loading": "Loading", "error": "Error", "streamingStatus": {"causes": "Streaming Causes", "symptoms": "Streaming Symptoms", "properties": "Streaming Properties", "oils": "Streaming Oils"}, "enrichmentStatus": "Property Enrichment Status", "autoAnalyze": "Auto Analyze Properties", "streamingError": "Streaming Error"}}}