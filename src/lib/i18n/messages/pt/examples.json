{"title": "Exemplos de i18n Apenas no Servidor", "description": "Demonstração de padrões de tradução do lado do servidor com Componentes Cliente que recebem traduções como props, eliminando FOUC e incompatibilidades de hidratação.", "examples": {"client-button": {"title": "Botão Cliente com Traduções", "description": "Mostra como Componentes Cliente podem receber traduções como props de Componentes Servidor.", "code-title": "Padrão de Implementação:"}, "locale-switcher": {"title": "<PERSON><PERSON><PERSON>", "description": "Seletor de idioma que funciona sem dependências de roteador ou carregamento de tradução do lado do cliente.", "code-title": "Características Principais:"}, "interactive-form": {"title": "Formulário Interativo com Validação", "description": "Formulário complexo com validação do lado do cliente usando traduções fornecidas pelo servidor.", "code-title": "Padrão de Server Action:"}}, "button": {"text": "Clique em Mim", "loading": "Carregando..."}, "locale-switcher": {"select-language": "Selecionar Idioma", "english": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "portuguese": "Português", "spanish": "Espanhol"}, "form": {"name-label": "Nome <PERSON>to", "name-placeholder": "Digite seu nome completo", "name-required": "Nome é obrigatório", "email-label": "Endereço de Email", "email-placeholder": "Digite seu endereço de email", "email-required": "Email é obrigatório", "email-invalid": "Por favor, digite um endereço de email válido", "message-label": "Mensagem", "message-placeholder": "Digite sua mensagem", "message-required": "Mensagem é obrigatória", "submit-button": "Enviar Mensagem", "submitting": "Enviando...", "submit-error": "Falha ao enviar mensagem. Tente novamente."}, "benefits": {"title": "Benefícios do i18n Apenas no Servidor", "no-fouc": {"title": "Sem FOUC", "description": "Conteúdo aparece instantaneamente no idioma correto"}, "performance": {"title": "Performance", "description": "React cache() elimina carregamento redundante"}, "seo": {"title": "SEO Perfeito", "description": "Motores de busca veem conteúdo totalmente traduzido"}, "hydration": {"title": "Sem Problemas de Hidratação", "description": "Conteúdo do servidor e cliente sempre coincidem"}}}