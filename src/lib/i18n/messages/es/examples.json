{"title": "Ejemplos de i18n Solo en Servidor", "description": "Demostración de patrones de traducción del lado del servidor con Componentes Cliente que reciben traducciones como props, eliminando FOUC e incompatibilidades de hidratación.", "examples": {"client-button": {"title": "Botón Cliente con Traducciones", "description": "Muestra cómo los Componentes Cliente pueden recibir traducciones como props de Componentes Servidor.", "code-title": "Patrón de Implementación:"}, "locale-switcher": {"title": "<PERSON>or <PERSON>", "description": "Selector de idioma que funciona sin dependencias de enrutador o carga de traducción del lado del cliente.", "code-title": "Características Clave:"}, "interactive-form": {"title": "Formulario Interactivo con Validación", "description": "Formulario complejo con validación del lado del cliente usando traducciones proporcionadas por el servidor.", "code-title": "Patrón de Server Action:"}}, "button": {"text": "Haz Clic", "loading": "Cargando..."}, "locale-switcher": {"select-language": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "english": "Inglés", "portuguese": "Portugués", "spanish": "Español"}, "form": {"name-label": "Nombre Completo", "name-placeholder": "Ingresa tu nombre completo", "name-required": "El nombre es obligatorio", "email-label": "Dirección de Email", "email-placeholder": "Ingresa tu dirección de email", "email-required": "El email es obligatorio", "email-invalid": "Por favor, ingresa una dirección de email válida", "message-label": "Men<PERSON><PERSON>", "message-placeholder": "Ingresa tu mensaje", "message-required": "El mensaje es obligatorio", "submit-button": "<PERSON><PERSON><PERSON>", "submitting": "Enviando...", "submit-error": "Error al enviar mensaje. Inténtalo de nuevo."}, "benefits": {"title": "Beneficios del i18n Solo en Servidor", "no-fouc": {"title": "Sin FOUC", "description": "El contenido aparece instantáneamente en el idioma correcto"}, "performance": {"title": "Rendimiento", "description": "React cache() elimina la carga redundante"}, "seo": {"title": "SEO Perfecto", "description": "Los motores de búsqueda ven contenido completamente traducido"}, "hydration": {"title": "Sin Problemas de Hidratación", "description": "El contenido del servidor y cliente siempre coinciden"}}}