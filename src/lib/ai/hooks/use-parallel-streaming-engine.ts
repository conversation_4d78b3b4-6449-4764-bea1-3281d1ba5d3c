import { useState, useCallback } from 'react';
import { StreamingService } from '../services/streaming.service';

export interface ParallelStreamRequest {
  id: string; // Unique identifier for tracking results
  url: string; // API endpoint
  requestData: any; // Request body data
  label?: string; // Optional label for debugging
  responseParser?: (updates: any) => any;
}

export interface ParallelStreamingState<T> {
  isStreaming: boolean;
  completedCount: number;
  totalCount: number;
  results: Map<string, T>;
  errors: Map<string, string>;
}

export interface UseParallelStreamingEngineReturn<T> {
  streamingState: ParallelStreamingState<T>;
  startStreams: (requests: ParallelStreamRequest[]) => Promise<Map<string, T>>;
  resetState: () => void;
}

export function useParallelStreamingEngine<T = any>(): UseParallelStreamingEngineReturn<T> {
  const [streamingState, setStreamingState] = useState<ParallelStreamingState<T>>({
    isStreaming: false,
    completedCount: 0,
    totalCount: 0,
    results: new Map(),
    errors: new Map(),
  });

  const resetState = useCallback(() => {
    setStreamingState({
      isStreaming: false,
      completedCount: 0,
      totalCount: 0,
      results: new Map(),
      errors: new Map(),
    });
  }, []);

  const startStreams = useCallback(async (requests: ParallelStreamRequest[]): Promise<Map<string, T>> => {
    // Create a direct reference to the results map that we'll return
    const finalResults = new Map<string, T>();
    
    setStreamingState({
      isStreaming: true,
      completedCount: 0,
      totalCount: requests.length,
      results: new Map(),
      errors: new Map(),
    });

    const streamingPromises = requests.map(request =>
      (async () => {
        const { id, url, requestData, label, responseParser } = request;
        let streamResult: T | null = null;

        try {
          console.log(`[ParallelEngine] Starting stream for ID: ${id}` + (label ? ` (${label})` : ''));
          console.log(`[ParallelEngine] Request data:`, {
            feature: requestData.feature,
            step: requestData.step,
            dataKeys: Object.keys(requestData.data || {})
          });
          
          const streamingService = new StreamingService();
          let hasProcessedResult = false; // Prevent duplicate processing
          
          await streamingService.stream<T>(url, requestData, (updates) => {
            if (responseParser && updates.finalData && !hasProcessedResult) {
              streamResult = responseParser(updates);
              console.log(`[ParallelEngine] Parsed result for ${id}:`, streamResult ? 'SUCCESS' : 'NO_RESULT');
              hasProcessedResult = true;
            } else if (updates.finalData && !hasProcessedResult) {
              streamResult = updates.finalData;
              console.log(`[ParallelEngine] Direct result for ${id}:`, streamResult ? 'SUCCESS' : 'NO_RESULT');
              hasProcessedResult = true;
            }
            
            if (streamResult && !finalResults.has(id)) {
              // Update both the state and our direct reference (only once)
              finalResults.set(id, streamResult as T);
              
              setStreamingState(prev => ({
                ...prev,
                results: new Map(prev.results).set(id, streamResult as T),
                completedCount: prev.completedCount + 1,
              }));
              
              console.log(`[ParallelEngine] Added result for ${id} to finalResults. Size now: ${finalResults.size}`);
            }
            
            if (updates.error) {
                console.error(`[ParallelEngine] Streaming error for ID ${id}:`, updates.error);
            }
          });
          return { success: true, id, data: streamResult };
        } catch (error) {
          console.error(`[ParallelEngine] Failed to stream for ID ${id}:`, error);
          setStreamingState(prev => ({
            ...prev,
            errors: new Map(prev.errors).set(id, String(error)),
            completedCount: prev.completedCount + 1,
          }));
          return { success: false, id, error };
        }
      })()
    );

    const settledResults = await Promise.allSettled(streamingPromises);
    
    // Log settlement results for debugging
    console.log(`[ParallelEngine] Promise settlement results:`, settledResults.map((result, index) => ({
      index,
      status: result.status,
      id: requests[index].id,
      success: result.status === 'fulfilled' ? (result.value as any)?.success : false
    })));
    
    // Update the streaming state one last time
    setStreamingState(prev => ({ ...prev, isStreaming: false }));

    console.log(`[ParallelEngine] All streams completed. Results: ${finalResults.size}/${requests.length}`);
    console.log(`[ParallelEngine] Final results keys:`, Array.from(finalResults.keys()));
    console.log(`[ParallelEngine] Final results values:`, Array.from(finalResults.values()).map(v => v ? 'SUCCESS' : 'NULL'));
    
    return finalResults;
  }, []);

  return {
    streamingState,
    startStreams,
    resetState,
  };
}
