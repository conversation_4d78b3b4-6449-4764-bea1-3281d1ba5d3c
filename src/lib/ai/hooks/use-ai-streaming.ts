// src/lib/ai/hooks/use-ai-streaming.ts
// Core reusable hook for OpenAI Agents JS SDK streaming with generic type support
// REFACTORED: Now uses StreamingService while maintaining 100% compatibility

import { useState, useEffect, useRef, useCallback } from 'react';
import { StreamingService } from '../services/streaming.service';
import { 
  StreamRequest,
  StreamConfig,
  StreamingState,
  StreamEvent,
  processStreamingText,
  processStructuredData,
  DEFAULT_STREAM_CONFIG
} from '../utils/streaming-utils';

/**
 * Hook state interface - EXACT same API as before
 */
export interface StreamState<T> {
  streamingText: string;
  partialData: T | null;
  isStreaming: boolean;
  isComplete: boolean;
  error: string | null;
  finalData: T | null;
  startStream: (url: string, requestData: StreamRequest) => Promise<void>;
  resetStream: () => void;
}

/**
 * Reusable hook for OpenAI Agents JS SDK streaming
 * REFACTORED: Uses StreamingService but maintains exact same behavior
 * Provides real-time text accumulation, error handling, and SSE connection management
 */
export function useAIStreaming<T = any>(config: StreamConfig = {}): StreamState<T> {
  const mergedConfig = { ...DEFAULT_STREAM_CONFIG, ...config };

  // State management - EXACT same as before
  const [streamingText, setStreamingText] = useState<string>('');
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [isComplete, setIsComplete] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [finalData, setFinalData] = useState<T | null>(null);
  const [partialData, setPartialData] = useState<T | null>(null);

  // Service instance - NEW: Use our enhanced service
  const serviceRef = useRef<StreamingService | null>(null);

  // Initialize service with config
  if (!serviceRef.current) {
    serviceRef.current = new StreamingService(mergedConfig);
  }

  /**
   * Clean up connections and timers - EXACT same logic as before
   */
  const cleanup = useCallback(() => {
    try {
      // Use service's abort method instead of manual cleanup
      if (serviceRef.current) {
        serviceRef.current.abort();
      }
    } catch (cleanupError) {
      // Ignore cleanup errors - they're usually harmless (EXACT replica)
      console.debug('Cleanup error (harmless):', cleanupError);
    }
  }, []);

  /**
   * Reset stream state to initial values - EXACT same as before
   */
  const resetStream = useCallback(() => {
    cleanup();
    setStreamingText('');
    setIsStreaming(false);
    setIsComplete(false);
    setError(null);
    setFinalData(null);
    setPartialData(null);
  }, [cleanup]);

  /**
   * Handle updates from the streaming service - NEW: Processes service callbacks
   * Maintains EXACT same logic as the original handleStreamEvent
   */
  const handleServiceUpdate = useCallback((updates: Partial<StreamingState<T>>) => {
    // Handle text_chunk updates with jsonArrayPath processing - EXACT replica
    if (updates.streamingText !== undefined) {
      setStreamingText(prev => {
        const { updatedText, partialData: newPartialData } = processStreamingText<T>(
          updates.streamingText || '',
          prev,
          mergedConfig.jsonArrayPath
        );

        // Update partial data if jsonArrayPath processing found something
        if (newPartialData !== null) {
          setPartialData(newPartialData);
        }

        return updatedText;
      });
    }

    // Handle structured_data updates - EXACT replica with sophisticated validation
    if (updates.partialData !== undefined) {
      const metadata = (updates as any)._structuredDataMeta;
      
      // SIMPLIFIED: Only log essential info instead of full item details
      console.log('📦 HOOK DEBUG - Received complete structured item:', {
        index: metadata?.index,
        dataType: metadata?.dataType,
        itemKeys: metadata?.itemKeys
      });

      setPartialData(prev => {
        return processStructuredData<T>(
          updates.partialData,
          prev,
          metadata
        );
      });
    }

    // Handle completion states - EXACT same as before
    if (updates.finalData !== undefined) {
      setFinalData(updates.finalData);
    }

    if (updates.isComplete !== undefined) {
      setIsComplete(updates.isComplete);
    }

    if (updates.isStreaming !== undefined) {
      setIsStreaming(updates.isStreaming);
    }

    if (updates.error !== undefined) {
      setError(updates.error);
      setIsStreaming(false);
    }
  }, [mergedConfig.jsonArrayPath]);

  /**
   * Start streaming from the specified URL - EXACT same API as before
   * REFACTORED: Now uses StreamingService instead of manual fetch logic
   */
  const startStream = useCallback(async (url: string, requestData: StreamRequest) => {
    // Prevent starting a new stream if one is already active - EXACT same logic
    if (isStreaming) {
      console.warn('Stream already active, cleaning up before starting new one');
      cleanup();
    }

    // Reset state for new stream - EXACT same as before
    setStreamingText('');
    setIsComplete(false);
    setError(null);
    setFinalData(null);
    setPartialData(null);
    setIsStreaming(true);

    // Clean up any existing connections
    cleanup();

    try {
      console.log('AI streaming initiated successfully');

      // NEW: Use service instead of manual streaming logic
      // The service replicates ALL the original functionality exactly
      await serviceRef.current!.stream<T>(url, requestData, handleServiceUpdate);

    } catch (fetchError) {
      // EXACT same error handling as before
      console.error('Failed to initiate streaming:', fetchError);
      setError(fetchError instanceof Error ? fetchError.message : 'Failed to start streaming');
      setIsStreaming(false);
    }
  }, [isStreaming, cleanup, handleServiceUpdate]);

  // Cleanup on unmount - EXACT same as before
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // EXACT same return interface as before
  return {
    streamingText,
    isStreaming,
    isComplete,
    error,
    finalData,
    partialData,
    startStream,
    resetStream,
  };
}
