/**
 * @fileoverview Unified OpenAI embeddings service for both single and batch requests
 * Consolidates embeddings.service.ts and batch-embeddings.service.ts following DRY principle
 */

import OpenAI from 'openai';
import { validateOpenAIKey } from '../utils/api-helpers';

/**
 * Unified embedding configuration interface
 */
export interface UnifiedEmbeddingConfig {
  model: string;
  dimensions?: number;
}

/**
 * Default embedding configuration
 */
const DEFAULT_CONFIG: UnifiedEmbeddingConfig = {
  model: 'text-embedding-3-small',
  dimensions: 1536
};

/**
 * Single embedding request interface
 */
export interface SingleEmbeddingRequest {
  text: string;
  model?: string;
}

/**
 * Batch embedding request interface
 */
export interface BatchEmbeddingRequest {
  texts: string[];
  model?: string;
}

/**
 * Unified embedding response interface
 */
export interface EmbeddingResponse {
  embedding: number[];
  model: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

/**
 * @class UnifiedEmbeddingsService
 * @description Unified service for creating vector embeddings using the OpenAI API.
 * Handles both single text and batch text requests efficiently.
 */
export class UnifiedEmbeddingsService {
  private client: OpenAI;
  private config: UnifiedEmbeddingConfig;
  private serviceType: 'single' | 'batch';

  constructor(config: Partial<UnifiedEmbeddingConfig> = {}, serviceType: 'single' | 'batch' = 'single') {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.serviceType = serviceType;
    
    // Use the validation helper from api-helpers
    validateOpenAIKey();

    this.client = new OpenAI({
      apiKey: process.env['OPENAI_API_KEY']
    });
    
    // Use service-specific logging prefix
    const logPrefix = this.serviceType === 'single' ? 'EmbeddingsService' : 'BatchEmbeddingsService';
    console.log(`📊 [${logPrefix}] Initialized with model: ${this.config.model}`);
    if (this.config.dimensions) {
      console.log(`📊 [${logPrefix}] Using dimensions: ${this.config.dimensions}`);
    }
  }

  /**
   * Creates a single embedding
   */
  async createSingleEmbedding(request: SingleEmbeddingRequest): Promise<EmbeddingResponse> {
    try {
      const { text, model = this.config.model } = request;
      const logPrefix = 'EmbeddingsService';

      const isDefaultModel = model === this.config.model;
      console.log(`🔍 [${logPrefix}] Creating embedding using model: ${model}${isDefaultModel ? ' (default)' : ' (override)'}`);

      if (!text || text.length === 0) {
        throw new Error('Text input cannot be empty');
      }

      const params: OpenAI.EmbeddingCreateParams = {
        model,
        input: text.trim(),
        encoding_format: 'float',
      };

      if (isDefaultModel && this.config.dimensions) {
        params.dimensions = this.config.dimensions;
      }

      const response = await this.client.embeddings.create(params);

      if (!response.data || response.data.length === 0) {
        throw new Error('No embedding data received from OpenAI');
      }

      const embeddingData = response.data[0];
      if (!embeddingData) {
        throw new Error('No embedding data in response');
      }

      const actualModel = (response as any).model || model;
      console.log(`✅ [${logPrefix}] Embedding created successfully using model: ${actualModel}`);
      
      return {
        embedding: embeddingData.embedding,
        model: actualModel,
        usage: response.usage
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ [EmbeddingsService] Error creating embedding:', errorMessage);
      throw new Error(`Failed to create embedding: ${errorMessage}`);
    }
  }

  /**
   * Creates batch embeddings
   */
  async createBatchEmbeddings(request: BatchEmbeddingRequest): Promise<EmbeddingResponse[]> {
    const startTime = performance.now();
    const traceId = `embed-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`;
    
    try {
      const { texts, model = this.config.model } = request;
      const logPrefix = 'BatchEmbeddingsService';

      console.log(`🚀 [${logPrefix}-${traceId}] ===== BATCH EMBEDDINGS STARTED =====`);
      console.log(`📊 [${logPrefix}-${traceId}] Input validation: ${texts?.length ?? 'undefined'} texts received`);
      console.log(`🤖 [${logPrefix}-${traceId}] Model: ${model} ${model === this.config.model ? '(default)' : '(override)'}`);
      console.log(`⏰ [${logPrefix}-${traceId}] Start time: ${new Date().toISOString()}`);

      if (!texts || !Array.isArray(texts) || texts.length === 0) {
        console.error(`❌ [${logPrefix}-${traceId}] Invalid input: texts must be a non-empty array`);
        throw new Error('Texts input must be a non-empty array');
      }

      // Validate individual texts
      const emptyTexts = texts.filter((text) => !text || text.trim().length === 0);
      if (emptyTexts.length > 0) {
        console.warn(`⚠️  [${logPrefix}-${traceId}] Found ${emptyTexts.length} empty texts that will be trimmed`);
      }

      // Log sample texts for debugging
      console.log(`📝 [${logPrefix}-${traceId}] Sample texts:`, texts.slice(0, 3).map((text, i) => `${i + 1}. "${text?.substring(0, 50)}..."`));

      const params: OpenAI.EmbeddingCreateParams = {
        model,
        input: texts.map(text => text?.trim() || ''),
        encoding_format: 'float',
      };

      if (model === this.config.model && this.config.dimensions) {
        params.dimensions = this.config.dimensions;
        console.log(`🔧 [${logPrefix}-${traceId}] Using custom dimensions: ${this.config.dimensions}`);
      }

      console.log(`📡 [${logPrefix}-${traceId}] Calling OpenAI embeddings API...`);
      const apiStartTime = performance.now();
      
      const response = await this.client.embeddings.create(params);
      
      const apiTime = performance.now() - apiStartTime;
      console.log(`✅ [${logPrefix}-${traceId}] OpenAI API call completed in ${Math.round(apiTime)}ms`);

      if (!response.data || response.data.length === 0) {
        console.error(`❌ [${logPrefix}-${traceId}] No embedding data received from OpenAI`);
        throw new Error('No embedding data received from OpenAI');
      }

      console.log(`📊 [${logPrefix}-${traceId}] Response validation: ${response.data.length} embeddings received`);
      console.log(`💰 [${logPrefix}-${traceId}] Token usage:`, {
        prompt_tokens: response.usage.prompt_tokens,
        total_tokens: response.usage.total_tokens
      });

      const actualModel = (response as any).model || model;
      const totalTime = performance.now() - startTime;
      
      console.log(`🎯 [${logPrefix}-${traceId}] ===== BATCH EMBEDDINGS COMPLETED =====`);
      console.log(`✅ [${logPrefix}-${traceId}] Success: ${response.data.length} embeddings created using model: ${actualModel}`);
      console.log(`⏱️  [${logPrefix}-${traceId}] Total time: ${Math.round(totalTime)}ms (API: ${Math.round(apiTime)}ms)`);
      
      return response.data.map(embeddingData => ({
        embedding: embeddingData.embedding,
        model: actualModel,
        usage: response.usage 
      }));

    } catch (error) {
      const totalTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
      
      console.error(`💥 [BatchEmbeddingsService-${traceId}] ===== BATCH EMBEDDINGS FAILED =====`);
      console.error(`❌ [BatchEmbeddingsService-${traceId}] Error message: ${errorMessage}`);
      console.error(`❌ [BatchEmbeddingsService-${traceId}] Error type: ${error instanceof Error ? error.constructor.name : typeof error}`);
      console.error(`❌ [BatchEmbeddingsService-${traceId}] Stack trace:`, errorStack);
      console.error(`⏱️  [BatchEmbeddingsService-${traceId}] Failed after: ${Math.round(totalTime)}ms`);
      
      throw new Error(`Failed to create embeddings: ${errorMessage}`);
    }
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const logPrefix = this.serviceType === 'single' ? 'EmbeddingsService' : 'BatchEmbeddingsService';
      if (this.serviceType === 'single') {
        await this.createSingleEmbedding({
          text: 'test',
          model: this.config.model
        });
      } else {
        await this.createBatchEmbeddings({
          texts: ['test'],
          model: this.config.model
        });
      }
      console.log(`✅ [${logPrefix}] Health check passed`);
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`❌ [${this.serviceType === 'single' ? 'EmbeddingsService' : 'BatchEmbeddingsService'}] Health check failed:`, errorMessage);
      return false;
    }
  }
}

// Singleton instances
let singletonSingleService: UnifiedEmbeddingsService | null = null;
let singletonBatchService: UnifiedEmbeddingsService | null = null;

/**
 * Gets a singleton instance of the embeddings service for single requests
 */
export function getEmbeddingsService(config?: Partial<UnifiedEmbeddingConfig>): UnifiedEmbeddingsService {
  if (!singletonSingleService) {
    singletonSingleService = new UnifiedEmbeddingsService(config, 'single');
  }
  return singletonSingleService;
}

/**
 * Gets a singleton instance of the embeddings service for batch requests
 */
export function getBatchEmbeddingsService(config?: Partial<UnifiedEmbeddingConfig>): UnifiedEmbeddingsService {
  if (!singletonBatchService) {
    singletonBatchService = new UnifiedEmbeddingsService(config, 'batch');
  }
  return singletonBatchService;
}

// Type aliases for backward compatibility
export type OpenAIEmbeddingsService = UnifiedEmbeddingsService;
export type OpenAIBatchEmbeddingsService = UnifiedEmbeddingsService; 