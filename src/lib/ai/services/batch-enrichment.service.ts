/**
 * @fileoverview Batch oil enrichment service for efficient safety data retrieval
 * Replaces individual AI agent calls with batch processing for better performance
 */

import { createClient } from '@/lib/supabase/server';
import { getBatchEmbeddingsService } from './unified-embeddings.service';

/**
 * Input oil data from AI agent (suggested-oils.yaml output)
 */
export interface SuggestedOilData {
  oil_id: string;
  name_english: string;
  name_botanical: string;
  name_localized: string;
  match_rationale_localized: string;
  relevancy_to_property_score: number;
  isEnriched?: boolean;
}

/**
 * Safety data structure with IDs
 */
export interface OilSafetyData {
  internal_use: any;
  dilution: any;
  phototoxicity: any;
  pregnancy_nursing: any;
  child_safety: any;
  // Safety IDs from Supabase
  internal_use_id?: string;
  dilution_id?: string;
  phototoxicity_id?: string;
  pregnancy_nursing_ids?: string[];
  child_safety_ids?: string[];
}

/**
 * Enriched oil data with safety information from vector search
 */
export interface EnrichedOilData extends SuggestedOilData {
  // Safety data from vector search
  name_scientific?: string;
  safety?: OilSafetyData;

  // Enrichment metadata
  enrichment_status: 'enriched' | 'not_found' | 'discarded';
  botanical_mismatch?: boolean;
  similarity_score?: number;
  search_query?: string;
  enrichment_timestamp?: string;
  isEnriched: boolean;
}

/**
 * Batch enrichment response
 */
export interface BatchEnrichmentResponse {
  enriched_oils: EnrichedOilData[];
  total_input: number;
  total_enriched: number;
  total_not_found: number;
  total_discarded: number;
  processing_time_ms: number;
}

/**
 * Batch oil enrichment service
 * @class BatchEnrichmentService
 */
export class BatchEnrichmentService {
  private batchEmbeddingService: ReturnType<typeof getBatchEmbeddingsService>;

  constructor() {
    this.batchEmbeddingService = getBatchEmbeddingsService();
    console.log('🔧 [BatchEnrichmentService] Initialized with BATCH embeddings service');
  }

  /**
   * Enriches a batch of suggested oils with safety data from vector search
   * @param suggestedOils Array of oils from AI agent output
   * @returns Promise<BatchEnrichmentResponse>
   */
  async batchEnrichOils(suggestedOils: SuggestedOilData[]): Promise<BatchEnrichmentResponse> {
    const startTime = performance.now();
    const traceId = `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    console.log(`🚀 [${traceId}] ===== BATCH ENRICHMENT STARTED =====`);
    console.log(`📊 [${traceId}] Input validation: ${suggestedOils.length} oils received`);
    console.log(`🔍 [${traceId}] Environment: ${process.env.NODE_ENV || 'unknown'}`);
    console.log(`⏰ [${traceId}] Start time: ${new Date().toISOString()}`);

    if (suggestedOils.length === 0) {
      console.log(`⚠️  [${traceId}] Early return: No oils to process`);
      return {
        enriched_oils: [],
        total_input: 0,
        total_enriched: 0,
        total_not_found: 0,
        total_discarded: 0,
        processing_time_ms: performance.now() - startTime
      };
    }

    // Validate input data structure
    const invalidOils = suggestedOils.filter(oil => !oil?.name_english || !oil?.name_botanical);
    if (invalidOils.length > 0) {
      console.error(`❌ [${traceId}] Invalid oil data detected: ${invalidOils.length} oils missing required fields`);
      console.error(`❌ [${traceId}] Invalid oils:`, invalidOils);
    }

    try {
      // Step 1: Format search queries - ${name_english} - ${name_botanical}
      console.log(`🔧 [${traceId}] STEP 1: Formatting search queries...`);
      const searchQueries = suggestedOils.map((oil, index) => {
        const query = `${oil.name_english} - ${oil.name_botanical}`.trim();
        console.log(`   ${index + 1}. "${query}"`);
        return query;
      });

      console.log(`✅ [${traceId}] Search queries formatted: ${searchQueries.length} queries ready`);

      // Step 2: Generate batch embeddings using a single API call
      console.log(`🔧 [${traceId}] STEP 2: Creating batch embeddings...`);
      const embeddingStartTime = performance.now();

      const embeddingsResults = await this.batchEmbeddingService.createBatchEmbeddings({
        texts: searchQueries
      });

      const embeddingTime = performance.now() - embeddingStartTime;
      console.log(`✅ [${traceId}] Embeddings created: ${embeddingsResults.length} embeddings in ${Math.round(embeddingTime)}ms`);

      // Validate embeddings
      const invalidEmbeddings = embeddingsResults.filter(result => !result.embedding || result.embedding.length === 0);
      if (invalidEmbeddings.length > 0) {
        console.error(`❌ [${traceId}] Invalid embeddings detected: ${invalidEmbeddings.length} empty embeddings`);
      }

      // Step 3: Perform batch vector search
      console.log(`🔧 [${traceId}] STEP 3: Performing batch vector search...`);
      const vectorSearchStartTime = performance.now();

      const supabase = await createClient();
      console.log(`🔗 [${traceId}] Supabase client created successfully`);

      // Convert embeddings to JSON strings for vector[] type
      const embeddings = embeddingsResults.map((result, index) => {
        const jsonString = JSON.stringify(result.embedding);
        console.log(`   Embedding ${index + 1}: ${jsonString.length} characters`);
        return jsonString;
      });

      console.log(`📡 [${traceId}] Calling batch_similarity_search_with_ids RPC...`);
      console.log(`   - Embeddings: ${embeddings.length}`);
      console.log(`   - Match threshold: 0.5`);
      console.log(`   - Match count: 3`);

      const { data: searchResults, error } = await supabase.rpc('batch_similarity_search_with_ids', {
        query_embeddings: embeddings,
        match_threshold: 0.5,
        match_count: 3,
      });

      const vectorSearchTime = performance.now() - vectorSearchStartTime;

      if (error) {
        console.error(`❌ [${traceId}] Supabase RPC error:`, error);
        console.error(`❌ [${traceId}] Error details:`, {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw new Error(`Vector search failed - database error: ${error.message}`);
      }

      console.log(`✅ [${traceId}] Vector search completed: ${searchResults?.length || 0} results in ${Math.round(vectorSearchTime)}ms`);

      if (searchResults && searchResults.length > 0) {
        console.log(`📊 [${traceId}] Sample search result:`, searchResults[0]);
      }

      // Step 4: Process results and apply botanical validation
      console.log(`🔧 [${traceId}] STEP 4: Processing search results and applying validation...`);
      const processingStartTime = performance.now();

      const enrichedOils = await this.processSearchResults(suggestedOils, searchQueries, searchResults || [], traceId);

      const processingTime = performance.now() - processingStartTime;
      const endTime = performance.now();
      const totalTime = Math.round(endTime - startTime);
      const stats = this.calculateStats(enrichedOils);

      console.log(`✅ [${traceId}] Processing completed in ${Math.round(processingTime)}ms`);
      console.log(`🎯 [${traceId}] ===== BATCH ENRICHMENT COMPLETED =====`);
      console.log(`📊 [${traceId}] Final statistics:`, {
        total_input: suggestedOils.length,
        total_enriched: stats.total_enriched,
        total_not_found: stats.total_not_found,
        total_discarded: stats.total_discarded,
        success_rate: `${((stats.total_enriched / suggestedOils.length) * 100).toFixed(1)}%`,
        processing_time_ms: totalTime
      });

      return {
        enriched_oils: enrichedOils,
        total_input: suggestedOils.length,
        ...stats,
        processing_time_ms: totalTime
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';

      console.error(`💥 [${traceId}] ===== BATCH ENRICHMENT FAILED =====`);
      console.error(`❌ [${traceId}] Error message: ${errorMessage}`);
      console.error(`❌ [${traceId}] Error type: ${error instanceof Error ? error.constructor.name : typeof error}`);
      console.error(`❌ [${traceId}] Stack trace:`, errorStack);
      console.error(`⏰ [${traceId}] Failed after: ${Math.round(performance.now() - startTime)}ms`);

      // Throw descriptive errors that will be handled by the API route's error handling
      throw new Error(`Oil enrichment failed: ${errorMessage}`);
    }
  }

  /**
   * Process search results and apply botanical name validation
   */
  private async processSearchResults(
    suggestedOils: SuggestedOilData[],
    searchQueries: string[],
    searchResults: any[],
    traceId: string
  ): Promise<EnrichedOilData[]> {
    console.log(`🔍 [${traceId}] Processing ${suggestedOils.length} oils with ${searchResults.length} search results`);
    const enrichedOils: EnrichedOilData[] = [];
    let processedCount = 0;

    // Group results by embedding_index
    const resultsByIndex = searchResults.reduce((acc: any, result: any) => {
      const index = result.embedding_index;
      if (!acc[index]) acc[index] = [];
      acc[index].push(result);
      return acc;
    }, {});

    // Process each oil
    for (let i = 0; i < suggestedOils.length; i++) {
      const oil = suggestedOils[i];
      const embeddingIndex = i + 1; // Supabase function uses 1-based indexing
      const results = resultsByIndex[embeddingIndex] || [];
      const searchQuery = searchQueries[i];
      processedCount++;

      console.log(`🔄 [${traceId}] Processing oil ${processedCount}/${suggestedOils.length}: "${oil?.name_english}"`);

      // Linter fix: ensure oil object is valid before proceeding
      if (!oil || !oil.name_english || !oil.name_botanical) {
        console.warn(`⚠️  [${traceId}] Skipping invalid oil data at index ${i}:`, oil);
        continue;
      }

      console.log(`   📋 [${traceId}] Oil details: ${oil.name_english} - ${oil.name_botanical}`);
      console.log(`   🔍 [${traceId}] Search results found: ${results.length}`);
      console.log(`   📊 [${traceId}] Embedding index: ${embeddingIndex}`);

      if (results.length === 0) {
        // No results found
        console.log(`🟡 [${traceId}] NOT FOUND: "${oil.name_english}" - No vector search results for query: "${searchQuery}"`);
        const enrichedOil: EnrichedOilData = {
          ...oil,
          enrichment_status: 'not_found',
          search_query: searchQuery,
          enrichment_timestamp: new Date().toISOString(),
          isEnriched: false
        };
        enrichedOils.push(enrichedOil);
        continue;
      }

      // Get best match (first result, already sorted by similarity)
      const bestMatch = results[0];
      const similarity = bestMatch.similarity;

      console.log(`   🎯 [${traceId}] Best match: "${bestMatch.name_scientific}" (similarity: ${similarity.toFixed(3)})`);
      console.log(`   📊 [${traceId}] All matches:`, results.map((r: any) => `${r.name_scientific} (${r.similarity.toFixed(3)})`));

      // Check if botanical name matches
      const botanicalMismatch = this.checkBotanicalMismatch(oil.name_botanical, bestMatch.name_scientific);
      console.log(`   🔬 [${traceId}] Botanical comparison: "${oil.name_botanical}" vs "${bestMatch.name_scientific}" - Mismatch: ${botanicalMismatch}`);

      // Discard if similarity is too low and botanical name doesn't match
      if (similarity < 0.65 && botanicalMismatch) {
        console.log(`🔴 [${traceId}] DISCARDED: "${oil.name_english}" - Low similarity (${similarity.toFixed(3)}) and botanical mismatch`);
        console.log(`   ❌ [${traceId}] Discard reasons: similarity=${similarity.toFixed(3)} < 0.65 AND botanical_mismatch=${botanicalMismatch}`);
        const enrichedOil: EnrichedOilData = {
          ...oil,
          enrichment_status: 'discarded',
          botanical_mismatch: true,
          similarity_score: similarity,
          search_query: searchQuery,
          enrichment_timestamp: new Date().toISOString(),
          isEnriched: false
        };
        enrichedOils.push(enrichedOil);
        continue;
      }

      // Create enriched oil with safety data properly structured
      const safety: OilSafetyData = {
        internal_use: bestMatch.internal_use,
        dilution: bestMatch.dilution,
        phototoxicity: bestMatch.phototoxicity,
        pregnancy_nursing: bestMatch.pregnancy_nursing_safety,
        child_safety: bestMatch.child_safety
      };

      // Extract safety IDs
      safety.internal_use_id = bestMatch.internal_use_status_id;
      safety.dilution_id = bestMatch.dilution_recommendation_id;
      safety.phototoxicity_id = bestMatch.phototoxicity_status_id;

      // Extract pregnancy nursing IDs from the JSON array
      if (Array.isArray(bestMatch.pregnancy_nursing_safety)) {
        safety.pregnancy_nursing_ids = bestMatch.pregnancy_nursing_safety.map((item: any) => item.id).filter(Boolean);
      }

      // Extract child safety IDs from the JSON array
      if (Array.isArray(bestMatch.child_safety)) {
        safety.child_safety_ids = bestMatch.child_safety.map((item: any) => item.age_range_id).filter(Boolean);
      }

      // Check if safety data is actually present (not just empty fields)
      const hasSafetyData = Object.values(safety).some(value => value !== null && value !== undefined && value !== '');

      // For enriched/valid oils, set oil_id to the official Supabase ID
      const enrichedOil: EnrichedOilData = {
        ...oil,
        oil_id: bestMatch.id, // <-- Use the official Supabase oil ID
        name_scientific: bestMatch.name_scientific,
        safety: hasSafetyData ? safety : undefined,
        enrichment_status: hasSafetyData ? 'enriched' : 'not_found',
        botanical_mismatch: botanicalMismatch,
        similarity_score: similarity,
        search_query: searchQuery,
        enrichment_timestamp: new Date().toISOString(),
        isEnriched: hasSafetyData
      };

      if (hasSafetyData) {
        console.log(`✅ [${traceId}] ENRICHED: "${oil.name_english}" - Similarity: ${similarity.toFixed(3)}, Safety data present`);
        console.log(`   🛡️  [${traceId}] Safety data keys:`, Object.keys(safety).filter(key => safety[key as keyof OilSafetyData] !== null && safety[key as keyof OilSafetyData] !== undefined));
      } else {
        console.log(`🟡 [${traceId}] PARTIAL: "${oil.name_english}" - Found match but no safety data`);
        console.log(`   ⚠️  [${traceId}] Missing safety data for: ${bestMatch.name_scientific}`);
      }
      enrichedOils.push(enrichedOil);
    }

    console.log(`🏁 [${traceId}] Processing completed: ${enrichedOils.length} oils processed`);
    return enrichedOils;
  }

  /**
   * Calculate enrichment statistics
   */
  private calculateStats(enrichedOils: EnrichedOilData[]) {
    return {
      total_enriched: enrichedOils.filter(o => o.enrichment_status === 'enriched').length,
      total_not_found: enrichedOils.filter(o => o.enrichment_status === 'not_found').length,
      total_discarded: enrichedOils.filter(o => o.enrichment_status === 'discarded').length,
    };
  }

  private checkBotanicalMismatch(searchedBotanical: string, foundBotanical: string | undefined): boolean {
    if (!foundBotanical) return true; // If no scientific name is found, assume mismatch
    const trimmedSearched = searchedBotanical.trim().toLowerCase();
    const trimmedFound = foundBotanical.trim().toLowerCase();
    return trimmedSearched !== trimmedFound;
  }
}

/**
 * Singleton instance of the BatchEnrichmentService
 */
let serviceInstance: BatchEnrichmentService | null = null;

export function getBatchEnrichmentService(): BatchEnrichmentService {
  if (!serviceInstance) {
    serviceInstance = new BatchEnrichmentService();
  }
  return serviceInstance;
}

export async function batchEnrichOils(suggestedOils: SuggestedOilData[]): Promise<BatchEnrichmentResponse> {
  const service = getBatchEnrichmentService();
  return service.batchEnrichOils(suggestedOils);
}