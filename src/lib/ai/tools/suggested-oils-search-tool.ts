/**
 * @fileoverview OpenAI Agents JS tools for suggested oils search in create-recipe workflow
 *
 * This module contains the AI tools that agents use to search for essential oils
 * based on therapeutic properties. It uses the generic Pinecone service for
 * vector search operations while handling domain-specific logic.
 *
 * Updated: Fixed OpenAI Structured Outputs compatibility with nullable optional fields.
 */

import { tool } from '@openai/agents';
import { z } from 'zod';
import { performVectorSearch, checkVectorSearchHealth } from '@/lib/pinecone/pinecone.service';


/**
 * Vector search tool parameters schema for essential oils
 */
const SuggestedOilsSearchParams = z.object({
  therapeutic_property: z.string().describe('The therapeutic property to search for (e.g., "Anti-inflammatory", "Relaxante Muscular")'),
  health_concern: z.string().describe('The health concern context (e.g., "dor de cabeça", "back pain")'),
  additional_context: z.string().nullable().optional().describe('Additional context like symptoms, causes, or combined search terms. Can be structured text with symptoms/causes information for more sophisticated queries.'),
  max_results: z.number().min(1).max(20).default(10).describe('Maximum number of oils to return')
});

/**
 * Search for essential oils using domain-specific logic and generic vector search
 * This function handles the create-recipe workflow specific requirements while
 * delegating the actual vector search to the generic Pinecone service.
 */
async function searchSuggestedOils(
  therapeutic_property: string,
  health_concern: string,
  additional_context: string | null | undefined,
  max_results: number
): Promise<string> {
  try {
    // Build the search query based on what context is provided
    let queryText: string;
    
    if (additional_context && additional_context.trim()) {
      // If additional context is provided, create a more sophisticated query
      queryText = `óleos essenciais ${therapeutic_property} para ${health_concern} ${additional_context}`;
      console.log(`🔍 [searchSuggestedOils] Using enhanced query with context: "${queryText}"`);
    } else {
      // Create a basic query directly without query builder
      queryText = `óleos essenciais ${therapeutic_property} para ${health_concern}`;
      console.log(`🔍 [searchSuggestedOils] Using basic query: "${queryText}"`);
    }

    // Always use text-embedding-ada-002 for suggested oils
    const forcedEmbeddingModel = 'text-embedding-ada-002';
    console.log(`🔍 [searchSuggestedOils] Using embedding model: ${forcedEmbeddingModel}`);
    
    // Perform the vector search using the generic Pinecone service
    const searchResult = await performVectorSearch({
      queryText,
      maxResults: max_results,
      embeddingModel: forcedEmbeddingModel  // Always use text-embedding-ada-002 for suggested oils
    });

    // Format the result for the create-recipe workflow's specific output format
    return JSON.stringify({
      search_query: searchResult.queryUsedForEmbedding,
      therapeutic_property,
      health_concern,
      additional_context: additional_context || null,
      results: searchResult.results.map(result => ({
        text: result.text,
        score: result.score
      })),
      total_results: searchResult.totalResults,
      source: searchResult.source,
      embedding_model_used: searchResult.embeddingModelUsed
    }, null, 2);

  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    console.error('❌ searchSuggestedOils failed:', message);
    throw new Error(`Suggested oils search failed: ${message}`);
  }
}

/**
 * OpenAI Agents JS tool for essential oils recommendation in create-recipe workflow
 *
 * This tool performs semantic similarity search using:
 * 1. OpenAI embeddings to convert search query to vector
 * 2. Pinecone vector database to find similar essential oils
 * 3. Returns ranked list of oils with relevance scores
 */
export const suggestedOilsSearchTool = tool({
  name: 'get_recommended_essential_oils',
  description: `Search for singular essential oils that possess specific therapeutic properties using vector similarity search.
  
  This tool finds oils that are semantically similar to the given therapeutic property and health concern.
  Use this tool multiple times with different query variations to get comprehensive results.
  
  IMPORTANT: Use the additional_context parameter to pass complex search terms that combine symptoms, causes, or other relevant information.
  
  Examples:
  - Basic: therapeutic_property="Anti-inflammatory", health_concern="headache"
  - With symptoms: therapeutic_property="Anti-inflammatory", health_concern="dor de cabeça", additional_context="com sintomas tensão muscular, stress"
  - With causes: therapeutic_property="Anti-inflammatory", health_concern="dor de cabeça", additional_context="com causas falta de sono, trabalho excessivo"
  - Combined: therapeutic_property="Anti-inflammatory", health_concern="dor de cabeça", additional_context="para tensão muscular, stress e prevenção de falta de sono"`,
  
  parameters: SuggestedOilsSearchParams,
  
  execute: async (args, _context) => {
    try {
      const { therapeutic_property, health_concern, additional_context, max_results } = args;

      console.log(`🔍 [SUGGESTED OILS SEARCH TOOL] Vector search: ${therapeutic_property} for ${health_concern}`);
      console.log(`🔍 [SUGGESTED OILS SEARCH TOOL] Using embedding model: text-embedding-ada-002`);
      console.log(`🔍 [SUGGESTED OILS SEARCH TOOL] Args:`, JSON.stringify(args, null, 2));

      // Use the domain-specific search function
      return await searchSuggestedOils(therapeutic_property, health_concern, additional_context, max_results);

    } catch (error) {
      console.error('❌ Suggested oils search error:', error);

      return JSON.stringify({
        error: true,
        message: `Suggested oils search failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        therapeutic_property: args.therapeutic_property,
        health_concern: args.health_concern
      }, null, 2);
    }
  },

  errorFunction: (_context, error) => {
    console.error('🚨 Suggested oils search tool execution failed:', error);
    return `Suggested oils search tool encountered an error: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again with different search parameters.`;
  }
});

/**
 * Health check tool for vector search infrastructure used by suggested oils feature
 */
export const suggestedOilsHealthCheckTool = tool({
  name: 'check_vector_search_health',
  description: 'Check if vector search infrastructure (Pinecone + OpenAI embeddings) is working correctly for suggested oils feature',

  parameters: z.object({}),

  execute: async (_args, _context) => {
    try {
      console.log('🏥 Checking vector search health for suggested oils...');
      
      const healthResult = await checkVectorSearchHealth();
      return JSON.stringify(healthResult, null, 2);

    } catch (error) {
      return JSON.stringify({
        overall_status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }, null, 2);
    }
  }
});

/**
 * Export all suggested oils search tools for easy import by agents
 */
export const suggestedOilsSearchTools = [
  suggestedOilsSearchTool,
  suggestedOilsHealthCheckTool
];

// Legacy exports for backward compatibility (can be removed in future versions)
export const vectorSearchTool = suggestedOilsSearchTool;
export const vectorSearchHealthCheckTool = suggestedOilsHealthCheckTool;
export const vectorSearchTools = suggestedOilsSearchTools;
