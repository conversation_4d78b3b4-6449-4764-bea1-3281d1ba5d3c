import "server-only";

import type { Locale } from "../../i18n-config";

// This is a simple dictionary type - you can expand this
type Dictionary = {
  [key: string]: any;
}

const dictionaries: Record<Locale, () => Promise<Dictionary>> = {
  en: () => import('../lib/i18n/messages/en/common.json').then((module) => module.default),
  pt: () => import('../lib/i18n/messages/pt/common.json').then((module) => module.default),
  es: () => import('../lib/i18n/messages/es/common.json').then((module) => module.default),
};

export const getDictionary = async (locale: Locale): Promise<Dictionary> => {
  return dictionaries[locale]?.() ?? dictionaries.en();
};

// Simple utility to get translations for a specific namespace
export const getTranslations = async (locale: Locale, namespace?: string): Promise<Dictionary> => {
  try {
    if (namespace) {
      // Load specific namespace
      const translations = await import(`../lib/i18n/messages/${locale}/${namespace}.json`);
      return translations.default;
    }
    
    // Load common translations
    return await getDictionary(locale);
  } catch (error) {
    console.warn(`Failed to load translations for ${locale}/${namespace || 'common'}:`, error);
    // Fallback to English
    if (locale !== 'en') {
      return getTranslations('en', namespace);
    }
    return {};
  }
};
