/**
 * @fileoverview ok_to_future_delete - Temporary debugging overlay for recipe wizard data
 * This component displays all accumulated data from the recipe wizard for debugging purposes.
 * The entire component and its usage should be removed when no longer needed.
 */

'use client';

import React from 'react';
import { X, ChevronDown, ChevronRight, Copy } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useRecipeStore } from '../store/recipe-store';
import { useApiLanguage } from '@/lib/i18n/utils';
import { enrichOilsForAI, filterPropertiesForAI } from '../utils/oil-data-enrichment';
import { TherapeuticProperty } from '../types/recipe.types';
import { type ParallelStreamingState } from '@/lib/ai/hooks/use-parallel-streaming-engine';

interface RecipeDebugOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  properties?: TherapeuticProperty[];
  streamingState?: ParallelStreamingState<any>;
}

/**
 * ok_to_future_delete - Debug overlay component that shows all recipe wizard data
 */
export function RecipeDebugOverlay({ isOpen, onClose }: RecipeDebugOverlayProps) {
  const [expandedSections, setExpandedSections] = React.useState<Record<string, boolean>>({
    healthConcern: true,
    demographics: true,
    causes: true,
    symptoms: true,
    properties: true,
    metadata: false
  });

  const [copied, setCopied] = React.useState(false);
  const [copiedMinimal, setCopiedMinimal] = React.useState(false);

  // ===================== ok_to_future_delete - Solution 1 & 4 clipboard buttons =====================
  const [copiedSolution1, setCopiedSolution1] = React.useState(false);
  const [copiedSolution4, setCopiedSolution4] = React.useState(false);
  const [copiedSolution1Inline, setCopiedSolution1Inline] = React.useState(false);
  // ===================== end ok_to_future_delete - Solution 1 & 4 clipboard buttons =====================

  // Get all data from the recipe store
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    therapeuticProperties,
    suggestedOils,
    potentialCauses,
    potentialSymptoms,
    currentStep,
    completedSteps,
    isLoading,
    error,
    isStreamingCauses,
    isStreamingSymptoms,
    isStreamingProperties,
    isStreamingOils,
    streamingError,
    propertyEnrichmentStatus,
    shouldAutoAnalyzeProperties,
    lastUpdated,
    sessionId
  } = useRecipeStore();

  const apiLanguage = useApiLanguage();

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Use shared utility functions for filtering and enrichment (DRY principle)
  const filteredProperties = filterPropertiesForAI(therapeuticProperties);
  const enrichedOils = enrichOilsForAI(filteredProperties);

  // Build the final minimal data with enriched scores
  const minimalData = {
    language: apiLanguage,
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    oils: enrichedOils,
    therapeuticProperties: therapeuticProperties.map(prop => ({
      property_id: prop.property_id,
      property_name_localized: prop.property_name_localized,
      property_name_english: prop.property_name_english,
      description_contextual_localized: prop.description_contextual_localized,
      addresses_cause_ids: prop.addresses_cause_ids,
      addresses_symptom_ids: prop.addresses_symptom_ids,
      relevancy_score: prop.relevancy_score
    }))
  };

  // ===================== ok_to_future_delete - Solution 1 & 4 clipboard buttons =====================
  function generateSolution1Data() {
    // Flattened, abbreviated keys
    return {
      lang: apiLanguage,
      health: healthConcern,
      demo: demographics,
      causes: selectedCauses,
      symptoms: selectedSymptoms,
      props: therapeuticProperties.map((prop: any) => ({
        id: prop.property_id,
        name: prop.property_name_localized,
        en: prop.property_name_english,
        desc: prop.description_contextual_localized,
        cause_ids: prop.addresses_cause_ids,
        symptom_ids: prop.addresses_symptom_ids,
        score: prop.relevancy_score
      })),
      oils: enrichedOils.map((oil: any) => ({
        id: oil.oil_id,
        name: oil.name_localized,
        en: oil.name_english,
        sci: oil.name_botanical,
        props: (oil.properties || []).map((p: any) => ({
          pid: p.property_id,
          score: p.relevancy_to_property_score,
          inst: p.recommendation_instance_score,
          rationale: p.match_rationale_localized
        })),
        final: oil.final_relevance_score,
        safety: oil.safety
      }))
    };
  }

  function generateSolution4Data() {
    // Fully relational structure
    const oilsObj: Record<string, any> = {};
    const matchesObj: Record<string, any> = {};
    enrichedOils.forEach((oil: any) => {
      oilsObj[oil.oil_id] = {
        name: oil.name_localized,
        en: oil.name_english,
        sci: oil.name_botanical,
        final: oil.final_relevance_score,
        safety: oil.safety
      };
      if (oil.properties && oil.properties.length > 0) {
        matchesObj[oil.oil_id] = {};
        oil.properties.forEach((p: any) => {
          matchesObj[oil.oil_id][p.property_id] = {
            score: p.relevancy_to_property_score,
            inst: p.recommendation_instance_score,
            rationale: p.match_rationale_localized
          };
        });
      }
    });
    const propsObj: Record<string, any> = {};
    therapeuticProperties.forEach((prop: any) => {
      propsObj[prop.property_id] = {
        name: prop.property_name_localized,
        en: prop.property_name_english,
        desc: prop.description_contextual_localized,
        cause_ids: prop.addresses_cause_ids,
        symptom_ids: prop.addresses_symptom_ids,
        score: prop.relevancy_score
      };
    });
    return {
      lang: apiLanguage,
      health: healthConcern,
      demo: demographics,
      causes: selectedCauses,
      symptoms: selectedSymptoms,
      props: propsObj,
      oils: oilsObj,
      matches: matchesObj
    };
  }

  async function handleCopySolution1() {
    try {
      await navigator.clipboard.writeText(JSON.stringify(generateSolution1Data(), null, 2));
      setCopiedSolution1(true);
      setTimeout(() => setCopiedSolution1(false), 1500);
    } catch (e) {
      setCopiedSolution1(false);
    }
  }

  async function handleCopySolution4() {
    try {
      await navigator.clipboard.writeText(JSON.stringify(generateSolution4Data(), null, 2));
      setCopiedSolution4(true);
      setTimeout(() => setCopiedSolution4(false), 1500);
    } catch (e) {
      setCopiedSolution4(false);
    }
  }

  // Solution 1 (inline, single-line JSON)
  function generateSolution1InlineData() {
    // Use the same structure as generateSolution1Data
    return generateSolution1Data();
  }

  async function handleCopySolution1Inline() {
    try {
      await navigator.clipboard.writeText(JSON.stringify(generateSolution1InlineData()));
      setCopiedSolution1Inline(true);
      setTimeout(() => setCopiedSolution1Inline(false), 1500);
    } catch (e) {
      setCopiedSolution1Inline(false);
    }
  }
  // ===================== end ok_to_future_delete - Solution 1 & 4 clipboard buttons =====================

  const handleCopyMinimal = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(minimalData, null, 2));
      setCopiedMinimal(true);
      setTimeout(() => setCopiedMinimal(false), 1500);
    } catch (e) {
      setCopiedMinimal(false);
    }
  };

  const handleCopy = async () => {
    try {
      const debugData = {
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        therapeuticProperties,
        suggestedOils,
        potentialCauses,
        potentialSymptoms,
        currentStep,
        completedSteps,
        isLoading,
        error,
        isStreamingCauses,
        isStreamingSymptoms,
        isStreamingProperties,
        isStreamingOils,
        streamingError,
        propertyEnrichmentStatus,
        shouldAutoAnalyzeProperties,
        lastUpdated,
        sessionId
      };
      await navigator.clipboard.writeText(JSON.stringify(debugData, null, 2));
      setCopied(true);
      setTimeout(() => setCopied(false), 1500);
    } catch (e) {
      setCopied(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
      <div className="fixed inset-4 bg-background rounded-lg shadow-xl overflow-hidden flex flex-col border border-border">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border bg-card">
          <div>
            <h2 className="text-lg font-semibold text-foreground">
              🐛 Recipe Wizard Debug Data
            </h2>
            <p className="text-sm text-muted-foreground">
              All accumulated data from the recipe creation process
            </p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4 space-y-4">

          {/* Health Concern Section */}
          <Collapsible open={expandedSections['healthConcern']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('healthConcern')}
              className="flex items-center justify-between w-full p-3 bg-card rounded-lg hover:bg-muted transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">1. Health Concern</span>
                <Badge variant={healthConcern ? "default" : "secondary"}>
                  {healthConcern ? "Completed" : "Empty"}
                </Badge>
              </div>
              {expandedSections['healthConcern'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 p-3 bg-muted rounded-lg">
              <pre className="text-sm overflow-auto text-muted-foreground">
                {JSON.stringify(healthConcern, null, 2)}
              </pre>
            </CollapsibleContent>
          </Collapsible>

          {/* Demographics Section */}
          <Collapsible open={expandedSections['demographics']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('demographics')}
              className="flex items-center justify-between w-full p-3 bg-card rounded-lg hover:bg-muted transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">2. Demographics</span>
                <Badge variant={demographics ? "default" : "secondary"}>
                  {demographics ? "Completed" : "Empty"}
                </Badge>
              </div>
              {expandedSections['demographics'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 p-3 bg-muted rounded-lg">
              <pre className="text-sm overflow-auto text-muted-foreground">
                {JSON.stringify(demographics, null, 2)}
              </pre>
            </CollapsibleContent>
          </Collapsible>

          {/* Causes Section */}
          <Collapsible open={expandedSections['causes']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('causes')}
              className="flex items-center justify-between w-full p-3 bg-card rounded-lg hover:bg-muted transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">3. Potential Causes</span>
                <Badge variant={selectedCauses.length > 0 ? "default" : "secondary"}>
                  {selectedCauses.length} selected / {potentialCauses.length} available
                </Badge>
              </div>
              {expandedSections['causes'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 space-y-2">
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">Selected Causes ({selectedCauses.length})</h4>
                <pre className="text-sm overflow-auto text-muted-foreground">
                  {JSON.stringify(selectedCauses, null, 2)}
                </pre>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">All Available Causes ({potentialCauses.length})</h4>
                <pre className="text-sm overflow-auto max-h-40 text-muted-foreground">
                  {JSON.stringify(potentialCauses, null, 2)}
                </pre>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Symptoms Section */}
          <Collapsible open={expandedSections['symptoms']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('symptoms')}
              className="flex items-center justify-between w-full p-3 bg-card rounded-lg hover:bg-muted transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">4. Potential Symptoms</span>
                <Badge variant={selectedSymptoms.length > 0 ? "default" : "secondary"}>
                  {selectedSymptoms.length} selected / {potentialSymptoms.length} available
                </Badge>
              </div>
              {expandedSections['symptoms'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 space-y-2">
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">Selected Symptoms ({selectedSymptoms.length})</h4>
                <pre className="text-sm overflow-auto text-muted-foreground">
                  {JSON.stringify(selectedSymptoms, null, 2)}
                </pre>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">All Available Symptoms ({potentialSymptoms.length})</h4>
                <pre className="text-sm overflow-auto max-h-40 text-muted-foreground">
                  {JSON.stringify(potentialSymptoms, null, 2)}
                </pre>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Properties Section */}
          <Collapsible open={expandedSections['properties']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('properties')}
              className="flex items-center justify-between w-full p-3 bg-card rounded-lg hover:bg-muted transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">5. Therapeutic Properties & Oils</span>
                <Badge variant={therapeuticProperties.length > 0 ? "default" : "secondary"}>
                  {therapeuticProperties.length} properties
                </Badge>
                <Badge variant={suggestedOils.length > 0 ? "default" : "secondary"}>
                  {suggestedOils.length} oils
                </Badge>
              </div>
              {expandedSections['properties'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 space-y-2">
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">Therapeutic Properties ({therapeuticProperties.length})</h4>
                <pre className="text-sm overflow-auto max-h-60 text-muted-foreground">
                  {JSON.stringify(therapeuticProperties, null, 2)}
                </pre>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">Suggested Oils ({suggestedOils.length})</h4>
                <pre className="text-sm overflow-auto max-h-40 text-muted-foreground">
                  {JSON.stringify(suggestedOils, null, 2)}
                </pre>
              </div>
              <div className="p-3 bg-muted rounded-lg">
                <h4 className="font-medium mb-2 text-foreground">Property Enrichment Status</h4>
                <pre className="text-sm overflow-auto text-muted-foreground">
                  {JSON.stringify(propertyEnrichmentStatus, null, 2)}
                </pre>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Metadata Section */}
          <Collapsible open={expandedSections['metadata']}>
            <CollapsibleTrigger
              onClick={() => toggleSection('metadata')}
              className="flex items-center justify-between w-full p-3 bg-muted rounded-lg hover:bg-card transition-colors"
            >
              <div className="flex items-center space-x-2">
                <span className="font-medium text-foreground">Wizard Metadata</span>
                <Badge variant="outline">
                  Step: {currentStep}
                </Badge>
              </div>
              {expandedSections['metadata'] ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2 p-3 bg-card rounded-lg">
              <div className="space-y-2 text-sm text-foreground">
                <div><strong>Current Step:</strong> {currentStep}</div>
                <div><strong>Completed Steps:</strong> {completedSteps.join(', ')}</div>
                <div><strong>Session ID:</strong> {sessionId}</div>
                <div><strong>Last Updated:</strong> {lastUpdated?.toLocaleString()}</div>
                <div><strong>Loading States:</strong></div>
                <ul className="ml-4 space-y-1">
                  <li>General Loading: {isLoading ? '✅' : '❌'}</li>
                  <li>Streaming Causes: {isStreamingCauses ? '✅' : '❌'}</li>
                  <li>Streaming Symptoms: {isStreamingSymptoms ? '✅' : '❌'}</li>
                  <li>Streaming Properties: {isStreamingProperties ? '✅' : '❌'}</li>
                  <li>Streaming Oils: {isStreamingOils ? '✅' : '❌'}</li>
                </ul>
                <div><strong>Auto Analyze Properties:</strong> {shouldAutoAnalyzeProperties ? '✅' : '❌'}</div>
                {error && <div className="text-red-600"><strong>Error:</strong> {error}</div>}
                {streamingError && <div className="text-red-600"><strong>Streaming Error:</strong> {streamingError}</div>}
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>

        {/* Footer */}
        <div className="border-t border-border p-4 bg-card">
          <div className="flex items-center justify-between gap-2 flex-wrap">
            <p className="text-sm text-muted-foreground">
              This overlay shows all data accumulated during the recipe creation process.
            </p>
            {/* ok_to_future_delete - Copy aggregated data button */}
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopy}
                className="flex items-center gap-1 border-dashed border-2 border-blue-400 text-blue-700 bg-blue-50 hover:bg-blue-100"
                title="Copy all debug data as JSON"
                aria-label="Copy all debug data as JSON"
              >
                <Copy className="h-4 w-4" />
                {copied ? 'Copied!' : 'Copy JSON'}
              </Button>
              {/* ok_to_future_delete - Copy minimal data button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopyMinimal}
                className="flex items-center gap-1 border-dashed border-2 border-green-400 text-green-700 bg-green-50 hover:bg-green-100"
                title="Copy minimal debug data as JSON"
                aria-label="Copy minimal debug data as JSON"
              >
                <Copy className="h-4 w-4" />
                {copiedMinimal ? 'Copied!' : 'Copy JSON (minimal)'}
              </Button>
              {/* ok_to_future_delete - Copy Solution 1 data button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySolution1}
                className="flex items-center gap-1 border-dashed border-2 border-orange-400 text-orange-700 bg-orange-50 hover:bg-orange-100"
                title="Copy Solution 1 (flattened) JSON"
                aria-label="Copy Solution 1 (flattened) JSON"
              >
                <Copy className="h-4 w-4" />
                {copiedSolution1 ? 'Copied!' : 'Copy JSON (Solution 1)'}
              </Button>
              {/* ok_to_future_delete - Copy Solution 4 data button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySolution4}
                className="flex items-center gap-1 border-dashed border-2 border-purple-400 text-purple-700 bg-purple-50 hover:bg-purple-100"
                title="Copy Solution 4 (relational) JSON"
                aria-label="Copy Solution 4 (relational) JSON"
              >
                <Copy className="h-4 w-4" />
                {copiedSolution4 ? 'Copied!' : 'Copy JSON (Solution 4)'}
              </Button>
              {/* ok_to_future_delete - Copy Solution 1 (inline) data button */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleCopySolution1Inline}
                className="flex items-center gap-1 border-dashed border-2 border-teal-400 text-teal-700 bg-teal-50 hover:bg-teal-100"
                title="Copy Solution 1 (inline, single-line) JSON"
                aria-label="Copy Solution 1 (inline, single-line) JSON"
              >
                <Copy className="h-4 w-4" />
                {copiedSolution1Inline ? 'Copied!' : 'Copy JSON (Solution 1 inline)'}
              </Button>
              <Button onClick={onClose} className="bg-primary text-primary-foreground hover:bg-primary/90">
                Close Debug
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
