/**
 * @fileoverview Final recipes display component - main container with tab navigation
 * Shows three time-specific essential oil protocols with overview and detailed views
 */

'use-client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useRecipeStore } from '../store/recipe-store';
import { RecipeTimeSlot } from '../types/recipe.types';
// Removed unused import
import { useBatchedRecipeUpdates } from '../hooks/use-batched-recipe-updates';
import { useParallelStreamingEngine } from '@/lib/ai/hooks/use-parallel-streaming-engine';
import { createStreamRequest } from '../utils/api-data-transform';
import { filterPropertiesForAI } from '../utils/oil-data-enrichment';
import { ProtocolSummaryCard } from './protocol-summary-card';
import { RecipeProtocolCard } from './recipe-protocol-card';
import { SafetyWarnings } from './safety-warnings';
import { useI18n } from '@/hooks/use-i18n';

/**
 * Tab types for navigation
 */
type TabType = 'overview' | 'recipes' | 'studies' | 'security';

/**
 * Main final recipes display component with tab navigation
 * Follows the exact structure from standalone-v1.html
 */
export function FinalRecipesDisplay() {
  console.log('🍃 [Final Recipes] Component mounting/rendering');
  
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [activeProtocol, setActiveProtocol] = useState<RecipeTimeSlot>('morning');
  const { t } = useI18n();

  // Store state - using the same data source as debug overlay
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    therapeuticProperties, // This is the main data source (same as debug overlay)
    finalRecipes,
    isStreamingFinalRecipes,
    setFinalRecipesGenerating
  } = useRecipeStore();

  // Use parallel streaming engine like properties component
  const { streamingState: parallelStreamingState, startStreams } = useParallelStreamingEngine<any>();

  // Streaming hooks
  const { completeAIStreaming } = useBatchedRecipeUpdates();

  // CRITICAL: Single execution guard ref to prevent infinite loops (Properties pattern)
  const autoTriggerExecutedRef = React.useRef(false);

  /**
   * Generate all three recipe protocols
   * Memoized to prevent unnecessary re-renders
   */
  const handleGenerateRecipes = useCallback(async () => {
    console.log('🍃 [Final Recipes] handleGenerateRecipes called');
    
    // Get current values from the store to avoid stale closures
    const currentState = useRecipeStore.getState();
    const {
      healthConcern: currentHealthConcern,
      demographics: currentDemographics,
      selectedCauses: currentSelectedCauses,
      selectedSymptoms: currentSelectedSymptoms,
      therapeuticProperties: currentTherapeuticProperties
    } = currentState;

    console.log('🔍 [Final Recipes] Current state validation:', {
      hasHealthConcern: !!currentHealthConcern,
      hasDemographics: !!currentDemographics,
      selectedCausesCount: currentSelectedCauses.length,
      selectedSymptomsCount: currentSelectedSymptoms.length,
      therapeuticPropertiesCount: currentTherapeuticProperties.length
    });

    if (!currentHealthConcern || !currentDemographics) {
      console.log('❌ [Final Recipes] Missing basic required data');
      return;
    }

    // Validate required data using therapeuticProperties (corrected data source)
    const hasTherapeuticPropertiesWithOils = currentTherapeuticProperties.some(prop =>
      prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0
    );

    console.log('🔍 [Final Recipes] Advanced data validation:', {
      hasTherapeuticPropertiesWithOils,
      enrichedPropertiesCount: currentTherapeuticProperties.filter(p => p.isEnriched).length,
      propertiesWithOilsCount: currentTherapeuticProperties.filter(p => p.suggested_oils && p.suggested_oils.length > 0).length
    });

    if (currentSelectedCauses.length === 0 || currentSelectedSymptoms.length === 0 || !hasTherapeuticPropertiesWithOils) {
      console.log('❌ [Final Recipes] Missing wizard data for recipe generation');
      return;
    }

    // Prevent duplicate generation
    if (finalRecipes.hasStartedGeneration) {
      console.log('❌ [Final Recipes] Recipe generation already started, skipping');
      return;
    }

    console.log('✅ [Final Recipes] All validations passed, starting generation...');

    try {
      console.log('🍃 [Final Recipes] Setting generation flag...');
      setFinalRecipesGenerating(true);

      const timeSlots: RecipeTimeSlot[] = ['morning', 'mid-day', 'night'];

      // FILTER THERAPEUTIC PROPERTIES (DRY: use same filtering logic as debug overlay)
      const filteredTherapeuticProperties = filterPropertiesForAI(currentTherapeuticProperties);

      console.log('🔍 [Final Recipes] Filtered therapeutic properties:', {
        propertiesCount: filteredTherapeuticProperties.length,
        // FIX (18048): Added optional chaining to prevent error if suggested_oils is undefined.
        totalOilsCount: filteredTherapeuticProperties.reduce((total, prop) => total + (prop.suggested_oils?.length || 0), 0)
      });

      // Create parallel streaming requests using the utility
      const requests = timeSlots.map(timeSlot => ({
        id: timeSlot,
        url: '/api/ai/streaming',
        requestData: createStreamRequest(
          'create-recipe',
          'final-recipes',
          currentHealthConcern,
          currentDemographics,
          currentSelectedCauses,
          currentSelectedSymptoms,
          'PT_BR',
          undefined, // No property for final recipes
          { timeSlot, suggestedOils: filteredTherapeuticProperties } // DRY: same data source as debug overlay
        ),
        label: `${timeSlot} recipe`,
        responseParser: (updates: any) => {
          if (updates.finalData?.data?.recipe_protocol) {
            return updates.finalData.data.recipe_protocol;
          }
          return null;
        }
      }));

      console.log('🚀 [Final Recipes] Starting parallel streaming with requests:', {
        requestsCount: requests.length,
        timeSlots: requests.map(r => r.id)
      });

      // Start parallel streaming (non-blocking, results handled by useEffect)
      startStreams(requests);
      console.log('✅ [Final Recipes] Parallel streaming initiated successfully');

    } catch (error) {
      console.error('❌ [Final Recipes] Recipe generation failed:', error);

      // CRITICAL: Reset generation flag on error to allow retry
      setFinalRecipesGenerating(false);
      // CRITICAL: Reset execution guard on error to allow retry (Properties pattern)
      autoTriggerExecutedRef.current = false;
      completeAIStreaming('final-recipes', new Map());
    }
  }, [
    // Only include stable dependencies
    startStreams,
    completeAIStreaming,
    setFinalRecipesGenerating
  ]);

  // Auto-start recipe generation when component mounts
  // Using the same execution guard pattern from Properties step
  useEffect(() => {
    console.log('🔍 [Final Recipes] Auto-trigger useEffect running...');
    
    // CRITICAL: Check execution guard FIRST, before any other logic (Properties pattern)
    if (autoTriggerExecutedRef.current) {
      console.log('❌ [Final Recipes] Auto-trigger skipped: already executed (execution guard)');
      return;
    }

    // Check if we have existing recipes or processing is in progress
    const hasExistingRecipes = !!finalRecipes.morning.recipe || 
                              !!finalRecipes.midDay.recipe || 
                              !!finalRecipes.night.recipe;
    
    const isProcessing = finalRecipes.isGenerating || parallelStreamingState.isStreaming;
    
    console.log('🔍 [Final Recipes] State checks:', {
      hasExistingRecipes,
      isProcessing: isProcessing,
      morningRecipe: !!finalRecipes.morning.recipe,
      midDayRecipe: !!finalRecipes.midDay.recipe,
      nightRecipe: !!finalRecipes.night.recipe,
      isGenerating: finalRecipes.isGenerating,
      isStreaming: parallelStreamingState.isStreaming
    });
    
    if (hasExistingRecipes || isProcessing) {
      console.log('❌ [Final Recipes] Auto-trigger skipped: recipes exist or processing in progress');
      return;
    }

    // Check required data using therapeuticProperties (corrected data source)
    const hasTherapeuticPropertiesWithOils = therapeuticProperties.some(prop =>
      prop.isEnriched && prop.suggested_oils && prop.suggested_oils?.length > 0
    );

    const hasRequiredData = !!healthConcern && 
                           !!demographics &&
                           selectedCauses.length > 0 &&
                           selectedSymptoms.length > 0 &&
                           hasTherapeuticPropertiesWithOils;

    console.log('🔍 [Final Recipes] Required data check:', {
      hasHealthConcern: !!healthConcern,
      hasDemographics: !!demographics,
      selectedCausesCount: selectedCauses.length,
      selectedSymptomsCount: selectedSymptoms.length,
      hasTherapeuticPropertiesWithOils,
      therapeuticPropertiesCount: therapeuticProperties.length,
      enrichedPropertiesCount: therapeuticProperties.filter(p => p.isEnriched).length,
      hasRequiredData
    });

    const canGenerate = hasRequiredData && !hasExistingRecipes && !isProcessing;

    console.log('🔍 [Final Recipes] Final decision:', {
      canGenerate,
      willTrigger: canGenerate
    });

    if (canGenerate) {
      console.log('🤖 [Final Recipes] Auto-triggering recipe generation');
      
      // CRITICAL: Mark as executed IMMEDIATELY to prevent race conditions (Properties pattern)
      autoTriggerExecutedRef.current = true;
      
      // Execute the generation
      console.log('🚀 [Final Recipes] About to call handleGenerateRecipes');
      handleGenerateRecipes();
    } else {
      console.log('❌ [Final Recipes] Auto-trigger conditions not met');
    }
  }, [
    // CRITICAL FIX: Use stable dependencies (data lengths, not objects)
    !!healthConcern,
    !!demographics,
    selectedCauses.length,
    selectedSymptoms.length,
    therapeuticProperties.filter(p => p.isEnriched).length,
    !!finalRecipes.morning.recipe,
    !!finalRecipes.midDay.recipe,
    !!finalRecipes.night.recipe,
    finalRecipes.isGenerating,
    parallelStreamingState.isStreaming,
    handleGenerateRecipes
  ]);

  // Reset execution guard when streaming completes or fails (Properties pattern)
  const isStreamingRef = React.useRef(parallelStreamingState.isStreaming);
  useEffect(() => {
    // Reset guard when streaming stops (completion or error)
    if (!parallelStreamingState.isStreaming && isStreamingRef.current) {
      autoTriggerExecutedRef.current = false;
    }
    isStreamingRef.current = parallelStreamingState.isStreaming;
  }, [parallelStreamingState.isStreaming]);

  /**
   * Track which time slots have been processed to prevent duplicate processing
   */
  const processedTimeSlotsRef = useRef(new Set<string>());
  
  /**
   * Handle parallel streaming results incrementally (like Properties step)
   * Process each result as it arrives, preventing duplicates
   */
  const handleStreamingResults = useCallback(() => {
    if (parallelStreamingState.results.size === 0) return;
    
    // Process each result individually as it arrives
    Array.from(parallelStreamingState.results.entries()).forEach(([timeSlot, recipe]) => {
      // Skip if already processed this time slot
      if (processedTimeSlotsRef.current.has(timeSlot)) {
        return;
      }
      
      if (recipe) {
        // Mark this time slot as processed
        processedTimeSlotsRef.current.add(timeSlot);
        
        // Update store with this individual recipe
        const singleResultMap = new Map();
        singleResultMap.set(timeSlot, recipe);
        completeAIStreaming('final-recipes', singleResultMap);
      }
    });
  }, [parallelStreamingState.results, completeAIStreaming]);

  /**
   * Process parallel streaming results when they change
   * Reset processed time slots when streaming starts fresh
   */
  useEffect(() => {
    // Reset processed time slots when streaming starts fresh
    if (parallelStreamingState.isStreaming && parallelStreamingState.results.size === 0) {
      processedTimeSlotsRef.current.clear();
    }
    
    handleStreamingResults();
  }, [handleStreamingResults, parallelStreamingState.isStreaming]);

  /**
   * Switch between tabs
   */
  const switchTab = (tab: TabType) => {
    setActiveTab(tab);
  };

  /**
   * Switch between protocol timelines in recipes tab
   */
  const switchProtocol = (protocol: RecipeTimeSlot) => {
    setActiveProtocol(protocol);
  };

  /**
   * Switch to recipes tab and optionally set a specific protocol
   */
  const switchToRecipes = (protocol?: RecipeTimeSlot) => {
    setActiveTab('recipes');
    if (protocol) {
      setActiveProtocol(protocol);
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      {/* Tab Navigation - Responsive with horizontal scroll on mobile */}
      <nav className="flex gap-6 border-b-2 border-border mb-8 sticky top-0 z-10 bg-background/80 backdrop-blur-sm overflow-x-auto scrollbar-hide">
        <div className="flex gap-6 min-w-max px-2 sm:px-0">
          <TabButton
            active={activeTab === 'overview'}
            onClick={() => switchTab('overview')}
            icon={<InfoIcon />}
            label={t('create-recipe:steps.final-recipes.tabs.overview')}
          />
          <TabButton
            active={activeTab === 'recipes'}
            onClick={() => switchTab('recipes')}
            icon={<FlaskIcon />}
            label={t('create-recipe:steps.final-recipes.tabs.recipes')}
          />
          <TabButton
            active={activeTab === 'studies'}
            onClick={() => switchTab('studies')}
            icon={<DocumentIcon />}
            label={t('create-recipe:steps.final-recipes.tabs.studies')}
          />
          <TabButton
            active={activeTab === 'security'}
            onClick={() => switchTab('security')}
            icon={<ShieldIcon />}
            label={t('create-recipe:steps.final-recipes.tabs.security')}
          />
        </div>
      </nav>

      {/* Tab Content */}
      <main>
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <OverviewTab
            healthConcern={healthConcern}
            demographics={demographics}
            selectedCauses={selectedCauses}
            selectedSymptoms={selectedSymptoms}
            finalRecipes={finalRecipes}
            isLoading={isStreamingFinalRecipes}
            onSwitchToRecipes={switchToRecipes}
            t={t}
          />
        )}

        {/* Recipes Tab */}
        {activeTab === 'recipes' && (
          <RecipesTab
            activeProtocol={activeProtocol}
            onSwitchProtocol={switchProtocol}
            finalRecipes={finalRecipes}
            isLoading={isStreamingFinalRecipes}
          />
        )}

        {/* Studies Tab */}
        {activeTab === 'studies' && (
          <StudiesTab />
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          // FIX (6133): Removed unused 'demographics' prop.
          <SecurityTab finalRecipes={finalRecipes} />
        )}
      </main>
    </div>
  );
}

/**
 * Tab button component
 */
interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
}

function TabButton({ active, onClick, icon, label }: TabButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`
        flex items-center gap-2 px-1 py-3 border-none bg-none cursor-pointer
        text-base font-medium border-b-2 transform translate-y-0.5
        transition-all duration-200 ease-in-out
        ${active
          ? 'text-primary font-semibold border-primary'
          : 'text-muted-foreground border-transparent hover:text-primary'
        }
      `}
    >
      <span className="h-5 w-5">{icon}</span>
      {label}
    </button>
  );
}

/**
 * Overview tab component
 */
interface OverviewTabProps {
  healthConcern: any;
  demographics: any;
  selectedCauses: any[];
  selectedSymptoms: any[];
  finalRecipes: any;
  isLoading: boolean;
  onSwitchToRecipes: (protocol?: RecipeTimeSlot) => void;
  t: (key: string, fallback?: string) => string;
}

function OverviewTab({
  healthConcern,
  demographics,
  selectedCauses,
  selectedSymptoms,
  finalRecipes,
  isLoading,
  onSwitchToRecipes,
  t
}: OverviewTabProps) {
  const generatedRecipes = [
    finalRecipes.morning.recipe,
    finalRecipes.midDay.recipe,
    finalRecipes.night.recipe
  ].filter(Boolean);

  // FIX (7006): Added explicit 'string' type to the 'causeId' parameter.
  const getCauseNameById = useCallback((causeId: string) => {
    return selectedCauses.find(cause => cause.cause_id === causeId)?.cause_name || t('create-recipe:steps.final-recipes.overview.unknownCause');
  }, [selectedCauses, t]);

  // FIX (7006): Added explicit 'string' type to the 'symptomId' parameter.
  const getSymptomNameById = useCallback((symptomId: string) => {
    return selectedSymptoms.find(symptom => symptom.symptom_id === symptomId)?.symptom_name || t('create-recipe:steps.final-recipes.overview.unknownSymptom');
  }, [selectedSymptoms, t]);

  // Extract echo data from first available recipe
  const echoData = React.useMemo(() => {
    const firstAvailableRecipe = generatedRecipes[0];
    return firstAvailableRecipe?.echo;
  }, [generatedRecipes]);

  const therapeuticStrategy = React.useMemo(() => {
    if (generatedRecipes.length === 0) return null;

    // Aggregate therapeutic properties from all recipes
    const allTherapeuticProperties = generatedRecipes.flatMap(r => 
      r.therapeutic_properties_targeted || []
    );
    const uniqueTherapeuticProperties = allTherapeuticProperties.filter((prop, index, self) => 
      index === self.findIndex(p => p.property_id === prop.property_id)
    );

    // Aggregate application methods
    const allApplicationMethods = generatedRecipes.map(r => r.application_type_localized).filter(Boolean);
    // FIX (2802): Changed spread syntax to Array.from() for better compatibility with older JS targets.
    const uniqueApplicationMethods = Array.from(new Set(allApplicationMethods));

    const themes = generatedRecipes.map(r => r.recipe_theme_localized);
    const benefits = generatedRecipes.map(r => r.holistic_benefit_localized);

    return { 
      themes, 
      benefits, 
      uniqueTherapeuticProperties,
      uniqueApplicationMethods
    };
  }, [generatedRecipes]);

  // Centralized flip state management for protocol summary cards
  const [flippedCard, setFlippedCard] = React.useState<string | null>(null);

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8">
        {/* User Profile */}
        <div className="lg:col-span-2">
          <div className="bg-card rounded-lg p-6 shadow-sm h-full border border-border">
            <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
              <UserIcon className="h-6 w-6 text-primary" />
              {t('create-recipe:steps.final-recipes.overview.userProfile.title')}
            </h2>
            <div className="profile-data space-y-2">
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.condition')}:</strong> <span className="text-muted-foreground">{echoData?.health_concern_input || healthConcern?.healthConcern || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')}</span></div>
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.age')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input?.age_specific ? `${echoData.user_info_input.age_specific} ${echoData.user_info_input.age_unit}` : `${demographics?.specificAge || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')} anos`}</span></div>
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.gender')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input?.gender || demographics?.gender || t('create-recipe:steps.final-recipes.overview.userProfile.notProvided')}</span></div>
            </div>

              {(selectedCauses.length > 0 || echoData?.selected_cause_ids) && (
                <div className="pt-2">
                  <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                    {t('create-recipe:steps.final-recipes.overview.userProfile.identifiedCauses')}
                  </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {/* Show from selectedCauses array */}
                    {selectedCauses.map((cause) => (
                      <span key={cause.cause_id} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                        {cause.cause_name}
                      </span>
                    ))}
                    {/* Show from echo data UUIDs */}
                    {echoData?.selected_cause_ids?.map((causeId) => (
                      <span key={causeId} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                        {getCauseNameById(causeId)}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {(selectedSymptoms.length > 0 || echoData?.selected_symptom_ids) && (
                <div className="pt-2">
                  <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                    {t('create-recipe:steps.final-recipes.overview.userProfile.identifiedSymptoms')}
                  </span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {/* Show from selectedSymptoms array */}
                    {selectedSymptoms.map((symptom) => (
                      <span key={symptom.symptom_id} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
                        {symptom.symptom_name}
                      </span>
                    ))}
                    {/* Show from echo data UUIDs */}
                    {echoData?.selected_symptom_ids?.map((symptomId) => (
                      <span key={symptomId} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
                        {getSymptomNameById(symptomId)}
                      </span>
                    ))}
                  </div>
                </div>
              )}
          </div>
        </div>
        {/* FIX (1005, 1128, 1109, 2304): Removed an extra closing </div> tag that was causing a major JSX syntax error.
            This single fix resolves multiple cascading errors like "')' expected" and "Declaration or statement expected". */}

        {/* DYNAMIC Therapeutic Strategy Section */}
        <div className="lg:col-span-3">
          <div className="bg-card rounded-lg p-6 shadow-sm h-full border border-border">
            <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
              <ChartIcon className="h-6 w-6 text-primary" />
              {t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.title')}
            </h2>
            {therapeuticStrategy ? (
              <div className="space-y-4">
                {/* Therapeutic Properties - aggregated */}
                <div className="therapeutic-properties mb-4">
                  <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.properties')}</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {therapeuticStrategy.uniqueTherapeuticProperties.map((property) => (
                      <div key={property.property_id} className="bg-primary/5 p-3 rounded-lg border border-primary/10">
                        <strong className="text-primary">{property.property_name_localized}</strong>
                        <p className="text-sm text-muted-foreground">{property.description_localized}</p>
                      </div>
                    ))}
                  </div>
                </div>
                {/* Application Methods - aggregated */}
                <div className="application-methods">
                  <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.applicationMethods')}</h4>
                  <div className="flex flex-wrap gap-2">
                    {therapeuticStrategy.uniqueApplicationMethods.map((method) => (
                      <span key={method} className="inline-block px-3 py-1 text-sm font-medium bg-accent/10 text-accent-foreground rounded-full border border-accent/20">
                        {method}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground">
                {t('create-recipe:steps.final-recipes.loading', 'Generating therapeutic strategy...')}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Protocol Summary Cards */}
      <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
        <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
          <ClipboardIcon className="h-6 w-6 text-primary" />
          {t('create-recipe:steps.final-recipes.overview.protocolSummary.title')}
        </h2>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            {['morning', 'mid-day', 'night'].map((timeSlot) => (
              <div key={timeSlot} className="h-96 bg-muted rounded-2xl animate-pulse flex items-center justify-center">
                <div className="text-muted-foreground">{t('create-recipe:steps.final-recipes.loading')}</div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            <ProtocolSummaryCard
              timeSlot="morning"
              recipe={finalRecipes.morning.recipe}
              onViewDetails={() => onSwitchToRecipes('morning')}
              isFlipped={flippedCard === 'morning'}
              onFlip={() => setFlippedCard(flippedCard === 'morning' ? null : 'morning')}
            />
            <ProtocolSummaryCard
              timeSlot="mid-day"
              recipe={finalRecipes.midDay.recipe}
              onViewDetails={() => onSwitchToRecipes('mid-day')}
              isFlipped={flippedCard === 'mid-day'}
              onFlip={() => setFlippedCard(flippedCard === 'mid-day' ? null : 'mid-day')}
            />
            <ProtocolSummaryCard
              timeSlot="night"
              recipe={finalRecipes.night.recipe}
              onViewDetails={() => onSwitchToRecipes('night')}
              isFlipped={flippedCard === 'night'}
              onFlip={() => setFlippedCard(flippedCard === 'night' ? null : 'night')}
            />
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Recipes tab component
 */
interface RecipesTabProps {
  activeProtocol: RecipeTimeSlot;
  onSwitchProtocol: (protocol: RecipeTimeSlot) => void;
  finalRecipes: any;
  isLoading: boolean;
}

function RecipesTab({ activeProtocol, onSwitchProtocol, finalRecipes, isLoading }: RecipesTabProps) {
  const { t } = useI18n();
  // Map from JSON data.recipe_protocol for each time slot
  const timeSlots = [
    {
      key: 'morning' as RecipeTimeSlot,
      time: finalRecipes.morning.recipe?.time_range_localized || t('create-recipe:steps.final-recipes.protocols.morning.timeRange'),
      label: `${t('create-recipe:steps.final-recipes.timeline.protocol')} ${finalRecipes.morning.recipe?.time_of_day_localized || t('create-recipe:steps.final-recipes.protocols.morning.label')}`,
      description: finalRecipes.morning.recipe?.description_localized || t('create-recipe:steps.final-recipes.protocols.morning.description')
    },
    {
      key: 'mid-day' as RecipeTimeSlot,
      time: finalRecipes.midDay.recipe?.time_range_localized || t('create-recipe:steps.final-recipes.protocols.midDay.timeRange'),
      label: `${t('create-recipe:steps.final-recipes.timeline.protocol')} ${finalRecipes.midDay.recipe?.time_of_day_localized || t('create-recipe:steps.final-recipes.protocols.midDay.label')}`,
      description: finalRecipes.midDay.recipe?.description_localized || t('create-recipe:steps.final-recipes.protocols.midDay.description')
    },
    {
      key: 'night' as RecipeTimeSlot,
      time: finalRecipes.night.recipe?.time_range_localized || t('create-recipe:steps.final-recipes.protocols.night.timeRange'),
      label: `${t('create-recipe:steps.final-recipes.timeline.protocol')} ${finalRecipes.night.recipe?.time_of_day_localized || t('create-recipe:steps.final-recipes.protocols.night.label')}`,
      description: finalRecipes.night.recipe?.description_localized || t('create-recipe:steps.final-recipes.protocols.night.description')
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 lg:gap-8 px-2 sm:px-4 md:px-0">
      {/* Timeline Navigation */}
      <aside className="md:col-span-1 flex flex-col items-center md:items-stretch">
        <div className="relative flex flex-col h-full timeline">
          {timeSlots.map((slot) => (
            <div
              key={slot.key}
              className={`
                relative pl-10 pb-8 cursor-pointer transition-all duration-200 timeline-item
                ${activeProtocol === slot.key ? 'active active-box' : ''}
              `}
              onClick={() => onSwitchProtocol(slot.key)}
            >
              {/* Timeline dot */}
              <div className="dot" />

              {/* Content */}
              <div className={`
                p-4 rounded-lg transition-all duration-200 border
                ${activeProtocol === slot.key
                  ? 'bg-primary/5 border-primary/20 shadow-md'
                  : 'bg-muted/50 border-border hover:bg-primary/5'
                }
              `}>
                <p className="text-sm text-primary">{slot.time}</p>
                <h4 className="font-bold text-foreground">{slot.label}</h4>
                <p className="text-sm text-muted-foreground">{slot.description}</p>
              </div>
            </div>
          ))}
        </div>
      </aside>

      {/* Protocol Cards Container */}
      <section className="md:col-span-3 flex flex-col items-center gap-8 w-full">
        {isLoading ? (
          <div className="w-full max-w-4xl h-96 bg-muted rounded-2xl animate-pulse flex items-center justify-center">
            <div className="text-muted-foreground">{t('create-recipe:steps.final-recipes.loading')}</div>
          </div>
        ) : (
          <RecipeProtocolCard
            timeSlot={activeProtocol}
            recipe={finalRecipes[activeProtocol === 'mid-day' ? 'midDay' : activeProtocol]?.recipe}
          />
        )}
      </section>
    </div>
  );
}

/**
 * Studies tab component
 */
function StudiesTab() {
  const { t } = useI18n();
  return (
    <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
      <h2 className="text-xl font-bold text-card-foreground mb-4">{t('create-recipe:steps.final-recipes.studies.title')}</h2>
      <p className="text-muted-foreground">
        {t('create-recipe:steps.final-recipes.studies.description')}
      </p>
      <div className="mt-4 p-4 bg-accent rounded-lg">
        <p className="text-accent-foreground text-sm">
          🔬 <strong>Em desenvolvimento:</strong> {t('create-recipe:steps.final-recipes.studies.inDevelopment')}
        </p>
      </div>
    </div>
  );
}

/**
 * Security tab component
 */
// FIX (6133): Removed unused 'demographics' prop.
interface SecurityTabProps {
  finalRecipes: any;
}

function SecurityTab({ finalRecipes }: SecurityTabProps) {
  // Aggregate safety warnings from all recipes
  const allSafetyWarnings = React.useMemo(() => {
    const recipes = [
      finalRecipes.morning.recipe,
      finalRecipes.midDay.recipe,
      finalRecipes.night.recipe
    ].filter(Boolean);

    return recipes.flatMap(recipe => recipe.safety_warnings || []);
  }, [finalRecipes]);

  return (
    <div className="space-y-6">
      {/* SafetyWarnings component using JSON safety_warnings array */}
      <SafetyWarnings warnings={allSafetyWarnings} />
    </div>
  );
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function InfoIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function FlaskIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
    </svg>
  );
}

function DocumentIcon() {
  return (
    <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 20h9" />
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16.5 3.5a2.121 2.121 0 013 3L7 19.5 3 21l1.5-4L16.5 3.5z" />
    </svg>
  );
}

function ShieldIcon({ className }: { className?: string }) {
  return (
    <svg className={className || "h-5 w-5"} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
    </svg>
  );
}

function UserIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0m-6 4a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function ChartIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2m0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
    </svg>
  );
}

function ClipboardIcon({ className }: { className?: string }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth="2">
      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
    </svg>
  );
}