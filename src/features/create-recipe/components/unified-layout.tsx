/**
 * @fileoverview Unified responsive layout component for Essential Oil Recipe Creator.
 * Replaces dashboard-layout.tsx, mobile-layout.tsx, and wizard-layout.tsx with a single,
 * KISS-compliant component that handles all layout scenarios.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import { useRecipeStore } from '../store/recipe-store';
import { BreadcrumbNavigation, CompactBreadcrumbNavigation } from './breadcrumb-navigation';
import { cn } from '@/lib/utils';
import { useI18n } from '@/hooks/use-i18n';

/**
 * Unified layout props
 */
interface UnifiedLayoutProps {
  children: React.ReactNode;
  layout?: 'dashboard' | 'mobile' | 'standalone';
  showBreadcrumbs?: boolean;
  showProgress?: boolean;
  className?: string;
}

/**
 * Progress sidebar component (for dashboard layout)
 */
function ProgressSidebar() {
  const { t } = useI18n();
  const router = useRouter();
  const { stepInfo, getCompletionPercentage } = useRecipeWizardNavigation();
  const { currentStep, completedSteps, resetWizard } = useRecipeStore();

  const completionPercentage = getCompletionPercentage();

  const handleStartNewRecipe = () => {
    resetWizard();
    router.push('/dashboard/create-recipe/health-concern');
  };

  return (
    <div className="bg-card rounded-lg border p-4 space-y-4">
      {/* Progress Header */}
      <div className="space-y-2">
        <h3 className="font-semibold text-foreground">{t('create-recipe:summary.progress', 'Recipe Progress')}</h3>
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>{t('create-recipe:navigation.progress', 'Step {current} of {total}', { current: stepInfo.progress, total: 6 })}</span>
          <span>{completionPercentage}%</span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-muted rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500 ease-in-out"
            style={{ width: `${completionPercentage}%` }}
          />
        </div>
      </div>

      {/* Step List */}
      <div className="space-y-2">
        {[
          { key: 'health-concern', step: 1 },
          { key: 'demographics', step: 2 },
          { key: 'causes', step: 3 },
          { key: 'symptoms', step: 4 },
          { key: 'properties', step: 5 },
          { key: 'oils', step: 6 }
        ].map((item) => {
          const isActive = currentStep === item.key;
          const isCompleted = completedSteps.includes(item.key as any);
          
          return (
            <div
              key={item.key}
              className={cn(
                "flex items-center space-x-3 p-2 rounded-md text-sm transition-colors",
                isActive && "bg-primary/10 text-primary",
                isCompleted && !isActive && "text-muted-foreground",
                !isActive && !isCompleted && "text-muted-foreground/60"
              )}
            >
              {/* Step Number/Status */}
              <div className={cn(
                "flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium transition-colors flex-shrink-0",
                isActive && "bg-primary text-primary-foreground",
                isCompleted && !isActive && "bg-green-100 text-green-600",
                !isActive && !isCompleted && "bg-muted text-muted-foreground"
              )}>
                {isCompleted && !isActive ? (
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                ) : (
                  item.step
                )}
              </div>
              
              {/* Step Title */}
              <span className="font-medium">{t(`create-recipe:steps.${item.key}.title`)}</span>
            </div>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="pt-2 border-t space-y-2">
        <button className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors">
          💾 {t('create-recipe:actions.saveProgress', 'Save Progress')}
        </button>
        <button className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors">
          📋 {t('create-recipe:actions.viewPrevious', 'View Previous Recipes')}
        </button>
        <button
          onClick={handleStartNewRecipe}
          className="w-full text-left text-sm text-muted-foreground hover:text-foreground transition-colors"
        >
          🔄 {t('create-recipe:actions.startNew', 'Start New Recipe')}
        </button>
      </div>
    </div>
  );
}



/**
 * Mobile header component
 */
function MobileHeader() {
  const { t } = useI18n();
  const { stepInfo, getCompletionPercentage } = useRecipeWizardNavigation();
  const { currentStep } = useRecipeStore();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const completionPercentage = getCompletionPercentage();

  return (
    <div className="sticky top-0 z-50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b lg:hidden">
      <div className="px-4 py-3">
        {/* Header content */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex-1 min-w-0">
            <h1 className="text-base sm:text-lg font-semibold text-foreground truncate">
              {t(`create-recipe:steps.${stepInfo.current.key}.title`)}
            </h1>
            <p className="text-xs sm:text-sm text-muted-foreground">
              {t('wizard.stepProgress', `Step ${stepInfo.progress} of 6`, { progress: stepInfo.progress, total: 6 })}
            </p>
          </div>

          {/* Menu toggle for mobile */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-2 rounded-md hover:bg-muted transition-colors flex-shrink-0"
            aria-label="Toggle navigation menu"
          >
            <svg
              className={cn("w-4 h-4 sm:w-5 sm:h-5 transition-transform", isMenuOpen && "rotate-180")}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={isMenuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"}
              />
            </svg>
          </button>
        </div>

        {/* Progress bar */}
        <div className="mt-3">
          <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
            <span>{t('wizard.progress', 'Progress')}</span>
            <span>{completionPercentage}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-500 ease-in-out"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>

        {/* Mobile navigation menu */}
        {isMenuOpen && (
          <div className="mt-4 pb-2">
            <CompactBreadcrumbNavigation
              currentStep={currentStep}
              className="border-t pt-4"
            />
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Dashboard header component
 */
function DashboardHeader({ showBreadcrumbs = true }: { showBreadcrumbs?: boolean }) {
  const { t } = useI18n();
  const { stepInfo } = useRecipeWizardNavigation();

  return (
    <div className="space-y-4">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">
            {t('create-recipe:title', 'Create Essential Oil Recipe')}
          </h1>
          <p className="text-muted-foreground">
            {t('create-recipe:subtitle', 'Get personalized essential oil recommendations for your health concerns')}
          </p>
        </div>

        {/* Current Step Badge */}
        <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm font-medium">
          {t(`create-recipe:steps.${stepInfo.current.key}.title`, stepInfo.current.title)}
        </div>
      </div>

      {/* Breadcrumb Navigation */}
      {showBreadcrumbs && (
        <BreadcrumbNavigation
          currentStep={stepInfo.current.key}
          showStepNumbers={true}
          showCompletionStatus={true}
          allowNavigation={true}
          className="bg-muted/30 rounded-lg p-3"
        />
      )}
    </div>
  );
}

/**
 * Touch-friendly scroll area
 */
function ScrollArea({ children, className }: { children: React.ReactNode; className?: string }) {
  return (
    <div
      className={cn(
        "flex-1 overflow-y-auto overscroll-y-contain",
        // iOS momentum scrolling
        "[-webkit-overflow-scrolling:touch]",
        // Custom scrollbar for webkit browsers
        "[&::-webkit-scrollbar]:w-2",
        "[&::-webkit-scrollbar-track]:bg-muted/20",
        "[&::-webkit-scrollbar-thumb]:bg-muted",
        "[&::-webkit-scrollbar-thumb]:rounded-full",
        "[&::-webkit-scrollbar-thumb:hover]:bg-muted/80",
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Unified layout component that handles all scenarios
 */
export function UnifiedLayout({
  children,
  layout = 'mobile',
  showBreadcrumbs = true,
  showProgress = true,
  className
}: UnifiedLayoutProps) {
  const [viewportHeight, setViewportHeight] = useState<number>(0);
  const { currentStep } = useRecipeStore();

  // Handle viewport height changes (mobile browser address bar)
  useEffect(() => {
    const updateViewportHeight = () => {
      setViewportHeight(window.innerHeight);
    };

    updateViewportHeight();
    window.addEventListener('resize', updateViewportHeight);
    window.addEventListener('orientationchange', updateViewportHeight);

    return () => {
      window.removeEventListener('resize', updateViewportHeight);
      window.removeEventListener('orientationchange', updateViewportHeight);
    };
  }, []);

  // Dashboard layout - responsive behavior
  if (layout === 'dashboard') {
    return (
      <>
        {/* Desktop Dashboard Layout */}
        <div className={cn("hidden lg:block space-y-6", className)}>
          <DashboardHeader showBreadcrumbs={showBreadcrumbs} />
          
          <div className="grid grid-cols-4 gap-6">
            <div className="col-span-3">
              <div className="bg-card rounded-lg border p-6">
                {children}
              </div>
            </div>
            
            {showProgress && (
              <div className="col-span-1">
                <div className="sticky top-6">
                  <ProgressSidebar />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Mobile Dashboard Layout - same as mobile layout */}
        <div
          className={cn("lg:hidden flex flex-col min-h-screen bg-background", className)}
          style={{
            minHeight: viewportHeight > 0 ? `${viewportHeight}px` : '100vh'
          }}
        >
          <MobileHeader />
          
          {showBreadcrumbs && (
            <div className="hidden md:block border-b bg-muted/30">
              <div className="px-4 py-3">
                <BreadcrumbNavigation
                  currentStep={currentStep}
                  showStepNumbers={true}
                  showCompletionStatus={true}
                  allowNavigation={true}
                />
              </div>
            </div>
          )}
          
          <ScrollArea className="flex-1">
            <main className="px-4 py-6">
              <div className="max-w-4xl mx-auto">
                {children}
              </div>
            </main>
          </ScrollArea>
        </div>
      </>
    );
  }

  // Standalone layout
  if (layout === 'standalone') {
    return (
      <div className={cn("w-full", className)}>
        {children}
      </div>
    );
  }

  // Mobile layout (default)
  return (
    <div
      className={cn("flex flex-col min-h-screen bg-background", className)}
      style={{
        minHeight: viewportHeight > 0 ? `${viewportHeight}px` : '100vh'
      }}
    >
      {/* Mobile header */}
      <MobileHeader />

      {/* Desktop breadcrumbs */}
      {showBreadcrumbs && (
        <div className="hidden lg:block border-b bg-muted/30">
          <div className="px-4 py-3">
            <BreadcrumbNavigation
              currentStep={currentStep}
              showStepNumbers={true}
              showCompletionStatus={true}
              allowNavigation={true}
            />
          </div>
        </div>
      )}

      {/* Main content area */}
      <ScrollArea className="flex-1">
        <main className="px-4 py-6">
          <div className="max-w-4xl mx-auto">
            {children}
          </div>
        </main>
      </ScrollArea>
    </div>
  );
}

export default UnifiedLayout;