/**
 * @fileoverview Protocol summary card component with flip animation
 * Shows overview of each recipe protocol with flip interaction
 */

'use client';

import React from 'react';
import { RecipeTimeSlot, FinalRecipeProtocol } from '../types/recipe.types';
import { useI18n } from '@/hooks/use-i18n';
import { getTimeSlotConfig } from '../constants/time-slot-config';

interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
  isFlipped: boolean;
  onFlip: () => void;
}

/**
 * Protocol summary card with flip animation
 * Follows the exact design from standalone-v1.html flip cards
 */
export const ProtocolSummaryCard = React.memo(function ProtocolSummaryCard({ timeSlot, recipe, onViewDetails, isFlipped, onFlip }: ProtocolSummaryCardProps) {
  const { t } = useI18n();

  const config = getTimeSlotConfig(timeSlot);

  const handleFlip = () => {
    onFlip();
  };

  const handleViewRecipe = () => {
    onViewDetails();
  };

  if (!recipe) {
    return (
      <div className="h-96 bg-muted rounded-2xl animate-pulse flex items-center justify-center border border-border">
        <div className="text-muted-foreground">{t('create-recipe:steps.final-recipes.loading')}</div>
      </div>
    );
  }

  return (
    <div className={`h-96 flip-card ${isFlipped ? 'is-flipped' : ''}`}>
      <div className="flip-card-inner relative w-full h-full">
        {/* Front Card */}
        <div className={`flip-card-front bg-gradient-to-br ${config.gradient} p-8 flex flex-col items-center justify-between text-white shadow-2xl ${config.shadowColor}`}>
          <div className="w-full flex flex-col items-start">
            <span className={`text-sm uppercase tracking-widest ${config.accentColor}`}>
              PROTOCOLO
            </span>
            <div className="flex items-center gap-2 mt-2">
              <span className="text-2xl">{config.emoji}</span>
              <h3 className="text-2xl font-black">{recipe.time_of_day_localized}</h3>
            </div>
          </div>

          <div className="flex-1 flex flex-col items-center justify-center w-full mt-4">
            <div className="text-center">
              <div className="synergy-label text-base opacity-90">{t('create-recipe:steps.final-recipes.protocolSummary.synergyFor')}</div>
              <div className="theme text-xl font-bold mb-6">{recipe.recipe_theme_localized}</div>
            </div>
            <button
              onClick={handleFlip}
              className={`
                inline-flex items-center gap-2 px-6 py-2.5 font-semibold rounded-full
                shadow-lg transition-all duration-200 ${config.buttonColor}
              `}
            >
              {t('create-recipe:steps.final-recipes.overview.protocolSummary.viewDetails')}
              <svg xmlns='http://www.w3.org/2000/svg' className='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'>
                <path fillRule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clipRule='evenodd' />
              </svg>
            </button>
          </div>

          <div className={`text-center text-xs ${config.accentColor} mt-4 w-full`}>
            {t('create-recipe:steps.final-recipes.overview.protocolSummary.clickToView')}
          </div>
        </div>

        {/* Back Card */}
        <div className="flip-card-back bg-card p-6 overflow-y-auto border border-border shadow-sm">
          <button
            onClick={handleFlip}
            className="absolute top-4 right-4 text-muted-foreground hover:text-primary text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-primary"
            aria-label={t('create-recipe:steps.final-recipes.overview.protocolSummary.close')}
          >
            &times;
          </button>

          <div className="mt-2">
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.protocolSummary.objective')}:</strong> {recipe.holistic_benefit_localized}
            </div>
          </div>

          <div className="mt-4">
            <ul className="ingredients-list mt-2 space-y-1">
              {recipe.ingredients.essential_oils.map((oil) => (
                <li key={oil.oil_id} className="flex justify-between items-center">
                  <span className="text-primary">● {oil.name_localized}</span>
                  <span className="font-mono text-sm text-muted-foreground">{oil.drops} {t('create-recipe:steps.final-recipes.protocolSummary.drops')}</span>
                </li>
              ))}
            </ul>
          </div>

          <div className="mt-4">
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.protocolSummary.quickPreparation')}:</strong> {recipe.preparation_summary_localized}
            </div>
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.protocolSummary.howToUse')}:</strong> {recipe.usage_summary_localized}
            </div>
          </div>

          <div className="mt-2 text-right">
            <button
              onClick={handleViewRecipe}
              className="text-sm text-primary hover:text-primary/80 hover:underline transition-colors"
            >
              {t('create-recipe:steps.final-recipes.overview.protocolSummary.viewRecipe')} →
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});
