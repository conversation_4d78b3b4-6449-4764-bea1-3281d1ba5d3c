/**
 * @fileoverview Tests to verify debug overlay refactoring with shared utility function
 */

import { enrichOilsForAI } from '../oil-data-enrichment';
import type { TherapeuticProperty } from '../../types/recipe.types';

describe('Debug Overlay Refactoring', () => {
  const mockTherapeuticProperties: TherapeuticProperty[] = [
    {
      property_id: 'prop-1',
      property_name_localized: 'Anti-inflammatory',
      property_name_english: 'Anti-inflammatory',
      description_contextual_localized: 'Reduces inflammation',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 4,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: '<PERSON><PERSON><PERSON>',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Excellent for inflammation',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            },
            dilution_id: 'dilution-1',
            dilution: {
              name: 'Standard dilution',
              description: '2-3%',
              percentage_max: 3,
              percentage_min: 2,
              ratio: '1:50'
            }
          }
        }
      ]
    },
    {
      property_id: 'prop-2',
      property_name_localized: 'Calming',
      property_name_english: 'Calming',
      description_contextual_localized: 'Promotes relaxation',
      addresses_cause_ids: ['cause-2'],
      addresses_symptom_ids: ['symptom-2'],
      relevancy_score: 3,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1', // Same oil in different property context
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Well-known for calming effects',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        }
      ]
    }
  ];

  describe('Utility function integration', () => {
    it('should produce enriched oils data for debug overlay', () => {
      const enrichedOils = enrichOilsForAI(mockTherapeuticProperties);
      
      expect(enrichedOils).toHaveLength(1); // oil-1 appears in both properties but deduplicated
      expect(enrichedOils[0]).toMatchObject({
        oil_id: 'oil-1',
        name_english: 'Lavender',
        name_localized: 'Lavanda',
        name_scientific: 'Lavandula angustifolia'
      });
      
      expect(enrichedOils[0].final_relevance_score).toBeGreaterThan(0);
      expect(enrichedOils[0].properties).toHaveLength(2); // Appears in both properties
      expect(enrichedOils[0].safety).toBeDefined();
    });

    it('should provide data structure compatible with debug overlay minimalData', () => {
      const enrichedOils = enrichOilsForAI(mockTherapeuticProperties);
      
      // Simulate the minimalData structure that debug overlay creates
      const minimalData = {
        language: 'en',
        healthConcern: { healthConcern: 'Test concern' },
        demographics: { gender: 'female', ageCategory: 'adult', specificAge: 30 },
        selectedCauses: [],
        selectedSymptoms: [],
        oils: enrichedOils,
        therapeuticProperties: mockTherapeuticProperties.map(prop => ({
          property_id: prop.property_id,
          property_name_localized: prop.property_name_localized,
          property_name_english: prop.property_name_english,
          description_contextual_localized: prop.description_contextual_localized,
          addresses_cause_ids: prop.addresses_cause_ids,
          addresses_symptom_ids: prop.addresses_symptom_ids,
          relevancy_score: prop.relevancy_score
        }))
      };

      expect(minimalData.oils).toHaveLength(1);
      expect(minimalData.oils[0]).toHaveProperty('oil_id');
      expect(minimalData.oils[0]).toHaveProperty('final_relevance_score');
      expect(minimalData.oils[0]).toHaveProperty('properties');
      expect(minimalData.oils[0]).toHaveProperty('safety');
      expect(minimalData.therapeuticProperties).toHaveLength(2);
    });

    it('should provide data structure compatible with generateSolution1Data', () => {
      const enrichedOils = enrichOilsForAI(mockTherapeuticProperties);
      
      // Simulate the generateSolution1Data transformation
      const solution1Data = {
        lang: 'en',
        health: { healthConcern: 'Test concern' },
        demo: { gender: 'female', ageCategory: 'adult', specificAge: 30 },
        causes: [],
        symptoms: [],
        props: mockTherapeuticProperties.map((prop: any) => ({
          id: prop.property_id,
          name: prop.property_name_localized,
          en: prop.property_name_english,
          desc: prop.description_contextual_localized,
          cause_ids: prop.addresses_cause_ids,
          symptom_ids: prop.addresses_symptom_ids,
          score: prop.relevancy_score
        })),
        oils: enrichedOils.map((oil: any) => ({
          id: oil.oil_id,
          name: oil.name_localized,
          en: oil.name_english,
          sci: oil.name_scientific,
          props: (oil.properties || []).map((p: any) => ({
            pid: p.property_id,
            score: p.relevancy_to_property_score,
            inst: p.recommendation_instance_score,
            rationale: p.match_rationale_localized
          })),
          final: oil.final_relevance_score,
          safety: oil.safety
        }))
      };

      expect(solution1Data.oils).toHaveLength(1);
      expect(solution1Data.oils[0]).toMatchObject({
        id: 'oil-1',
        name: 'Lavanda',
        en: 'Lavender',
        sci: 'Lavandula angustifolia'
      });
      expect(solution1Data.oils[0].props).toHaveLength(2);
      expect(solution1Data.oils[0].final).toBeGreaterThan(0);
    });

    it('should provide data structure compatible with generateSolution4Data', () => {
      const enrichedOils = enrichOilsForAI(mockTherapeuticProperties);
      
      // Simulate the generateSolution4Data transformation
      const oilsObj: Record<string, any> = {};
      const matchesObj: Record<string, any> = {};
      
      enrichedOils.forEach((oil: any) => {
        oilsObj[oil.oil_id] = {
          id: oil.oil_id,
          name: oil.name_localized,
          en: oil.name_english,
          sci: oil.name_scientific,
          final: oil.final_relevance_score,
          safety: oil.safety
        };
        
        (oil.properties || []).forEach((prop: any, idx: number) => {
          const matchId = `${oil.oil_id}_${prop.property_id}`;
          matchesObj[matchId] = {
            oil_id: oil.oil_id,
            property_id: prop.property_id,
            score: prop.relevancy_to_property_score,
            inst: prop.recommendation_instance_score,
            rationale: prop.match_rationale_localized
          };
        });
      });

      const solution4Data = {
        oils: oilsObj,
        matches: matchesObj
      };

      expect(Object.keys(solution4Data.oils)).toHaveLength(1);
      expect(solution4Data.oils['oil-1']).toBeDefined();
      expect(Object.keys(solution4Data.matches)).toHaveLength(2); // 2 property matches
      expect(solution4Data.matches['oil-1_prop-1']).toBeDefined();
      expect(solution4Data.matches['oil-1_prop-2']).toBeDefined();
    });
  });

  describe('Data consistency verification', () => {
    it('should maintain identical output structure after refactoring', () => {
      const enrichedOils = enrichOilsForAI(mockTherapeuticProperties);
      
      // Verify the structure matches what the debug overlay expects
      enrichedOils.forEach(oil => {
        expect(oil).toHaveProperty('oil_id');
        expect(oil).toHaveProperty('name_english');
        expect(oil).toHaveProperty('name_localized');
        expect(oil).toHaveProperty('name_scientific');
        expect(oil).toHaveProperty('final_relevance_score');
        expect(oil).toHaveProperty('properties');
        expect(oil).toHaveProperty('safety');
        
        expect(Array.isArray(oil.properties)).toBe(true);
        expect(typeof oil.final_relevance_score).toBe('number');
        expect(typeof oil.safety).toBe('object');
        
        oil.properties.forEach((prop: any) => {
          expect(prop).toHaveProperty('property_id');
          expect(prop).toHaveProperty('match_rationale_localized');
          expect(prop).toHaveProperty('relevancy_to_property_score');
          expect(prop).toHaveProperty('recommendation_instance_score');
        });
      });
    });

    it('should handle empty therapeutic properties gracefully', () => {
      const enrichedOils = enrichOilsForAI([]);
      
      expect(enrichedOils).toEqual([]);
      
      // Should work with debug overlay structure
      const minimalData = {
        language: 'en',
        healthConcern: { healthConcern: 'Test concern' },
        demographics: { gender: 'female', ageCategory: 'adult', specificAge: 30 },
        selectedCauses: [],
        selectedSymptoms: [],
        oils: enrichedOils,
        therapeuticProperties: []
      };

      expect(minimalData.oils).toEqual([]);
      expect(minimalData.therapeuticProperties).toEqual([]);
    });
  });
});