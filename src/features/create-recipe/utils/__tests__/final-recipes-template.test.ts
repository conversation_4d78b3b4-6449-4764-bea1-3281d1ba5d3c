/**
 * @fileoverview Tests for final-recipes template processing with enhanced oil data
 */

import { PromptManager } from '@/lib/ai/utils/prompt-manager';

describe('Final Recipes Template Processing', () => {
  const promptManager = PromptManager.getInstance();
  
  const mockTemplateData = {
    health_concern: 'Stress and anxiety',
    gender: 'female',
    age_category: 'adult',
    age_specific: '30',
    user_language: 'en',
    selected_causes: [
      {
        cause_id: 'cause-1',
        cause_name: 'Work stress',
        cause_suggestion: 'High workload',
        explanation: 'Excessive work demands causing stress'
      }
    ],
    selected_symptoms: [
      {
        symptom_id: 'symptom-1',
        symptom_name: 'Anxiety',
        symptom_suggestion: 'Feeling anxious',
        explanation: 'General anxiety symptoms'
      }
    ],
    suggested_oils: [
      {
        oil_id: 'oil-1',
        name_english: 'Lavender',
        name_localized: '<PERSON><PERSON><PERSON>',
        name_scientific: '<PERSON><PERSON><PERSON><PERSON> angustifolia',
        final_relevance_score: 25.5,
        safety: {
          internal_use: 'internal-1',
          dilution: 'dilution-1',
          phototoxicity: null,
          pregnancy_nursing: ['preg-1'],
          child_safety: []
        },
        properties: [
          {
            property_id: 'prop-1',
            match_rationale_localized: 'Excellent for calming and reducing stress',
            relevancy_to_property_score: 5,
            recommendation_instance_score: 16.0
          },
          {
            property_id: 'prop-2',
            match_rationale_localized: 'Proven anti-anxiety effects',
            relevancy_to_property_score: 5,
            recommendation_instance_score: 9.5
          }
        ],
        properties_formatted: `       - Property ID: prop-1
       - Match Rationale: Excellent for calming and reducing stress
       - Property Relevancy Score: 5/5
       - Recommendation Instance Score: 16
       ---
       - Property ID: prop-2
       - Match Rationale: Proven anti-anxiety effects
       - Property Relevancy Score: 5/5
       - Recommendation Instance Score: 9.5`
      },
      {
        oil_id: 'oil-2',
        name_english: 'Bergamot',
        name_localized: 'Bergamota',
        name_scientific: 'Citrus bergamia',
        final_relevance_score: 15.0,
        safety: {
          internal_use: null,
          dilution: null,
          phototoxicity: 'photo-1',
          pregnancy_nursing: [],
          child_safety: []
        },
        properties: [
          {
            property_id: 'prop-2',
            match_rationale_localized: 'Uplifting citrus oil for anxiety relief',
            relevancy_to_property_score: 3,
            recommendation_instance_score: 15.0
          }
        ],
        properties_formatted: `       - Property ID: prop-2
       - Match Rationale: Uplifting citrus oil for anxiety relief
       - Property Relevancy Score: 3/5
       - Recommendation Instance Score: 15`
      }
    ],
    time_of_day: 'morning'
  };

  describe('Enhanced oil data rendering', () => {
    it('should process template with enhanced oil data structure', async () => {
      const result = await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(result).toBeDefined();
      expect(result.userMessage).toBeDefined();
      expect(result.userMessage).toContain('suggested_oils');
    });

    it('should render final relevance scores', async () => {
      const result = await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(result.userMessage).toContain('Final Relevance Score: 25.5');
      expect(result.userMessage).toContain('Final Relevance Score: 15');
    });

    it('should render formatted property contexts correctly', async () => {
      const result = await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // Should contain formatted property data, not raw template syntax
      expect(result.userMessage).toContain('Therapeutic Property Contexts');
      expect(result.userMessage).toContain('Property ID: prop-1');
      expect(result.userMessage).toContain('Match Rationale: Excellent for calming and reducing stress');
      expect(result.userMessage).toContain('Property Relevancy Score: 5/5');
      expect(result.userMessage).toContain('Recommendation Instance Score: 16');
      expect(result.userMessage).toContain('Property ID: prop-2');
      expect(result.userMessage).toContain('Match Rationale: Proven anti-anxiety effects');
      expect(result.userMessage).toContain('---'); // Separator between properties
      
      // Should NOT contain raw template syntax
      expect(result.userMessage).not.toContain('{{#each properties}}');
      expect(result.userMessage).not.toContain('{{property_id}}');
      expect(result.userMessage).not.toContain('{{match_rationale_localized}}');
    });

    it('should render scoring data correctly', async () => {
      const result = await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // For now, just verify the template processes and contains the structure
      expect(result.userMessage).toContain('Final Relevance Score');
      expect(result.userMessage).toContain('Therapeutic Property Contexts');
    });

    it('should handle edge cases gracefully', async () => {
      const edgeCaseData = {
        ...mockTemplateData,
        suggested_oils: [
          {
            oil_id: 'oil-edge',
            name_english: 'Edge Case Oil',
            name_localized: 'Edge Case Oil',
            name_scientific: 'Edge scientific',
            final_relevance_score: 5.0,
            safety: {
              internal_use: null,
              dilution: null,
              phototoxicity: null,
              pregnancy_nursing: [],
              child_safety: []
            },
            properties: [
              {
                property_id: 'prop-edge',
                match_rationale_localized: null,
                relevancy_to_property_score: null,
                recommendation_instance_score: 0
              }
            ],
            properties_formatted: `       - Property ID: prop-edge
       - Match Rationale: 
       - Property Relevancy Score: /5
       - Recommendation Instance Score: 0`
          }
        ]
      };

      const result = await promptManager.getProcessedPrompt('final-recipes', edgeCaseData);
      expect(result.userMessage).toContain('Oil ID: oil-edge');
      expect(result.userMessage).toContain('Final Relevance Score: 5');
      expect(result.userMessage).toContain('Property ID: prop-edge');
      expect(result.userMessage).toContain('Recommendation Instance Score: 0');
    });

    it('should use properties_formatted field instead of nested loops', async () => {
      const result = await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // Should contain the formatted properties string content
      expect(result.userMessage).toContain('Property ID: prop-1');
      expect(result.userMessage).toContain('Property ID: prop-2');
      expect(result.userMessage).toContain('Match Rationale: Excellent for calming and reducing stress');
      expect(result.userMessage).toContain('Match Rationale: Proven anti-anxiety effects');
      expect(result.userMessage).toContain('Property Relevancy Score: 5/5');
      expect(result.userMessage).toContain('Recommendation Instance Score: 16');
      expect(result.userMessage).toContain('Recommendation Instance Score: 9.5');
      
      // Should contain separator between properties for the first oil
      expect(result.userMessage).toContain('---');
      
      // Should NOT contain any handlebars template syntax
      expect(result.userMessage).not.toContain('{{#each');
      expect(result.userMessage).not.toContain('{{property_id}}');
      expect(result.userMessage).not.toContain('{{match_rationale_localized}}');
      expect(result.userMessage).not.toContain('{{relevancy_to_property_score}}');
      expect(result.userMessage).not.toContain('{{recommendation_instance_score}}');
      expect(result.userMessage).not.toContain('{{#unless @last}}');
      
      // Should NOT contain NaN or missing values
      expect(result.userMessage).not.toContain('NaN');
      expect(result.userMessage).not.toContain('Final Relevance Score: <-');
      expect(result.userMessage).not.toContain('Recommendation Instance Score: NaN');
      
      // Should contain actual scores
      expect(result.userMessage).toContain('Final Relevance Score: 25.5');
      expect(result.userMessage).toContain('Final Relevance Score: 15');
    });
  });
});