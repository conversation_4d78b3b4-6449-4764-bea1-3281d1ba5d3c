/**
 * @fileoverview Tests for data flow verification utilities
 */

import { 
  verifyDataFlowConsistency, 
  logAIDataConfirmation, 
  compareWithDebugOverlayFormat,
  performComprehensiveDataFlowVerification 
} from '../data-flow-verification';
import type { TherapeuticProperty } from '../../types/recipe.types';

describe('Data Flow Verification', () => {
  const mockTherapeuticProperties: TherapeuticProperty[] = [
    {
      property_id: 'prop-1',
      property_name_localized: 'Anti-inflammatory',
      property_name_english: 'Anti-inflammatory',
      description_contextual_localized: 'Reduces inflammation',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 4,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: '<PERSON><PERSON><PERSON>',
          name_scientific: '<PERSON><PERSON><PERSON>la angustifolia',
          match_rationale_localized: 'Excellent for inflammation',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        }
      ]
    }
  ];

  beforeEach(() => {
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('verifyDataFlowConsistency', () => {
    it('should verify consistent data flow with valid input', () => {
      const result = verifyDataFlowConsistency(mockTherapeuticProperties, 'test-context');
      
      expect(result.isConsistent).toBe(true);
      expect(result.issues).toHaveLength(0);
      expect(result.utilityOutput).toHaveLength(1);
      expect(result.summary.oil_count).toBe(1);
      expect(result.summary.has_scores).toBe(true);
      expect(result.summary.has_safety_data).toBe(true);
    });

    it('should detect issues with invalid data', () => {
      // Create data that will actually produce invalid output
      const invalidProperties: TherapeuticProperty[] = [
        {
          ...mockTherapeuticProperties[0],
          suggested_oils: [
            {
              ...mockTherapeuticProperties[0].suggested_oils![0],
              oil_id: '', // This will be filtered out by enrichment utility
            }
          ]
        }
      ];

      const result = verifyDataFlowConsistency(invalidProperties, 'invalid-test');
      
      // Since empty oil_id gets filtered out, we should have 0 oils
      expect(result.utilityOutput).toHaveLength(0);
      expect(result.isConsistent).toBe(true); // No oils means no issues
    });

    it('should handle empty therapeutic properties', () => {
      const result = verifyDataFlowConsistency([], 'empty-test');
      
      expect(result.isConsistent).toBe(true);
      expect(result.utilityOutput).toHaveLength(0);
      expect(result.summary.oil_count).toBe(0);
    });

    it('should handle errors gracefully', () => {
      // Pass invalid input to trigger error
      const result = verifyDataFlowConsistency(null as any, 'error-test');
      
      expect(result.isConsistent).toBe(false);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.utilityOutput).toEqual([]);
    });
  });

  describe('logAIDataConfirmation', () => {
    it('should log AI data confirmation with complete data', () => {
      const mockEnrichedOils = [
        {
          oil_id: 'oil-1',
          name_localized: 'Lavanda',
          final_relevance_score: 25.5,
          properties: [
            {
              property_id: 'prop-1',
              match_rationale_localized: 'Great for inflammation',
              relevancy_to_property_score: 5,
              recommendation_instance_score: 16.0
            }
          ],
          safety: {
            internal_use: 'safe-id',
            dilution: null,
            phototoxicity: null,
            pregnancy_nursing: [],
            child_safety: []
          }
        }
      ];

      logAIDataConfirmation(mockEnrichedOils, 'test-ai-context');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🤖 [AI-DATA-CONFIRMATION] test-ai-context'),
        expect.objectContaining({
          total_oils: 1,
          oils_with_final_relevance_score: 1,
          oils_with_property_contexts: 1,
          total_property_contexts: 1,
          has_complete_scoring_data: true,
          has_property_specific_data: true
        })
      );

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('✅ [AI-READY] test-ai-context')
      );
    });

    it('should warn about incomplete data', () => {
      const incompleteOils = [
        {
          oil_id: 'oil-1',
          name_localized: 'Lavanda',
          final_relevance_score: null, // Missing score
          properties: [], // No properties
          safety: {}
        }
      ];

      logAIDataConfirmation(incompleteOils, 'incomplete-test');

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [AI-WARNING] incomplete-test'),
        expect.objectContaining({
          missing_scores: true,
          missing_property_contexts: true
        })
      );
    });

    it('should handle empty oils array', () => {
      logAIDataConfirmation([], 'empty-test');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🤖 [AI-DATA-CONFIRMATION] empty-test'),
        expect.objectContaining({
          total_oils: 0,
          sample_oil_data: null
        })
      );
    });
  });

  describe('compareWithDebugOverlayFormat', () => {
    it('should validate compatible format', () => {
      const validOils = [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          final_relevance_score: 25.5,
          properties: [
            {
              property_id: 'prop-1',
              match_rationale_localized: 'Great for inflammation',
              relevancy_to_property_score: 5,
              recommendation_instance_score: 16.0
            }
          ],
          safety: {
            internal_use: 'safe-id',
            dilution: null,
            phototoxicity: null,
            pregnancy_nursing: [],
            child_safety: []
          }
        }
      ];

      const result = compareWithDebugOverlayFormat(validOils, 'valid-test');

      expect(result.isCompatible).toBe(true);
      expect(result.compatibilityIssues).toHaveLength(0);
      expect(result.formatAnalysis.has_required_fields).toBe(true);
      expect(result.formatAnalysis.has_correct_types).toBe(true);
      expect(result.formatAnalysis.has_property_contexts).toBe(true);
      expect(result.formatAnalysis.has_safety_references).toBe(true);
    });

    it('should detect format incompatibilities', () => {
      const invalidOils = [
        {
          oil_id: 'oil-1',
          // Missing required fields
          final_relevance_score: 'invalid', // Wrong type
          properties: 'not-array', // Wrong type
          safety: null // Wrong type
        }
      ];

      const result = compareWithDebugOverlayFormat(invalidOils, 'invalid-test');

      expect(result.isCompatible).toBe(false);
      expect(result.compatibilityIssues.length).toBeGreaterThan(0);
      expect(result.formatAnalysis.has_required_fields).toBe(false);
      expect(result.formatAnalysis.has_correct_types).toBe(false);
    });

    it('should handle empty array', () => {
      const result = compareWithDebugOverlayFormat([], 'empty-test');

      expect(result.isCompatible).toBe(true);
      expect(result.compatibilityIssues).toHaveLength(0);
    });
  });

  describe('performComprehensiveDataFlowVerification', () => {
    it('should perform comprehensive verification successfully', () => {
      const result = performComprehensiveDataFlowVerification(
        mockTherapeuticProperties, 
        'comprehensive-test'
      );

      expect(result.overall_success).toBe(true);
      expect(result.verification_result.isConsistent).toBe(true);
      expect(result.compatibility_result.isCompatible).toBe(true);
      expect(result.ai_data_ready).toBe(true);
    });

    it('should detect comprehensive failures', () => {
      // Test with properties that will result in no oils (empty oil_id gets filtered)
      const invalidProperties: TherapeuticProperty[] = [
        {
          ...mockTherapeuticProperties[0],
          suggested_oils: [
            {
              ...mockTherapeuticProperties[0].suggested_oils![0],
              oil_id: '', // This will be filtered out
            }
          ]
        }
      ];

      const result = performComprehensiveDataFlowVerification(
        invalidProperties, 
        'failure-test'
      );

      expect(result.overall_success).toBe(true); // No issues with empty result
      expect(result.ai_data_ready).toBe(false); // But no data means not ready
    });

    it('should handle empty input', () => {
      const result = performComprehensiveDataFlowVerification([], 'empty-comprehensive');

      expect(result.overall_success).toBe(true);
      expect(result.verification_result.isConsistent).toBe(true);
      expect(result.compatibility_result.isCompatible).toBe(true);
      expect(result.ai_data_ready).toBe(false); // No oils means not ready for AI
    });
  });

  describe('Logging verification', () => {
    it('should log appropriate success messages', () => {
      verifyDataFlowConsistency(mockTherapeuticProperties, 'success-test');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('✅ [SUCCESS] Data flow verification passed (success-test)')
      );
    });

    it('should log appropriate warning messages', () => {
      // Test with data that will actually produce warnings
      const incompleteOils = [
        {
          oil_id: 'oil-1',
          name_localized: 'Lavanda',
          final_relevance_score: null, // This will cause AI warning
          properties: [],
          safety: {}
        }
      ];

      logAIDataConfirmation(incompleteOils, 'warning-test');

      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('⚠️ [AI-WARNING] warning-test'),
        expect.any(Object)
      );
    });

    it('should log comprehensive verification summary', () => {
      performComprehensiveDataFlowVerification(mockTherapeuticProperties, 'summary-test');

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🔍 [DEBUG] Starting comprehensive data flow verification (summary-test)')
      );

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🔍 [DEBUG] Comprehensive verification summary (summary-test)'),
        expect.objectContaining({
          overall_success: expect.any(Boolean),
          data_flow_consistent: expect.any(Boolean),
          debug_overlay_compatible: expect.any(Boolean),
          ai_data_ready: expect.any(Boolean)
        })
      );
    });
  });
});