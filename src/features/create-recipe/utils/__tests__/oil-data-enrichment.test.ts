/**
 * @fileoverview Tests for oil data enrichment utility
 * Comprehensive tests to verify utility function produces identical output to debug overlay
 */

import { enrichOilsForAI, formatPropertiesForTemplate, filterPropertiesForAI } from '../oil-data-enrichment';
import type { TherapeuticProperty } from '../../types/recipe.types';
import {
  enrichRecommendationDataWithScores,
  calculateFinalRankedList
} from '../recommendation-scoring';

describe('enrichOilsForAI', () => {
  // Mock data that matches real-world structure
  const mockTherapeuticProperties: TherapeuticProperty[] = [
    {
      property_id: 'prop-1',
      property_name_localized: 'Anti-inflammatory',
      property_name_english: 'Anti-inflammatory',
      description_contextual_localized: 'Reduces inflammation',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 4,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_botanical: '<PERSON><PERSON><PERSON>la angustifolia',
          name_localized: '<PERSON><PERSON><PERSON>',
          name_scientific: '<PERSON><PERSON>dula angustifolia',
          match_rationale_localized: 'Great for inflammation',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            },
            dilution_id: 'dilution-1',
            dilution: {
              name: 'Standard dilution',
              description: '2-3%',
              percentage_max: 3,
              percentage_min: 2,
              ratio: '1:50'
            },
            phototoxicity_id: 'photo-1',
            phototoxicity: {
              status: 'Non-phototoxic',
              guidance: 'Safe for sun exposure',
              description: 'No phototoxic effects'
            },
            pregnancy_nursing: [
              {
                id: 'preg-1',
                name: 'Safe during pregnancy',
                status_description: 'Generally safe',
                code: 'SAFE',
                usage_guidance: 'Use as directed',
                description: 'Safe for pregnant women'
              }
            ],
            child_safety: [
              {
                age_range_id: 'child-1',
                age_range: '2-12 years',
                safety_notes: 'Safe for children over 2'
              }
            ]
          }
        },
        {
          oil_id: 'oil-2',
          name_english: 'Chamomile',
          name_botanical: 'Matricaria chamomilla',
          name_localized: 'Camomila',
          name_scientific: 'Matricaria chamomilla',
          match_rationale_localized: 'Excellent anti-inflammatory properties',
          relevancy_to_property_score: 4,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-2',
            internal_use: {
              name: 'Caution for internal use',
              code: 'CAUTION',
              description: 'Use with caution',
              guidance: 'Consult professional'
            }
          }
        }
      ]
    },
    {
      property_id: 'prop-2',
      property_name_localized: 'Calming',
      property_name_english: 'Calming',
      description_contextual_localized: 'Promotes relaxation',
      addresses_cause_ids: ['cause-2'],
      addresses_symptom_ids: ['symptom-2'],
      relevancy_score: 3,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1', // Same oil in different property context
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Well-known for calming effects',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        }
      ]
    }
  ];

  describe('Basic functionality', () => {
    it('should enrich oils with final relevance score and properties', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      expect(result).toHaveLength(2); // oil-1 and oil-2

      const firstOil = result[0]!; // Non-null assertion since we know it exists
      expect(firstOil).toMatchObject({
        oil_id: 'oil-1',
        name_english: 'Lavender',
        name_localized: 'Lavanda',
        name_scientific: 'Lavandula angustifolia'
      });

      expect(firstOil.final_relevance_score).toBeGreaterThan(0);
      expect(firstOil.properties).toHaveLength(2); // Appears in both properties
      expect(firstOil.properties_formatted).toBeDefined();
      expect(typeof firstOil.properties_formatted).toBe('string');
    });

    it('should handle empty therapeutic properties', () => {
      const result = enrichOilsForAI([]);
      expect(result).toEqual([]);
    });

    it('should process safety data references', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      const firstOil = result[0];
      expect(firstOil).toBeDefined();
      expect(firstOil.safety).toBeDefined();
      expect(firstOil.safety.internal_use).toBeTruthy();
      expect(firstOil.safety.dilution).toBeTruthy();
      expect(firstOil.safety.phototoxicity).toBeTruthy();
      expect(firstOil.safety.pregnancy_nursing).toHaveLength(1);
      expect(firstOil.safety.child_safety).toHaveLength(1);
    });
  });

  describe('Debug overlay compatibility', () => {
    it('should produce identical final_relevance_score calculations', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      // Manually calculate expected scores using the same logic as debug overlay
      const oilMap = new Map();
      mockTherapeuticProperties.forEach(prop => {
        (prop.suggested_oils || []).forEach(oil => {
          if (oil?.oil_id && !oilMap.has(oil.oil_id)) {
            const { isEnriched, enrichment_status, botanical_mismatch, similarity_score, search_query, enrichment_timestamp, name_botanical, match_rationale_localized, relevancy_to_property_score, ...oilRest } = oil;
            oilMap.set(oil.oil_id, { ...oilRest });
          }
        });
      });

      const recommendationData = {
        therapeuticProperties: mockTherapeuticProperties,
        oils: Array.from(oilMap.values())
      };

      const enrichedData = enrichRecommendationDataWithScores(recommendationData);
      const finalRankedOils = calculateFinalRankedList(enrichedData);

      const oilScoreMap = new Map();
      finalRankedOils.forEach(oil => {
        oilScoreMap.set(oil.oil_id, oil.final_relevance_score);
      });

      // Verify scores match
      result.forEach(oil => {
        const expectedScore = oilScoreMap.get(oil.oil_id);
        expect(oil.final_relevance_score).toBe(expectedScore);
      });
    });

    it('should produce identical properties arrays with correct scores', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      // Find lavender oil which appears in both properties
      const lavenderOil = result.find(oil => oil.oil_id === 'oil-1');
      expect(lavenderOil).toBeDefined();
      expect(lavenderOil!.properties).toHaveLength(2);

      // Verify property contexts match expected calculations
      const prop1Context = lavenderOil!.properties.find(p => p.property_id === 'prop-1');
      const prop2Context = lavenderOil!.properties.find(p => p.property_id === 'prop-2');

      expect(prop1Context).toMatchObject({
        property_id: 'prop-1',
        match_rationale_localized: 'Great for inflammation',
        relevancy_to_property_score: 5,
        recommendation_instance_score: expect.any(Number)
      });

      expect(prop2Context).toMatchObject({
        property_id: 'prop-2',
        match_rationale_localized: 'Well-known for calming effects',
        relevancy_to_property_score: 5,
        recommendation_instance_score: expect.any(Number)
      });

      // Verify recommendation_instance_score calculations
      // Formula: relevancy_to_property_score × relevancy_score × property_coverage_score
      const expectedProp1Score = Math.round((5 * 4 * (4 / 5)) * 100) / 100; // 16
      const expectedProp2Score = Math.round((5 * 3 * (3 / 5)) * 100) / 100; // 9

      expect(prop1Context!.recommendation_instance_score).toBe(expectedProp1Score);
      expect(prop2Context!.recommendation_instance_score).toBe(expectedProp2Score);
    });

    it('should handle safety data processing with actual Supabase UUIDs', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      const lavenderOil = result.find(oil => oil.oil_id === 'oil-1');
      expect(lavenderOil!.safety).toMatchObject({
        internal_use: 'internal-1',
        dilution: 'dilution-1',
        phototoxicity: 'photo-1',
        pregnancy_nursing: ['preg-1'],
        child_safety: ['child-1']
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle missing properties gracefully', () => {
      const propsWithMissingData: TherapeuticProperty[] = [
        {
          property_id: 'prop-1',
          property_name_localized: 'Test Property',
          property_name_english: 'Test Property',
          description_contextual_localized: 'Test description',
          addresses_cause_ids: [],
          addresses_symptom_ids: [],
          relevancy_score: 3,
          isEnriched: true,
          suggested_oils: [] // Empty oils array
        }
      ];

      const result = enrichOilsForAI(propsWithMissingData);
      expect(result).toEqual([]);
    });

    it('should handle null values in oil data', () => {
      const propsWithNullValues: TherapeuticProperty[] = [
        {
          property_id: 'prop-1',
          property_name_localized: 'Test Property',
          property_name_english: 'Test Property',
          description_contextual_localized: 'Test description',
          addresses_cause_ids: [],
          addresses_symptom_ids: [],
          relevancy_score: 3,
          isEnriched: true,
          suggested_oils: [
            {
              oil_id: 'oil-1',
              name_english: 'Test Oil',
              name_botanical: 'Test botanical',
              name_localized: 'Test Oil',
              match_rationale_localized: null as any, // Null rationale
              relevancy_to_property_score: null as any, // Null score
              isEnriched: true,
              enrichment_status: 'enriched' as const
            }
          ]
        }
      ];

      const result = enrichOilsForAI(propsWithNullValues);
      expect(result).toHaveLength(1);

      const firstOil = result[0];
      expect(firstOil).toBeDefined();
      expect(firstOil.properties).toHaveLength(1);

      const firstProperty = firstOil.properties[0]!; // Non-null assertion since we know it exists
      expect(firstProperty).toMatchObject({
        property_id: 'prop-1',
        match_rationale_localized: null,
        relevancy_to_property_score: null,
        recommendation_instance_score: 0 // Should calculate to 0 with null score
      });
    });

    it('should handle empty arrays in safety data', () => {
      const propsWithEmptySafety: TherapeuticProperty[] = [
        {
          property_id: 'prop-1',
          property_name_localized: 'Test Property',
          property_name_english: 'Test Property',
          description_contextual_localized: 'Test description',
          addresses_cause_ids: [],
          addresses_symptom_ids: [],
          relevancy_score: 3,
          isEnriched: true,
          suggested_oils: [
            {
              oil_id: 'oil-1',
              name_english: 'Test Oil',
              name_botanical: 'Test botanical',
              name_localized: 'Test Oil',
              match_rationale_localized: 'Test rationale',
              relevancy_to_property_score: 3,
              isEnriched: true,
              enrichment_status: 'enriched' as const,
              safety: {
                pregnancy_nursing: [], // Empty array
                child_safety: [] // Empty array
              }
            }
          ]
        }
      ];

      const result = enrichOilsForAI(propsWithEmptySafety);
      const firstOil = result[0];
      expect(firstOil).toBeDefined();
      expect(firstOil.safety).toMatchObject({
        internal_use: null,
        dilution: null,
        phototoxicity: null,
        pregnancy_nursing: [],
        child_safety: []
      });
    });
  });

  describe('Data structure consistency', () => {
    it('should remove unwanted fields from oil objects', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      result.forEach(oil => {
        // These fields should be removed
        expect(oil).not.toHaveProperty('isEnriched');
        expect(oil).not.toHaveProperty('enrichment_status');
        expect(oil).not.toHaveProperty('botanical_mismatch');
        expect(oil).not.toHaveProperty('similarity_score');
        expect(oil).not.toHaveProperty('search_query');
        expect(oil).not.toHaveProperty('enrichment_timestamp');
        expect(oil).not.toHaveProperty('name_botanical');
        expect(oil).not.toHaveProperty('match_rationale_localized');
        expect(oil).not.toHaveProperty('relevancy_to_property_score');

        // These fields should be present
        expect(oil).toHaveProperty('oil_id');
        expect(oil).toHaveProperty('name_english');
        expect(oil).toHaveProperty('name_localized');
        expect(oil).toHaveProperty('final_relevance_score');
        expect(oil).toHaveProperty('properties');
        expect(oil).toHaveProperty('properties_formatted');
        expect(oil).toHaveProperty('safety');
      });
    });

    it('should maintain consistent property context structure', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      result.forEach(oil => {
        oil.properties.forEach(property => {
          expect(property).toHaveProperty('property_id');
          expect(property).toHaveProperty('match_rationale_localized');
          expect(property).toHaveProperty('relevancy_to_property_score');
          expect(property).toHaveProperty('recommendation_instance_score');

          expect(typeof property.property_id).toBe('string');
          expect(typeof property.recommendation_instance_score).toBe('number');
        });
      });
    });

    it('should include formatted properties string in enriched oil data', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      const lavenderOil = result.find(oil => oil.oil_id === 'oil-1');
      expect(lavenderOil).toBeDefined();
      expect(lavenderOil!.properties_formatted).toBeDefined();
      expect(typeof lavenderOil!.properties_formatted).toBe('string');

      // Should contain formatted property data
      expect(lavenderOil!.properties_formatted).toContain('Property ID: prop-1');
      expect(lavenderOil!.properties_formatted).toContain('Property ID: prop-2');
      expect(lavenderOil!.properties_formatted).toContain('Match Rationale: Great for inflammation');
      expect(lavenderOil!.properties_formatted).toContain('Match Rationale: Well-known for calming effects');
      expect(lavenderOil!.properties_formatted).toContain('---'); // Separator between properties

      // Should not contain raw template syntax
      expect(lavenderOil!.properties_formatted).not.toContain('{{#each');
      expect(lavenderOil!.properties_formatted).not.toContain('{{property_id}}');
    });

    it('should have valid final_relevance_score and recommendation_instance_score values', () => {
      const result = enrichOilsForAI(mockTherapeuticProperties);

      result.forEach(oil => {
        // final_relevance_score should be a valid number, not null or undefined
        expect(oil.final_relevance_score).toBeDefined();
        expect(typeof oil.final_relevance_score).toBe('number');
        expect(oil.final_relevance_score).toBeGreaterThanOrEqual(0);

        // Each property should have valid recommendation_instance_score
        oil.properties.forEach(property => {
          expect(property.recommendation_instance_score).toBeDefined();
          expect(typeof property.recommendation_instance_score).toBe('number');
          expect(property.recommendation_instance_score).not.toBeNaN();
          expect(property.recommendation_instance_score).toBeGreaterThanOrEqual(0);
        });

        // properties_formatted should not contain NaN or empty scores
        expect(oil.properties_formatted).not.toContain('NaN');
        expect(oil.properties_formatted).not.toContain('Recommendation Instance Score: NaN');
        expect(oil.properties_formatted).not.toContain('Recommendation Instance Score: undefined');
        expect(oil.properties_formatted).not.toContain('Recommendation Instance Score: null');
      });
    });
  });
});

describe('filterPropertiesForAI', () => {
  it('should filter properties to only include enriched ones with suggested oils', () => {
    const properties = [
      {
        property_id: 'prop-1',
        property_name_localized: 'Property 1',
        property_name_english: 'Property 1',
        description_contextual_localized: 'Description 1',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 5,
        isEnriched: true,
        suggested_oils: [{ oil_id: 'oil-1' }]
      },
      {
        property_id: 'prop-2',
        property_name_localized: 'Property 2',
        property_name_english: 'Property 2',
        description_contextual_localized: 'Description 2',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 4,
        isEnriched: false, // Not enriched
        suggested_oils: [{ oil_id: 'oil-2' }]
      },
      {
        property_id: 'prop-3',
        property_name_localized: 'Property 3',
        property_name_english: 'Property 3',
        description_contextual_localized: 'Description 3',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 3,
        isEnriched: true,
        suggested_oils: [] // No suggested oils
      },
      {
        property_id: 'prop-4',
        property_name_localized: 'Property 4',
        property_name_english: 'Property 4',
        description_contextual_localized: 'Description 4',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 5,
        isEnriched: true,
        suggested_oils: [{ oil_id: 'oil-3' }]
      }
    ] as any[];

    const result = filterPropertiesForAI(properties);

    expect(result).toHaveLength(2); // Only first and last should pass
    expect(result[0].property_id).toBe('prop-1');
    expect(result[0].isEnriched).toBe(true);
    expect(result[0].suggested_oils).toHaveLength(1);
    expect(result[1].property_id).toBe('prop-4');
    expect(result[1].isEnriched).toBe(true);
    expect(result[1].suggested_oils).toHaveLength(1);
  });

  it('should return empty array when no properties meet criteria', () => {
    const properties = [
      {
        property_id: 'prop-1',
        property_name_localized: 'Property 1',
        property_name_english: 'Property 1',
        description_contextual_localized: 'Description 1',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 5,
        isEnriched: false,
        suggested_oils: []
      }
    ] as any[];

    const result = filterPropertiesForAI(properties);
    expect(result).toHaveLength(0);
  });

  it('should handle empty input array', () => {
    const result = filterPropertiesForAI([]);
    expect(result).toHaveLength(0);
  });
});

describe('formatPropertiesForTemplate', () => {
  it('should format properties with separators', () => {
    const properties = [
      {
        property_id: 'prop-1',
        match_rationale_localized: 'Great for stress',
        relevancy_to_property_score: 5,
        recommendation_instance_score: 16.0
      },
      {
        property_id: 'prop-2',
        match_rationale_localized: 'Helps with sleep',
        relevancy_to_property_score: 4,
        recommendation_instance_score: 12.0
      }
    ];
    
    const result = formatPropertiesForTemplate(properties);
    
    expect(result).toContain('Property ID: prop-1');
    expect(result).toContain('Match Rationale: Great for stress');
    expect(result).toContain('Property Relevancy Score: 5/5');
    expect(result).toContain('Recommendation Instance Score: 16');
    expect(result).toContain('---'); // Separator between properties
    expect(result).toContain('Property ID: prop-2');
    expect(result.endsWith('---')).toBe(false); // No separator after last
  });

  it('should handle empty properties array', () => {
    const result = formatPropertiesForTemplate([]);
    expect(result).toBe('');
  });

  it('should handle single property without separator', () => {
    const properties = [
      {
        property_id: 'prop-1',
        match_rationale_localized: 'Single property',
        relevancy_to_property_score: 3,
        recommendation_instance_score: 9.0
      }
    ];
    
    const result = formatPropertiesForTemplate(properties);
    
    expect(result).toContain('Property ID: prop-1');
    expect(result).toContain('Match Rationale: Single property');
    expect(result).toContain('Property Relevancy Score: 3/5');
    expect(result).toContain('Recommendation Instance Score: 9');
    expect(result).not.toContain('---'); // No separator for single property
  });

  it('should handle null and undefined values gracefully', () => {
    const properties = [
      {
        property_id: 'prop-1',
        match_rationale_localized: null,
        relevancy_to_property_score: null,
        recommendation_instance_score: 0
      },
      {
        property_id: 'prop-2',
        match_rationale_localized: undefined as any,
        relevancy_to_property_score: undefined as any,
        recommendation_instance_score: 5.5
      }
    ];
    
    const result = formatPropertiesForTemplate(properties);
    
    expect(result).toContain('Property ID: prop-1');
    expect(result).toContain('Match Rationale: '); // Empty string for null
    expect(result).toContain('Property Relevancy Score: /5'); // Empty string for null
    expect(result).toContain('Recommendation Instance Score: 0');
    expect(result).toContain('Property ID: prop-2');
    expect(result).toContain('Recommendation Instance Score: 5.5');
    expect(result).toContain('---'); // Separator between properties
  });

  it('should preserve Portuguese text and special characters', () => {
    const properties = [
      {
        property_id: 'prop-1',
        match_rationale_localized: 'Excelente para reduzir o estresse e ansiedade',
        relevancy_to_property_score: 5,
        recommendation_instance_score: 18.5
      }
    ];
    
    const result = formatPropertiesForTemplate(properties);
    
    expect(result).toContain('Match Rationale: Excelente para reduzir o estresse e ansiedade');
    expect(result).toContain('Property Relevancy Score: 5/5');
    expect(result).toContain('Recommendation Instance Score: 18.5');
  });

  it('should maintain proper indentation', () => {
    const properties = [
      {
        property_id: 'prop-1',
        match_rationale_localized: 'Test rationale',
        relevancy_to_property_score: 4,
        recommendation_instance_score: 12.0
      }
    ];
    
    const result = formatPropertiesForTemplate(properties);
    
    // Check that each line starts with proper indentation (7 spaces)
    const lines = result.split('\n');
    lines.forEach(line => {
      if (line.trim() !== '' && line !== '       ---') {
        expect(line).toMatch(/^       - /);
      }
    });
  });
});