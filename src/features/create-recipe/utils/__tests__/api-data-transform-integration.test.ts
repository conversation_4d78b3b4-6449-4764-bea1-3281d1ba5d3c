/**
 * @fileoverview Integration tests for API data transformation with oil enrichment utility
 */

import { createStreamRequest } from '../api-data-transform';
import type { 
  HealthConcernData, 
  DemographicsData, 
  PotentialCause, 
  PotentialSymptom,
  TherapeuticProperty 
} from '../../types/recipe.types';

describe('API Data Transform Integration', () => {
  const mockHealthConcern: HealthConcernData = {
    healthConcern: 'Stress and anxiety'
  };

  const mockDemographics: DemographicsData = {
    gender: 'female',
    ageCategory: 'adult',
    specificAge: 30
  };

  const mockSelectedCauses: PotentialCause[] = [
    {
      cause_id: 'cause-1',
      cause_name: 'Work stress',
      cause_suggestion: 'High workload',
      explanation: 'Excessive work demands'
    }
  ];

  const mockSelectedSymptoms: PotentialSymptom[] = [
    {
      symptom_id: 'symptom-1',
      symptom_name: 'Anxiety',
      symptom_suggestion: 'Feeling anxious',
      explanation: 'General anxiety symptoms'
    }
  ];

  const mockSuggestedOils: TherapeuticProperty[] = [
    {
      property_id: 'prop-1',
      property_name_localized: 'Calming',
      property_name_english: 'Calming',
      description_contextual_localized: 'Promotes relaxation',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 4,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Excellent for calming',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        }
      ]
    },
    {
      property_id: 'prop-2',
      property_name_localized: 'Anti-anxiety',
      property_name_english: 'Anti-anxiety',
      description_contextual_localized: 'Reduces anxiety symptoms',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 5,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1', // Same oil in different property context
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Proven anti-anxiety effects',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        },
        {
          oil_id: 'oil-2',
          name_english: 'Bergamot',
          name_botanical: 'Citrus bergamia',
          name_localized: 'Bergamota',
          name_scientific: 'Citrus bergamia',
          match_rationale_localized: 'Uplifting citrus for anxiety',
          relevancy_to_property_score: 4,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            phototoxicity_id: 'photo-1',
            phototoxicity: {
              status: 'Phototoxic',
              guidance: 'Avoid sun exposure',
              description: 'Can cause skin sensitivity'
            }
          }
        }
      ]
    }
  ];

  describe('final-recipes step integration', () => {
    it('should use enrichment utility for final-recipes step', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en',
        undefined,
        {
          timeSlot: 'morning',
          suggestedOils: mockSuggestedOils
        }
      );

      expect(result.feature).toBe('recipe-wizard');
      expect(result.step).toBe('final-recipes');
      expect(result.data.time_of_day).toBe('morning');
      expect(result.data.suggested_oils).toBeDefined();
      expect(Array.isArray(result.data.suggested_oils)).toBe(true);
    });

    it('should provide enriched oil data with final_relevance_score', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en',
        undefined,
        {
          timeSlot: 'morning',
          suggestedOils: mockSuggestedOils
        }
      );

      const enrichedOils = result.data.suggested_oils;
      expect(enrichedOils).toHaveLength(2); // oil-1 and oil-2

      // Verify each oil has enriched data structure
      enrichedOils.forEach((oil: any) => {
        expect(oil).toHaveProperty('oil_id');
        expect(oil).toHaveProperty('name_english');
        expect(oil).toHaveProperty('name_localized');
        expect(oil).toHaveProperty('final_relevance_score');
        expect(oil).toHaveProperty('properties');
        expect(oil).toHaveProperty('safety');
        expect(typeof oil.final_relevance_score).toBe('number');
        expect(Array.isArray(oil.properties)).toBe(true);
      });
    });

    it('should provide property context for each oil', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en',
        undefined,
        {
          timeSlot: 'morning',
          suggestedOils: mockSuggestedOils
        }
      );

      const enrichedOils = result.data.suggested_oils;
      const lavenderOil = enrichedOils.find((oil: any) => oil.oil_id === 'oil-1');
      
      expect(lavenderOil).toBeDefined();
      expect(lavenderOil.properties).toHaveLength(2); // Appears in both properties
      
      lavenderOil.properties.forEach((property: any) => {
        expect(property).toHaveProperty('property_id');
        expect(property).toHaveProperty('match_rationale_localized');
        expect(property).toHaveProperty('relevancy_to_property_score');
        expect(property).toHaveProperty('recommendation_instance_score');
      });
    });

    it('should provide safety data references', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en',
        undefined,
        {
          timeSlot: 'morning',
          suggestedOils: mockSuggestedOils
        }
      );

      const enrichedOils = result.data.suggested_oils;
      
      enrichedOils.forEach((oil: any) => {
        expect(oil.safety).toBeDefined();
        expect(oil.safety).toHaveProperty('internal_use');
        expect(oil.safety).toHaveProperty('dilution');
        expect(oil.safety).toHaveProperty('phototoxicity');
        expect(oil.safety).toHaveProperty('pregnancy_nursing');
        expect(oil.safety).toHaveProperty('child_safety');
        expect(Array.isArray(oil.safety.pregnancy_nursing)).toBe(true);
        expect(Array.isArray(oil.safety.child_safety)).toBe(true);
      });
    });

    it('should maintain backward compatibility for other steps', () => {
      // Test that non-final-recipes steps are not affected
      const result = createStreamRequest(
        'recipe-wizard',
        'potential-causes',
        mockHealthConcern,
        mockDemographics,
        [],
        [],
        'en'
      );

      expect(result.feature).toBe('recipe-wizard');
      expect(result.step).toBe('potential-causes');
      expect(result.data).toMatchObject({
        health_concern: 'Stress and anxiety',
        gender: 'female',
        age_category: 'adult',
        age_specific: '30',
        user_language: 'en',
        selected_causes: [],
        selected_symptoms: []
      });
      expect(result.data).not.toHaveProperty('suggested_oils');
    });

    it('should handle empty suggestedOils gracefully', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en',
        undefined,
        {
          timeSlot: 'evening',
          suggestedOils: []
        }
      );

      expect(result.data.suggested_oils).toEqual([]);
      expect(result.data.time_of_day).toBe('evening');
    });

    it('should handle missing additionalData gracefully', () => {
      const result = createStreamRequest(
        'recipe-wizard',
        'final-recipes',
        mockHealthConcern,
        mockDemographics,
        mockSelectedCauses,
        mockSelectedSymptoms,
        'en'
      );

      // Should fall back to regular data structure without final-recipes specific handling
      expect(result.data).not.toHaveProperty('time_of_day');
      expect(result.data).not.toHaveProperty('suggested_oils');
    });
  });

  describe('data structure consistency', () => {
    it('should provide consistent data structure across different time slots', () => {
      const timeSlots = ['morning', 'mid-day', 'night'];
      
      timeSlots.forEach(timeSlot => {
        const result = createStreamRequest(
          'recipe-wizard',
          'final-recipes',
          mockHealthConcern,
          mockDemographics,
          mockSelectedCauses,
          mockSelectedSymptoms,
          'en',
          undefined,
          {
            timeSlot,
            suggestedOils: mockSuggestedOils
          }
        );

        expect(result.data.time_of_day).toBe(timeSlot);
        expect(result.data.suggested_oils).toHaveLength(2);
        
        // Verify consistent structure regardless of time slot
        result.data.suggested_oils.forEach((oil: any) => {
          expect(oil).toHaveProperty('final_relevance_score');
          expect(oil).toHaveProperty('properties');
          expect(oil).toHaveProperty('safety');
        });
      });
    });
  });
});