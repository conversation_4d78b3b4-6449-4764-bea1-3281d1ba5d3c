/**
 * @fileoverview Tests for prompt manager debug logging functionality
 */

import { PromptManager } from '@/lib/ai/utils/prompt-manager';

describe('Prompt Manager Debug Logging', () => {
  const promptManager = PromptManager.getInstance();
  
  const mockTemplateData = {
    health_concern: 'Stress and anxiety',
    gender: 'female',
    age_category: 'adult',
    age_specific: '30',
    user_language: 'en',
    selected_causes: [
      {
        cause_id: 'cause-1',
        cause_name: 'Work stress',
        cause_suggestion: 'High workload',
        explanation: 'Excessive work demands causing stress'
      }
    ],
    selected_symptoms: [
      {
        symptom_id: 'symptom-1',
        symptom_name: 'Anxiety',
        symptom_suggestion: 'Feeling anxious',
        explanation: 'General anxiety symptoms'
      }
    ],
    suggested_oils: [
      {
        oil_id: 'oil-1',
        name_english: 'Lavender',
        name_localized: '<PERSON><PERSON>da',
        name_scientific: '<PERSON><PERSON><PERSON>la angustifolia',
        final_relevance_score: 25.5,
        safety: {
          internal_use: 'internal-1',
          dilution: 'dilution-1',
          phototoxicity: null,
          pregnancy_nursing: ['preg-1'],
          child_safety: []
        },
        properties: [
          {
            property_id: 'prop-1',
            match_rationale_localized: 'Excellent for calming and reducing stress',
            relevancy_to_property_score: 5,
            recommendation_instance_score: 16.0
          },
          {
            property_id: 'prop-2',
            match_rationale_localized: 'Proven anti-anxiety effects',
            relevancy_to_property_score: 5,
            recommendation_instance_score: 9.5
          }
        ]
      },
      {
        oil_id: 'oil-2',
        name_english: 'Bergamot',
        name_localized: 'Bergamota',
        name_scientific: 'Citrus bergamia',
        final_relevance_score: 15.0,
        safety: {
          internal_use: null,
          dilution: null,
          phototoxicity: 'photo-1',
          pregnancy_nursing: [],
          child_safety: []
        },
        properties: [
          {
            property_id: 'prop-2',
            match_rationale_localized: 'Uplifting citrus oil for anxiety relief',
            relevancy_to_property_score: 3,
            recommendation_instance_score: 15.0
          }
        ]
      }
    ],
    time_of_day: 'morning'
  };

  beforeEach(() => {
    // Spy on console.log to capture debug output
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    // Restore console.log
    jest.restoreAllMocks();
  });

  describe('Template processing debug logging', () => {
    it('should log template variables for suggested_oils', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // Verify debug logging was called
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Template processing - suggested_oils_count:',
        2
      );
    });

    it('should log first oil properties count', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Template processing - first_oil_properties_count:',
        2
      );
    });

    it('should log sample property data', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Template processing - sample_property:',
        {
          property_id: 'prop-1',
          match_rationale_localized: 'Excellent for calming and reducing stress',
          relevancy_to_property_score: 5,
          recommendation_instance_score: 16.0
        }
      );
    });

    it('should log each loop processing', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // Check if any log contains the expected debug messages
      const logCalls = (console.log as jest.Mock).mock.calls;
      const hasLoopProcessingLog = logCalls.some(call => 
        call[0] && call[0].includes('🔍 [DEBUG] Processing #each loop for:')
      );
      const hasArrayFoundLog = logCalls.some(call => 
        call[0] && call[0].includes('🔍 [DEBUG] Array found:')
      );
      
      expect(hasLoopProcessingLog).toBe(true);
      expect(hasArrayFoundLog).toBe(true);
    });

    it('should log oil properties processing', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      // Should log processing of oils with properties
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Processing oil with properties:',
        {
          oil_id: 'oil-1',
          properties_count: 2,
          has_nested_each: true
        }
      );
    });

    it('should log processed template sample', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Processed template sample - suggested_oils section (first 500 chars):'
      );
    });

    it('should log nested loop processing status', async () => {
      await promptManager.getProcessedPrompt('final-recipes', mockTemplateData);
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🔍 [DEBUG] Nested {{#each properties}} loops processed correctly:'),
        expect.any(Boolean)
      );
      
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('🔍 [DEBUG] {{#unless @last}} conditionals processed correctly:'),
        expect.any(Boolean)
      );
    });
  });

  describe('Edge cases logging', () => {
    it('should handle empty suggested_oils array', async () => {
      const emptyOilsData = {
        ...mockTemplateData,
        suggested_oils: []
      };
      
      await promptManager.getProcessedPrompt('final-recipes', emptyOilsData);
      
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Template processing - suggested_oils_count:',
        0
      );
    });

    it('should handle oils without properties', async () => {
      const noPropertiesData = {
        ...mockTemplateData,
        suggested_oils: [
          {
            oil_id: 'oil-no-props',
            name_english: 'No Properties Oil',
            name_localized: 'No Properties Oil',
            name_scientific: 'No properties',
            final_relevance_score: 5.0,
            safety: {},
            properties: []
          }
        ]
      };
      
      await promptManager.getProcessedPrompt('final-recipes', noPropertiesData);
      
      expect(console.log).toHaveBeenCalledWith(
        '🔍 [DEBUG] Template processing - first_oil_properties_count:',
        0
      );
    });

    it('should handle missing suggested_oils', async () => {
      const noOilsData = {
        ...mockTemplateData
      };
      delete noOilsData.suggested_oils;
      
      await promptManager.getProcessedPrompt('final-recipes', noOilsData);
      
      // Should not log suggested_oils related debug info
      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining('suggested_oils_count')
      );
    });
  });
});