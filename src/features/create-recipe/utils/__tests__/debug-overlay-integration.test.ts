/**
 * @fileoverview Integration test to verify utility function produces identical output to debug overlay
 * This test simulates the exact debug overlay logic and compares it with the utility function
 */

import { enrichOilsForAI } from '../oil-data-enrichment';
import type { TherapeuticProperty } from '../../types/recipe.types';
import { 
  enrichRecommendationDataWithScores, 
  calculateFinalRankedList,
  type EnrichedRecommendationData 
} from '../recommendation-scoring';

/**
 * Simulates the exact debug overlay logic for comparison
 */
function simulateDebugOverlayLogic(therapeuticProperties: TherapeuticProperty[]) {
  // This is the exact logic from recipe-debug-overlay.tsx
  const safetyMap = new Map();

  function getSafetyId(safetySubObj: Record<string, any> | null | undefined, category: string, actualId?: string): string | null {
    if (!safetySubObj) return null;
    const dedupKey = actualId || `${category}_${JSON.stringify(safetySubObj)}`;
    if (!safetyMap.has(dedupKey)) {
      safetyMap.set(dedupKey, { ...(safetySubObj || {}), safety_id: dedupKey, category });
    }
    return dedupKey;
  }

  // 1. Build a mapping of oil_id to property context for each oil
  const oilPropertiesMap: Record<string, any[]> = {};
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (!oil?.oil_id) return;
      if (!oilPropertiesMap[oil.oil_id]) oilPropertiesMap[oil.oil_id] = [];
      const propertyCoverageScore = prop.relevancy_score / 5;
      const recommendationInstanceScore = ((oil as any)['relevancy_to_property_score'] ?? 0) * prop.relevancy_score * propertyCoverageScore;
      oilPropertiesMap[oil.oil_id]?.push({
        property_id: prop.property_id,
        match_rationale_localized: (oil as any)['match_rationale_localized'] ?? null,
        relevancy_to_property_score: (oil as any)['relevancy_to_property_score'] ?? null,
        recommendation_instance_score: Math.round(recommendationInstanceScore * 100) / 100
      });
    });
  });

  // 2. Collect all unique oils
  const oilMap = new Map();
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (oil?.oil_id && !oilMap.has(oil.oil_id)) {
        const { isEnriched, enrichment_status, botanical_mismatch, similarity_score, search_query, enrichment_timestamp, name_botanical, match_rationale_localized, relevancy_to_property_score, ...oilRest } = oil;
        oilMap.set(oil.oil_id, { ...oilRest });
      }
    });
  });

  // 3. Create enriched recommendation data
  const recommendationData = {
    therapeuticProperties: therapeuticProperties,
    oils: Array.from(oilMap.values())
  };

  // 4. Enrich the data with calculated scores
  const enrichedData: EnrichedRecommendationData = enrichRecommendationDataWithScores(recommendationData);
  
  // 5. Calculate final ranked list
  const finalRankedOils = calculateFinalRankedList(enrichedData);
  
  // 6. Merge final_relevance_score and properties array
  const oilScoreMap = new Map();
  finalRankedOils.forEach(oil => {
    oilScoreMap.set(oil.oil_id, oil.final_relevance_score);
  });
  
  const oilsWithScore = Array.from(oilMap.values()).map((oil: Record<string, any>) => {
    let safetyRef = {};
    if (oil['safety']) {
      const safety = oil['safety'];
      safetyRef = {
        internal_use: getSafetyId(safety['internal_use'], 'internal_use', safety['internal_use_id']),
        dilution: getSafetyId(safety['dilution'], 'dilution', safety['dilution_id']),
        phototoxicity: getSafetyId(safety['phototoxicity'], 'phototoxicity', safety['phototoxicity_id']),
        pregnancy_nursing: Array.isArray(safety['pregnancy_nursing'])
          ? (safety['pregnancy_nursing'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['id'] || undefined;
              return getSafetyId(safeItem, 'pregnancy_nursing', actualId);
            }).filter(Boolean)
          : [],
        child_safety: Array.isArray(safety['child_safety'])
          ? (safety['child_safety'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['age_range_id'] || undefined;
              return getSafetyId(safeItem, 'child_safety', actualId);
            }).filter(Boolean)
          : []
      };
    }
    
    const properties = (therapeuticProperties || []).reduce((acc: any[], prop) => {
      const oilInProp = (prop.suggested_oils || []).find((o: any) => o.oil_id === oil['oil_id']);
      if (oilInProp) {
        acc.push({
          property_id: prop.property_id,
          match_rationale_localized: oilInProp.match_rationale_localized ?? null,
          relevancy_to_property_score: oilInProp.relevancy_to_property_score ?? null,
          recommendation_instance_score: Math.round(((oilInProp.relevancy_to_property_score ?? 0) * prop.relevancy_score * (prop.relevancy_score / 5)) * 100) / 100
        });
      }
      return acc;
    }, []);
    
    return {
      ...oil,
      final_relevance_score: oilScoreMap.get(oil['oil_id']) ?? null,
      properties,
      safety: safetyRef
    };
  });

  return oilsWithScore;
}

describe('Debug Overlay Integration', () => {
  const complexMockData: TherapeuticProperty[] = [
    {
      property_id: 'prop-1',
      property_name_localized: 'Anti-inflammatory',
      property_name_english: 'Anti-inflammatory',
      description_contextual_localized: 'Reduces inflammation',
      addresses_cause_ids: ['cause-1'],
      addresses_symptom_ids: ['symptom-1'],
      relevancy_score: 4,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1',
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'Excellent anti-inflammatory properties',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          similarity_score: 0.95,
          search_query: 'lavender oil',
          enrichment_timestamp: '2024-01-01T00:00:00Z',
          safety: {
            internal_use_id: 'uuid-internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            },
            dilution_id: 'uuid-dilution-1',
            dilution: {
              name: 'Standard dilution',
              description: '2-3%',
              percentage_max: 3,
              percentage_min: 2,
              ratio: '1:50'
            },
            phototoxicity_id: 'uuid-photo-1',
            phototoxicity: {
              status: 'Non-phototoxic',
              guidance: 'Safe for sun exposure',
              description: 'No phototoxic effects'
            },
            pregnancy_nursing: [
              {
                id: 'uuid-preg-1',
                name: 'Safe during pregnancy',
                status_description: 'Generally safe',
                code: 'SAFE',
                usage_guidance: 'Use as directed',
                description: 'Safe for pregnant women'
              }
            ],
            child_safety: [
              {
                age_range_id: 'uuid-child-1',
                age_range: '2-12 years',
                safety_notes: 'Safe for children over 2'
              }
            ]
          }
        },
        {
          oil_id: 'oil-2',
          name_english: 'Chamomile',
          name_botanical: 'Matricaria chamomilla',
          name_localized: 'Camomila',
          name_scientific: 'Matricaria chamomilla',
          match_rationale_localized: 'Potent anti-inflammatory effects',
          relevancy_to_property_score: 4,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'uuid-internal-2',
            internal_use: {
              name: 'Caution for internal use',
              code: 'CAUTION',
              description: 'Use with caution',
              guidance: 'Consult professional'
            }
          }
        }
      ]
    },
    {
      property_id: 'prop-2',
      property_name_localized: 'Calming',
      property_name_english: 'Calming',
      description_contextual_localized: 'Promotes relaxation and reduces anxiety',
      addresses_cause_ids: ['cause-2'],
      addresses_symptom_ids: ['symptom-2'],
      relevancy_score: 5,
      isEnriched: true,
      suggested_oils: [
        {
          oil_id: 'oil-1', // Same oil in different context
          name_english: 'Lavender',
          name_botanical: 'Lavandula angustifolia',
          name_localized: 'Lavanda',
          name_scientific: 'Lavandula angustifolia',
          match_rationale_localized: 'World-renowned for calming properties',
          relevancy_to_property_score: 5,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            internal_use_id: 'uuid-internal-1',
            internal_use: {
              name: 'Safe for internal use',
              code: 'SAFE',
              description: 'Generally safe',
              guidance: 'Use as directed'
            }
          }
        },
        {
          oil_id: 'oil-3',
          name_english: 'Bergamot',
          name_botanical: 'Citrus bergamia',
          name_localized: 'Bergamota',
          name_scientific: 'Citrus bergamia',
          match_rationale_localized: 'Uplifting and calming citrus oil',
          relevancy_to_property_score: 3,
          isEnriched: true,
          enrichment_status: 'enriched' as const,
          safety: {
            phototoxicity_id: 'uuid-photo-2',
            phototoxicity: {
              status: 'Phototoxic',
              guidance: 'Avoid sun exposure for 12 hours',
              description: 'Can cause skin sensitivity to sunlight'
            }
          }
        }
      ]
    }
  ];

  it('should produce identical output to debug overlay logic', () => {
    const utilityResult = enrichOilsForAI(complexMockData);
    const debugOverlayResult = simulateDebugOverlayLogic(complexMockData);

    // Remove properties_formatted field from utility result for comparison
    // since it's a new field not present in the original debug overlay logic
    const utilityResultForComparison = utilityResult.map(oil => {
      const { properties_formatted, ...oilWithoutFormatted } = oil;
      return oilWithoutFormatted;
    });

    // Convert both results to JSON strings for deep comparison
    const utilityJson = JSON.stringify(utilityResultForComparison, null, 2);
    const debugOverlayJson = JSON.stringify(debugOverlayResult, null, 2);

    expect(utilityJson).toBe(debugOverlayJson);
  });

  it('should have identical oil count and IDs', () => {
    const utilityResult = enrichOilsForAI(complexMockData);
    const debugOverlayResult = simulateDebugOverlayLogic(complexMockData);

    expect(utilityResult.length).toBe(debugOverlayResult.length);
    
    const utilityOilIds = utilityResult.map(oil => oil.oil_id).sort();
    const debugOverlayOilIds = debugOverlayResult.map(oil => oil.oil_id).sort();
    
    expect(utilityOilIds).toEqual(debugOverlayOilIds);
  });

  it('should have identical final_relevance_score values', () => {
    const utilityResult = enrichOilsForAI(complexMockData);
    const debugOverlayResult = simulateDebugOverlayLogic(complexMockData);

    utilityResult.forEach(utilityOil => {
      const debugOil = debugOverlayResult.find(oil => oil.oil_id === utilityOil.oil_id);
      expect(debugOil).toBeDefined();
      expect(utilityOil.final_relevance_score).toBe(debugOil.final_relevance_score);
    });
  });

  it('should have identical properties arrays', () => {
    const utilityResult = enrichOilsForAI(complexMockData);
    const debugOverlayResult = simulateDebugOverlayLogic(complexMockData);

    utilityResult.forEach(utilityOil => {
      const debugOil = debugOverlayResult.find(oil => oil.oil_id === utilityOil.oil_id);
      expect(debugOil).toBeDefined();
      
      expect(utilityOil.properties.length).toBe(debugOil.properties.length);
      
      utilityOil.properties.forEach(utilityProp => {
        const debugProp = debugOil.properties.find(p => p.property_id === utilityProp.property_id);
        expect(debugProp).toBeDefined();
        expect(utilityProp).toEqual(debugProp);
      });
    });
  });

  it('should have identical safety data structures', () => {
    const utilityResult = enrichOilsForAI(complexMockData);
    const debugOverlayResult = simulateDebugOverlayLogic(complexMockData);

    utilityResult.forEach(utilityOil => {
      const debugOil = debugOverlayResult.find(oil => oil.oil_id === utilityOil.oil_id);
      expect(debugOil).toBeDefined();
      expect(utilityOil.safety).toEqual(debugOil.safety);
    });
  });

  it('should handle edge cases identically', () => {
    const edgeCaseData: TherapeuticProperty[] = [
      {
        property_id: 'prop-edge',
        property_name_localized: 'Edge Case Property',
        property_name_english: 'Edge Case Property',
        description_contextual_localized: 'Testing edge cases',
        addresses_cause_ids: [],
        addresses_symptom_ids: [],
        relevancy_score: 1,
        isEnriched: true,
        suggested_oils: [
          {
            oil_id: 'oil-edge',
            name_english: 'Edge Oil',
            name_botanical: 'Edge botanical',
            name_localized: 'Edge Oil',
            match_rationale_localized: null as any,
            relevancy_to_property_score: null as any,
            isEnriched: true,
            enrichment_status: 'enriched' as const,
            safety: {
              pregnancy_nursing: [],
              child_safety: []
            }
          }
        ]
      }
    ];

    const utilityResult = enrichOilsForAI(edgeCaseData);
    const debugOverlayResult = simulateDebugOverlayLogic(edgeCaseData);

    // Remove properties_formatted field from utility result for comparison
    const utilityResultForComparison = utilityResult.map(oil => {
      const { properties_formatted, ...oilWithoutFormatted } = oil;
      return oilWithoutFormatted;
    });

    expect(JSON.stringify(utilityResultForComparison)).toBe(JSON.stringify(debugOverlayResult));
  });
});