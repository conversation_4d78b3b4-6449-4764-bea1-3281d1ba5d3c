/**
 * @fileoverview Data flow verification utilities for ensuring consistency
 * between utility function output and debug overlay output
 */

import { enrichOilsForAI } from './oil-data-enrichment';
import type { TherapeuticProperty } from '../types/recipe.types';

/**
 * Verifies that utility function output matches debug overlay's "Copy JSON (minimal)" format
 */
export function verifyDataFlowConsistency(
  therapeuticProperties: TherapeuticProperty[],
  context: string = 'unknown'
): {
  isConsistent: boolean;
  utilityOutput: any[];
  issues: string[];
  summary: {
    oil_count: number;
    total_properties: number;
    has_scores: boolean;
    has_safety_data: boolean;
  };
} {
  const issues: string[] = [];
  
  try {
    // Get utility function output
    const utilityOutput = enrichOilsForAI(therapeuticProperties);
    
    // Verify structure consistency
    const oilCount = utilityOutput.length;
    const totalProperties = utilityOutput.reduce((sum, oil) => sum + (oil.properties?.length || 0), 0);
    const hasScores = utilityOutput.every(oil => typeof oil.final_relevance_score === 'number');
    const hasSafetyData = utilityOutput.every(oil => oil.safety && typeof oil.safety === 'object');
    
    // Check for required fields
    utilityOutput.forEach((oil, index) => {
      if (!oil.oil_id) issues.push(`Oil ${index}: missing oil_id`);
      if (!oil.name_english) issues.push(`Oil ${index}: missing name_english`);
      if (!oil.name_localized) issues.push(`Oil ${index}: missing name_localized`);
      if (typeof oil.final_relevance_score !== 'number') {
        issues.push(`Oil ${index}: invalid final_relevance_score`);
      }
      if (!Array.isArray(oil.properties)) {
        issues.push(`Oil ${index}: properties is not an array`);
      }
      if (!oil.safety || typeof oil.safety !== 'object') {
        issues.push(`Oil ${index}: invalid safety data structure`);
      }
      
      // Check property contexts
      oil.properties?.forEach((prop: any, propIndex: number) => {
        if (!prop.property_id) {
          issues.push(`Oil ${index}, Property ${propIndex}: missing property_id`);
        }
        if (typeof prop.recommendation_instance_score !== 'number') {
          issues.push(`Oil ${index}, Property ${propIndex}: invalid recommendation_instance_score`);
        }
      });
    });
    
    const summary = {
      oil_count: oilCount,
      total_properties: totalProperties,
      has_scores: hasScores,
      has_safety_data: hasSafetyData
    };
    
    console.log(`🔍 [DEBUG] Data flow verification (${context}):`, {
      oil_count: oilCount,
      total_properties: totalProperties,
      has_scores: hasScores,
      has_safety_data: hasSafetyData,
      issues_count: issues.length,
      is_consistent: issues.length === 0
    });
    
    if (issues.length > 0) {
      console.warn(`⚠️ [WARNING] Data flow issues detected (${context}):`, issues);
    } else {
      console.log(`✅ [SUCCESS] Data flow verification passed (${context})`);
    }
    
    return {
      isConsistent: issues.length === 0,
      utilityOutput,
      issues,
      summary
    };
    
  } catch (error) {
    const errorMessage = `Data flow verification failed: ${error}`;
    issues.push(errorMessage);
    console.error(`❌ [ERROR] Data flow verification error (${context}):`, error);
    
    return {
      isConsistent: false,
      utilityOutput: [],
      issues,
      summary: {
        oil_count: 0,
        total_properties: 0,
        has_scores: false,
        has_safety_data: false
      }
    };
  }
}

/**
 * Logs confirmation that AI received complete therapeutic property data
 */
export function logAIDataConfirmation(
  enrichedOils: any[],
  context: string = 'AI request'
): void {
  const oilsWithScores = enrichedOils.filter(oil => typeof oil.final_relevance_score === 'number');
  const oilsWithProperties = enrichedOils.filter(oil => oil.properties && oil.properties.length > 0);
  const totalPropertyContexts = enrichedOils.reduce((sum, oil) => sum + (oil.properties?.length || 0), 0);
  
  const confirmation = {
    total_oils: enrichedOils.length,
    oils_with_final_relevance_score: oilsWithScores.length,
    oils_with_property_contexts: oilsWithProperties.length,
    total_property_contexts: totalPropertyContexts,
    has_complete_scoring_data: oilsWithScores.length === enrichedOils.length,
    has_property_specific_data: totalPropertyContexts > 0,
    sample_oil_data: enrichedOils.length > 0 ? {
      oil_id: enrichedOils[0].oil_id,
      final_relevance_score: enrichedOils[0].final_relevance_score,
      properties_count: enrichedOils[0].properties?.length || 0,
      has_safety_data: !!enrichedOils[0].safety,
      sample_property_context: enrichedOils[0].properties?.[0] || null
    } : null
  };
  
  console.log(`🤖 [AI-DATA-CONFIRMATION] ${context} - Enhanced oil data summary:`, confirmation);
  
  if (confirmation.has_complete_scoring_data && confirmation.has_property_specific_data) {
    console.log(`✅ [AI-READY] ${context} - AI has access to complete enhanced oil data with scoring and property contexts`);
  } else {
    console.warn(`⚠️ [AI-WARNING] ${context} - AI data may be incomplete:`, {
      missing_scores: !confirmation.has_complete_scoring_data,
      missing_property_contexts: !confirmation.has_property_specific_data
    });
  }
}

/**
 * Compares utility function output with expected debug overlay format
 */
export function compareWithDebugOverlayFormat(
  utilityOutput: any[],
  context: string = 'comparison'
): {
  isCompatible: boolean;
  compatibilityIssues: string[];
  formatAnalysis: {
    has_required_fields: boolean;
    has_correct_types: boolean;
    has_property_contexts: boolean;
    has_safety_references: boolean;
  };
} {
  const compatibilityIssues: string[] = [];
  
  // Check debug overlay compatibility
  let hasRequiredFields = true;
  let hasCorrectTypes = true;
  let hasPropertyContexts = true;
  let hasSafetyReferences = true;
  
  utilityOutput.forEach((oil, index) => {
    // Required fields for debug overlay
    const requiredFields = ['oil_id', 'name_english', 'name_localized', 'final_relevance_score', 'properties', 'safety'];
    requiredFields.forEach(field => {
      if (!(field in oil)) {
        hasRequiredFields = false;
        compatibilityIssues.push(`Oil ${index}: missing required field '${field}'`);
      }
    });
    
    // Type checking
    if (typeof oil.final_relevance_score !== 'number') {
      hasCorrectTypes = false;
      compatibilityIssues.push(`Oil ${index}: final_relevance_score should be number`);
    }
    
    if (!Array.isArray(oil.properties)) {
      hasCorrectTypes = false;
      hasPropertyContexts = false;
      compatibilityIssues.push(`Oil ${index}: properties should be array`);
    }
    
    if (typeof oil.safety !== 'object' || oil.safety === null) {
      hasCorrectTypes = false;
      hasSafetyReferences = false;
      compatibilityIssues.push(`Oil ${index}: safety should be object`);
    }
    
    // Property context structure
    if (Array.isArray(oil.properties)) {
      oil.properties.forEach((prop: any, propIndex: number) => {
        const requiredPropFields = ['property_id', 'match_rationale_localized', 'relevancy_to_property_score', 'recommendation_instance_score'];
        requiredPropFields.forEach(field => {
          if (!(field in prop)) {
            hasPropertyContexts = false;
            compatibilityIssues.push(`Oil ${index}, Property ${propIndex}: missing '${field}'`);
          }
        });
      });
    }
    
    // Safety data structure
    if (oil.safety && typeof oil.safety === 'object') {
      const requiredSafetyFields = ['internal_use', 'dilution', 'phototoxicity', 'pregnancy_nursing', 'child_safety'];
      requiredSafetyFields.forEach(field => {
        if (!(field in oil.safety)) {
          hasSafetyReferences = false;
          compatibilityIssues.push(`Oil ${index}: safety missing '${field}'`);
        }
      });
    }
  });
  
  const formatAnalysis = {
    has_required_fields: hasRequiredFields,
    has_correct_types: hasCorrectTypes,
    has_property_contexts: hasPropertyContexts,
    has_safety_references: hasSafetyReferences
  };
  
  const isCompatible = compatibilityIssues.length === 0;
  
  console.log(`🔍 [DEBUG] Debug overlay compatibility check (${context}):`, {
    is_compatible: isCompatible,
    issues_count: compatibilityIssues.length,
    format_analysis: formatAnalysis
  });
  
  if (!isCompatible) {
    console.warn(`⚠️ [WARNING] Debug overlay compatibility issues (${context}):`, compatibilityIssues);
  }
  
  return {
    isCompatible,
    compatibilityIssues,
    formatAnalysis
  };
}

/**
 * Comprehensive data flow verification that combines all checks
 */
export function performComprehensiveDataFlowVerification(
  therapeuticProperties: TherapeuticProperty[],
  context: string = 'comprehensive-check'
): {
  overall_success: boolean;
  verification_result: any;
  compatibility_result: any;
  ai_data_ready: boolean;
} {
  console.log(`🔍 [DEBUG] Starting comprehensive data flow verification (${context})`);
  
  // Step 1: Verify data flow consistency
  const verificationResult = verifyDataFlowConsistency(therapeuticProperties, context);
  
  // Step 2: Check debug overlay compatibility
  const compatibilityResult = compareWithDebugOverlayFormat(verificationResult.utilityOutput, context);
  
  // Step 3: Log AI data confirmation
  logAIDataConfirmation(verificationResult.utilityOutput, context);
  
  // Step 4: Overall assessment
  const overallSuccess = verificationResult.isConsistent && compatibilityResult.isCompatible;
  const aiDataReady = verificationResult.summary.oil_count > 0 &&
                      verificationResult.summary.has_scores && 
                      verificationResult.summary.has_safety_data && 
                      compatibilityResult.formatAnalysis.has_property_contexts;
  
  console.log(`🔍 [DEBUG] Comprehensive verification summary (${context}):`, {
    overall_success: overallSuccess,
    data_flow_consistent: verificationResult.isConsistent,
    debug_overlay_compatible: compatibilityResult.isCompatible,
    ai_data_ready: aiDataReady,
    total_issues: verificationResult.issues.length + compatibilityResult.compatibilityIssues.length
  });
  
  if (overallSuccess) {
    console.log(`✅ [SUCCESS] Comprehensive data flow verification passed (${context})`);
  } else {
    console.error(`❌ [FAILURE] Comprehensive data flow verification failed (${context})`);
  }
  
  return {
    overall_success: overallSuccess,
    verification_result: verificationResult,
    compatibility_result: compatibilityResult,
    ai_data_ready: aiDataReady
  };
}