/**
 * @fileoverview Oil Data Enrichment Utility
 * 
 * This utility extracts and centralizes the oil enrichment logic from the debug overlay
 * to provide consistent data transformation across the application. It processes
 * therapeutic properties to create enriched oil data with scoring and safety information.
 */

import type { TherapeuticProperty } from '../types/recipe.types';
import { 
  enrichRecommendationDataWithScores, 
  calculateFinalRankedList,
  type EnrichedRecommendationData 
} from './recommendation-scoring';

/**
 * Interface for enriched oil data structure sent to AI
 */
export interface EnrichedOilForAI {
  oil_id: string;
  name_english: string;
  name_localized: string;
  name_scientific?: string;
  safety: SafetyDataReferences;
  final_relevance_score: number;
  properties: OilPropertyContext[];
  properties_formatted: string;
}

// Enhanced return type for enrichOilsForAI function
export interface EnrichmentResult {
  oils: EnrichedOilForAI[];
  safety_library: SafetyLibrary;
  safety_library_formatted: string;
}

/**
 * Interface for oil property context within each oil
 */
export interface OilPropertyContext {
  property_id: string;
  match_rationale_localized: string | null;
  relevancy_to_property_score: number | null;
  recommendation_instance_score: number;
}

/**
 * Interface for safety data references using Supabase UUIDs
 */
export interface SafetyDataReferences {
  internal_use: string | null;
  dilution: string | null;
  phototoxicity: string | null;
  pregnancy_nursing: string[];
  child_safety: string[];
}

// Individual safety object interfaces for the deduplicated library
export interface InternalUseSafety {
  id: string;
  code?: string | null;
  name?: string | null;
  description?: string | null;
  guidance?: string | null;
}

export interface DilutionSafety {
  id: string;
  name?: string | null;
  description?: string | null;
  percentage_max?: number | null;
  percentage_min?: number | null;
  ratio?: string | null;
}

export interface PhototoxicitySafety {
  id: string;
  status?: string | null;
  guidance?: string | null;
  description?: string | null;
}

export interface PregnancyNursingSafety {
  id: string;
  name?: string | null;
  status_description?: string | null;
  code?: string | null;
  usage_guidance?: string | null;
  description?: string | null;
}

export interface ChildSafety {
  age_range_id: string;
  age_range?: string | null;
  safety_notes?: string | null;
}

// Deduplicated safety library interface
export interface SafetyLibrary {
  internal_use: Record<string, InternalUseSafety>;
  dilution: Record<string, DilutionSafety>;
  phototoxicity: Record<string, PhototoxicitySafety>;
  pregnancy_nursing: Record<string, PregnancyNursingSafety>;
  child_safety: Record<string, ChildSafety>;
}

/**
 * Formats properties array into a readable string for template processing.
 * This function pre-processes the properties data to avoid nested handlebars loops.
 * 
 * @param properties - Array of oil property contexts
 * @returns Formatted string with proper indentation and separators
 */
export function formatPropertiesForTemplate(properties: OilPropertyContext[]): string {
  if (!properties || properties.length === 0) {
    return '';
  }
  
  return properties.map((prop, index) => {
    const lines = [
      `       - Property ID: ${prop.property_id}`,
      `       - Match Rationale: ${prop.match_rationale_localized || ''}`,
      `       - Property Relevancy Score: ${prop.relevancy_to_property_score ?? ''}/5`,
      `       - Recommendation Instance Score: ${prop.recommendation_instance_score ?? ''}`
    ];
    
    // Add separator between properties (but not after last)
    if (index < properties.length - 1) {
      lines.push('       ---');
    }
    
    return lines.join('\n');
  }).join('\n');
}

/**
 * Filters therapeutic properties to only include those ready for AI processing.
 * This ensures consistent filtering logic across debug overlay and final-recipes.
 * 
 * @param therapeuticProperties - Array of therapeutic properties
 * @returns Filtered array of properties that are enriched and have suggested oils
 */
export function filterPropertiesForAI(therapeuticProperties: TherapeuticProperty[]): TherapeuticProperty[] {
  return therapeuticProperties.filter(prop => 
    prop.isEnriched && 
    prop.suggested_oils && 
    prop.suggested_oils.length > 0
  );
}

/**
 * Formats safety library into a readable string for template processing.
 * This function pre-processes the safety library data to avoid nested handlebars loops.
 * Following the same pattern as formatPropertiesForTemplate.
 * 
 * @param safetyLibrary - The deduplicated safety library
 * @returns Formatted string with proper structure and indentation
 */
export function formatSafetyLibraryForTemplate(safetyLibrary: SafetyLibrary): string {
  const sections: string[] = [];
  
  // Internal Use Safety Options
  if (Object.keys(safetyLibrary.internal_use).length > 0) {
    sections.push('**Internal Use Safety Options:**');
    Object.entries(safetyLibrary.internal_use).forEach(([id, safety]) => {
      sections.push(`     - ID: ${id}`);
      sections.push(`       Code: ${safety.code || 'N/A'}`);
      sections.push(`       Name: ${safety.name}`);
      sections.push(`       Description: ${safety.description}`);
      sections.push(`       Guidance: ${safety.guidance || 'N/A'}`);
      sections.push('');
    });
  }
  
  // Dilution Safety Options
  if (Object.keys(safetyLibrary.dilution).length > 0) {
    sections.push('**Dilution Safety Options:**');
    Object.entries(safetyLibrary.dilution).forEach(([id, safety]) => {
      sections.push(`     - ID: ${id}`);
      sections.push(`       Name: ${safety.name}`);
      sections.push(`       Description: ${safety.description}`);
      sections.push(`       Ratio: ${safety.ratio}`);
      sections.push(`       Percentage Min: ${safety.percentage_min !== null ? safety.percentage_min : 'N/A'}`);
      sections.push(`       Percentage Max: ${safety.percentage_max !== null ? safety.percentage_max : 'N/A'}`);
      sections.push('');
    });
  }
  
  // Phototoxicity Safety Options
  if (Object.keys(safetyLibrary.phototoxicity).length > 0) {
    sections.push('**Phototoxicity Safety Options:**');
    Object.entries(safetyLibrary.phototoxicity).forEach(([id, safety]) => {
      sections.push(`     - ID: ${id}`);
      sections.push(`       Status: ${safety.status || 'N/A'}`);
      sections.push(`       Description: ${safety.description || 'N/A'}`);
      sections.push(`       Guidance: ${safety.guidance || 'N/A'}`);
      sections.push('');
    });
  }
  
  // Pregnancy & Nursing Safety Options
  if (Object.keys(safetyLibrary.pregnancy_nursing).length > 0) {
    sections.push('**Pregnancy & Nursing Safety Options:**');
    Object.entries(safetyLibrary.pregnancy_nursing).forEach(([id, safety]) => {
      sections.push(`     - ID: ${id}`);
      // Use status_description when name/description are null
      const displayName = safety.name || safety.status_description || 'N/A';
      const displayDescription = safety.description || safety.status_description || 'N/A';
      sections.push(`       Name: ${displayName}`);
      sections.push(`       Status Description: ${safety.status_description || 'N/A'}`);
      sections.push(`       Code: ${safety.code || 'N/A'}`);
      sections.push(`       Description: ${displayDescription}`);
      sections.push(`       Usage Guidance: ${safety.usage_guidance || 'N/A'}`);
      sections.push('');
    });
  }
  
  // Child Safety Options
  if (Object.keys(safetyLibrary.child_safety).length > 0) {
    sections.push('**Child Safety Options:**');
    Object.entries(safetyLibrary.child_safety).forEach(([id, safety]) => {
      sections.push(`     - ID: ${id}`);
      sections.push(`       Age Range ID: ${safety.age_range_id}`);
      sections.push(`       Age Range: ${safety.age_range || 'N/A'}`);
      sections.push(`       Safety Notes: ${safety.safety_notes || 'N/A'}`);
      sections.push('');
    });
  }
  
  return sections.join('\n');
}

/**
 * Enriches therapeutic properties data to create comprehensive oil data for AI processing.
 * This function extracts the exact logic from the debug overlay to ensure consistency.
 * 
 * @param therapeuticProperties - Array of therapeutic properties with suggested oils
 * @param includeSafetyLibrary - Optional flag to include deduplicated safety library
 * @returns Array of enriched oil data with scoring and safety information, optionally with safety library
 */
// Function overloads to support both legacy and enhanced return types
export function enrichOilsForAI(therapeuticProperties: TherapeuticProperty[]): EnrichedOilForAI[];
export function enrichOilsForAI(therapeuticProperties: TherapeuticProperty[], includeSafetyLibrary: true): EnrichmentResult;
export function enrichOilsForAI(
  therapeuticProperties: TherapeuticProperty[], 
  includeSafetyLibrary: boolean = false
): EnrichedOilForAI[] | EnrichmentResult {
  // Safety map for deduplication using actual Supabase UUIDs
  const safetyMap = new Map();

  // Helper to get or create a safety entry using actual Supabase UUID or content hash
  function getSafetyId(safetySubObj: Record<string, any> | null | undefined, category: string, actualId?: string): string | null {
    if (!safetySubObj) return null;
    // Use the actual Supabase UUID if available, otherwise use a hash of the object content
    const dedupKey = actualId || `${category}_${JSON.stringify(safetySubObj)}`;
    if (!safetyMap.has(dedupKey)) {
      safetyMap.set(dedupKey, { ...(safetySubObj || {}), safety_id: dedupKey, category });
    }
    return dedupKey;
  }

  // Helper to build deduplicated safety library from all processed oils
  function buildSafetyLibrary(allSafetyData: any[]): SafetyLibrary {
    const library: SafetyLibrary = {
      internal_use: {},
      dilution: {},
      phototoxicity: {},
      pregnancy_nursing: {},
      child_safety: {}
    };

    // Track deduplication statistics for debugging
    let totalSafetyObjects = 0;
    let deduplicatedCount = 0;

    // Deduplicate and build library from collected safety data
    allSafetyData.forEach(safety => {
      totalSafetyObjects++;
      
      // Process internal_use
      if (safety.internal_use) {
        const id = getSafetyId(safety.internal_use, 'internal_use', safety.internal_use_id);
        if (id && !library.internal_use[id]) {
          library.internal_use[id] = {
            id,
            code: safety.internal_use?.code || null,
            name: safety.internal_use?.name || null,
            description: safety.internal_use?.description || null,
            guidance: safety.internal_use?.guidance || null,
          };
        } else if (id && library.internal_use[id]) {
          deduplicatedCount++;
        }
      }

      // Process dilution
      if (safety.dilution) {
        const id = getSafetyId(safety.dilution, 'dilution', safety.dilution_id);
        if (id && !library.dilution[id]) {
          library.dilution[id] = {
            id,
            name: safety.dilution?.name || null,
            description: safety.dilution?.description || null,
            percentage_max: safety.dilution?.percentage_max || null,
            percentage_min: safety.dilution?.percentage_min || null,
            ratio: safety.dilution?.ratio || null,
          };
        } else if (id && library.dilution[id]) {
          deduplicatedCount++;
        }
      }

      // Process phototoxicity
      if (safety.phototoxicity) {
        const id = getSafetyId(safety.phototoxicity, 'phototoxicity', safety.phototoxicity_id);
        if (id && !library.phototoxicity[id]) {
          library.phototoxicity[id] = {
            id,
            status: safety.phototoxicity?.status || null,
            guidance: safety.phototoxicity?.guidance || null,
            description: safety.phototoxicity?.description || null,
          };
        } else if (id && library.phototoxicity[id]) {
          deduplicatedCount++;
        }
      }

      // Process pregnancy_nursing array
      if (Array.isArray(safety.pregnancy_nursing)) {
        safety.pregnancy_nursing.forEach((item: any) => {
          const id = getSafetyId(item, 'pregnancy_nursing', item?.id);
          if (id && !library.pregnancy_nursing[id]) {
            library.pregnancy_nursing[id] = {
              id,
              name: item?.name || null,
              status_description: item?.status_description || null,
              code: item?.code || null,
              usage_guidance: item?.usage_guidance || null,
              description: item?.description || null,
            };
          } else if (id && library.pregnancy_nursing[id]) {
            deduplicatedCount++;
          }
        });
      }

      // Process child_safety array
      if (Array.isArray(safety.child_safety)) {
        safety.child_safety.forEach((item: any) => {
          const id = getSafetyId(item, 'child_safety', item?.age_range_id || item?.id);
          if (id && !library.child_safety[id]) {
            library.child_safety[id] = {
              age_range_id: id,
              age_range: item?.age_range || null,
              safety_notes: item?.safety_notes || null,
            };
          } else if (id && library.child_safety[id]) {
            deduplicatedCount++;
          }
        });
      }
    });

    // Log deduplication statistics (useful for debugging)
    console.log(`🔧 [Safety Library] Processed ${totalSafetyObjects} oils, deduplicated ${deduplicatedCount} safety objects`);
    console.log(`🔧 [Safety Library] Created library with:`, {
      internal_use: Object.keys(library.internal_use).length,
      dilution: Object.keys(library.dilution).length,
      phototoxicity: Object.keys(library.phototoxicity).length,
      pregnancy_nursing: Object.keys(library.pregnancy_nursing).length,
      child_safety: Object.keys(library.child_safety).length
    });

    return library;
  }

  // 1. Build a mapping of oil_id to property context for each oil
  const oilPropertiesMap: Record<string, any[]> = {};
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (!oil?.oil_id) return;
      if (!oilPropertiesMap[oil.oil_id]) oilPropertiesMap[oil.oil_id] = [];
      // Calculate recommendation_instance_score using the scoring system formula
      const propertyCoverageScore = prop.relevancy_score / 5; // MAX_RELEVANCY_SCORE = 5
      const recommendationInstanceScore = ((oil as any)['relevancy_to_property_score'] ?? 0) * prop.relevancy_score * propertyCoverageScore;
      oilPropertiesMap[oil.oil_id]?.push({
        property_id: prop.property_id,
        match_rationale_localized: (oil as any)['match_rationale_localized'] ?? null,
        relevancy_to_property_score: (oil as any)['relevancy_to_property_score'] ?? null,
        recommendation_instance_score: Math.round(recommendationInstanceScore * 100) / 100
      });
    });
  });

  // 2. Collect all unique oils from all properties' suggested_oils
  const oilMap = new Map();
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (oil?.oil_id && !oilMap.has(oil.oil_id)) {
        // Only include the essential, intrinsic oil fields (no property-specific context)
        // Remove unwanted fields as before
        const { isEnriched, enrichment_status, botanical_mismatch, similarity_score, search_query, enrichment_timestamp, name_botanical, match_rationale_localized, relevancy_to_property_score, ...oilRest } = oil;
        oilMap.set(oil.oil_id, {
          ...oilRest
        });
      }
    });
  });

  // 3. Create enriched recommendation data with calculated scores
  // Use the original therapeuticProperties (with suggested_oils) for scoring
  const recommendationData = {
    therapeuticProperties: therapeuticProperties,
    oils: Array.from(oilMap.values())
  };

  // 4. Enrich the data with calculated scores
  const enrichedData: EnrichedRecommendationData = enrichRecommendationDataWithScores(recommendationData);
  
  // 5. Calculate final ranked list
  const finalRankedOils = calculateFinalRankedList(enrichedData);
  
  // 6. Merge final_relevance_score and properties array into each oil in the deduplicated oils array
  const oilScoreMap = new Map();
  finalRankedOils.forEach(oil => {
    oilScoreMap.set(oil.oil_id, oil.final_relevance_score);
  });
  
  // Collect all safety data for library building (if requested)
  const allSafetyData: any[] = [];
  
  const oilsWithScore = Array.from(oilMap.values()).map((oil: Record<string, any>) => {
    let safetyRef: SafetyDataReferences = {
      internal_use: null,
      dilution: null,
      phototoxicity: null,
      pregnancy_nursing: [],
      child_safety: []
    };
    
    if (oil['safety']) {
      const safety = oil['safety'];
      
      // Collect safety data for library (before processing IDs)
      if (includeSafetyLibrary) {
        allSafetyData.push(safety);
      }
      
      safetyRef = {
        internal_use: getSafetyId(safety['internal_use'], 'internal_use', safety['internal_use_id']),
        dilution: getSafetyId(safety['dilution'], 'dilution', safety['dilution_id']),
        phototoxicity: getSafetyId(safety['phototoxicity'], 'phototoxicity', safety['phototoxicity_id']),
        pregnancy_nursing: Array.isArray(safety['pregnancy_nursing'])
          ? (safety['pregnancy_nursing'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['id'] || undefined;
              return getSafetyId(safeItem, 'pregnancy_nursing', actualId);
            }).filter(Boolean) as string[]
          : [],
        child_safety: Array.isArray(safety['child_safety'])
          ? (safety['child_safety'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['age_range_id'] || undefined;
              return getSafetyId(safeItem, 'child_safety', actualId);
            }).filter(Boolean) as string[]
          : []
      };
    }
    
    // Build the properties array for this oil, including relevancy and match_rationale_localized per property context
    const properties: OilPropertyContext[] = (therapeuticProperties || []).reduce((acc: OilPropertyContext[], prop) => {
      const oilInProp = (prop.suggested_oils || []).find((o: any) => o.oil_id === oil['oil_id']);
      if (oilInProp) {
        // Ensure we have valid numbers for calculation
        const relevancyToProperty = oilInProp.relevancy_to_property_score ?? 0;
        const propertyRelevancy = prop.relevancy_score ?? 0;
        const propertyCoverageScore = propertyRelevancy / 5; // MAX_RELEVANCY_SCORE = 5
        const recommendationInstanceScore = relevancyToProperty * propertyRelevancy * propertyCoverageScore;
        
        acc.push({
          property_id: prop.property_id,
          match_rationale_localized: oilInProp.match_rationale_localized ?? null,
          relevancy_to_property_score: oilInProp.relevancy_to_property_score ?? null,
          recommendation_instance_score: Math.round(recommendationInstanceScore * 100) / 100
        });
      }
      return acc;
    }, []);
    
    // Get the final relevance score, ensuring it's a valid number
    const finalRelevanceScore = oilScoreMap.get(oil['oil_id']) ?? 0;
    
    return {
      ...oil,
      final_relevance_score: finalRelevanceScore,
      properties,
      properties_formatted: formatPropertiesForTemplate(properties),
      safety: safetyRef
    } as EnrichedOilForAI;
  });

  // Return enhanced result with safety library if requested
  if (includeSafetyLibrary) {
    const safetyLibrary = buildSafetyLibrary(allSafetyData);
    const safetyLibraryFormatted = formatSafetyLibraryForTemplate(safetyLibrary);
    return {
      oils: oilsWithScore,
      safety_library: safetyLibrary,
      safety_library_formatted: safetyLibraryFormatted
    } as EnrichmentResult;
  }
  
  return oilsWithScore;
}