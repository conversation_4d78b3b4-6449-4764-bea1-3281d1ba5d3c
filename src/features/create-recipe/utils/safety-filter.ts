/**
 * @fileoverview Safety filtering utility for essential oils based on user demographics.
 * Implements child safety filtering and demographic-based oil restrictions.
 */

import type { DemographicsData, EnrichedEssentialOil } from '../types/recipe.types';

/**
 * Interface for safety assessment result
 */
export interface SafetyAssessment {
  isSafe: boolean;
  warnings: string[];
  restrictions: string[];
  recommendedDilution?: number;
}

/**
 * Known dermocaustic oils that should be avoided for children under 10
 * Based on aromatherapy safety guidelines
 */
const DERMOCAUSTIC_OILS = [
  'cinnamon bark',
  'cinnamon leaf', 
  'clove bud',
  'clove leaf',
  'oregano',
  'thyme',
  'wintergreen',
  'birch',
  'cassia',
  'bay laurel'
];

/**
 * Oils with high phototoxicity risk
 */
const PHOTOTOXIC_OILS = [
  'bergamot',
  'lime',
  'lemon',
  'grapefruit',
  'orange bitter',
  'angelica root'
];

/**
 * Filters oils based on child safety requirements
 * 
 * @param oils - Array of enriched essential oils
 * @param demographics - User demographic data
 * @returns Filtered array of safe oils
 */
export function filterOilsForChildSafety(
  oils: EnrichedEssentialOil[],
  demographics: DemographicsData
): EnrichedEssentialOil[] {
  // If user is 10 or older, return all oils
  if (demographics.specificAge >= 10) {
    return oils;
  }

  return oils.filter(oil => {
    const assessment = assessOilSafety(oil, demographics);
    return assessment.isSafe;
  });
}

/**
 * Assesses the safety of a specific oil for the given user demographics
 * 
 * @param oil - Enriched essential oil to assess
 * @param demographics - User demographic data
 * @returns Safety assessment with recommendations
 */
export function assessOilSafety(
  oil: EnrichedEssentialOil,
  demographics: DemographicsData
): SafetyAssessment {
  const warnings: string[] = [];
  const restrictions: string[] = [];
  let isSafe = true;
  let recommendedDilution: number | undefined;

  // Check for dermocaustic oils in children under 10
  if (demographics.specificAge < 10) {
    const isDermocaustic = DERMOCAUSTIC_OILS.some(dangerous => 
      oil.name_english.toLowerCase().includes(dangerous.toLowerCase()) ||
      oil.name_botanical.toLowerCase().includes(dangerous.toLowerCase())
    );

    if (isDermocaustic) {
      isSafe = false;
      restrictions.push(`${oil.name_english} is not safe for children under 10 years old`);
    }

    // Set conservative dilution for children
    recommendedDilution = 0.5; // 0.5% maximum for children under 10
  } else {
    // Standard dilution for older children and adults
    recommendedDilution = demographics.specificAge < 18 ? 1.0 : 2.0;
  }

  // Check child safety data from enrichment
  if (oil.safety?.child_safety) {
    oil.safety.child_safety.forEach(childSafety => {
      if (childSafety.age_range && childSafety.safety_notes) {
        // Parse age range (e.g., "0-10", "under 6")
        const ageMatch = childSafety.age_range.match(/(\d+)/g);
        if (ageMatch) {
          const maxAge = parseInt(ageMatch[ageMatch.length - 1]);
          if (demographics.specificAge <= maxAge) {
            if (childSafety.safety_notes.toLowerCase().includes('avoid') || 
                childSafety.safety_notes.toLowerCase().includes('not recommended')) {
              isSafe = false;
              restrictions.push(childSafety.safety_notes);
            } else {
              warnings.push(childSafety.safety_notes);
            }
          }
        }
      }
    });
  }

  // Check phototoxicity
  const isPhototoxic = PHOTOTOXIC_OILS.some(photo => 
    oil.name_english.toLowerCase().includes(photo.toLowerCase())
  );

  if (isPhototoxic || oil.safety?.phototoxicity?.status === 'high') {
    warnings.push(`${oil.name_english} may cause skin sensitivity to sunlight. Avoid sun exposure for 12 hours after topical application.`);
  }

  // Check pregnancy/nursing restrictions if applicable
  if (demographics.gender === 'female' && oil.safety?.pregnancy_nursing) {
    oil.safety.pregnancy_nursing.forEach(pregnancySafety => {
      if (pregnancySafety.status_description?.toLowerCase().includes('avoid') ||
          pregnancySafety.code === 'contraindicated') {
        warnings.push(`${oil.name_english}: ${pregnancySafety.usage_guidance || pregnancySafety.description}`);
      }
    });
  }

  // Check dilution requirements
  if (oil.safety?.dilution) {
    const maxDilution = oil.safety.dilution.percentage_max;
    if (maxDilution && recommendedDilution && recommendedDilution > maxDilution) {
      recommendedDilution = maxDilution;
      warnings.push(`${oil.name_english} requires maximum ${maxDilution}% dilution`);
    }
  }

  return {
    isSafe,
    warnings,
    restrictions,
    recommendedDilution
  };
}

/**
 * Gets the recommended dilution percentage for a user's age
 * 
 * @param age - User's age in years
 * @returns Recommended dilution percentage
 */
export function getRecommendedDilution(age: number): number {
  if (age < 2) return 0.25; // 0.25% for infants
  if (age < 6) return 0.5;  // 0.5% for young children
  if (age < 10) return 0.5; // 0.5% for children under 10
  if (age < 18) return 1.0; // 1% for teenagers
  return 2.0; // 2% for adults
}

/**
 * Calculates safe carrier oil amount based on essential oil drops and dilution
 * 
 * @param totalEssentialOilDrops - Total drops of essential oils
 * @param dilutionPercentage - Desired dilution percentage
 * @returns Carrier oil amount in ml
 */
export function calculateCarrierOilAmount(
  totalEssentialOilDrops: number,
  dilutionPercentage: number
): number {
  // Assuming 20 drops = 1ml for essential oils
  const essentialOilMl = totalEssentialOilDrops / 20;
  
  // Calculate total volume needed for desired dilution
  const totalVolumeMl = essentialOilMl / (dilutionPercentage / 100);
  
  // Carrier oil amount = total volume - essential oil volume
  const carrierOilMl = totalVolumeMl - essentialOilMl;
  
  // Round to reasonable precision
  return Math.round(carrierOilMl * 10) / 10;
}

/**
 * Validates that a recipe meets safety requirements for the user
 * 
 * @param oils - Selected oils for the recipe
 * @param demographics - User demographic data
 * @param totalDrops - Total essential oil drops in recipe
 * @returns Validation result with safety recommendations
 */
export function validateRecipeSafety(
  oils: EnrichedEssentialOil[],
  demographics: DemographicsData,
  totalDrops: number
): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  recommendedDilution: number;
  carrierOilAmount: number;
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  const recommendedDilution = getRecommendedDilution(demographics.specificAge);
  const carrierOilAmount = calculateCarrierOilAmount(totalDrops, recommendedDilution);

  // Check each oil for safety
  oils.forEach(oil => {
    const assessment = assessOilSafety(oil, demographics);
    
    if (!assessment.isSafe) {
      errors.push(...assessment.restrictions);
    }
    
    warnings.push(...assessment.warnings);
  });

  // Check total drop count for age appropriateness
  const maxDropsForAge = demographics.specificAge < 10 ? 3 : 
                        demographics.specificAge < 18 ? 5 : 8;
  
  if (totalDrops > maxDropsForAge) {
    errors.push(`Total drops (${totalDrops}) exceeds safe limit for age ${demographics.specificAge} (max: ${maxDropsForAge})`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings: [...new Set(warnings)], // Remove duplicates
    recommendedDilution,
    carrierOilAmount
  };
}
