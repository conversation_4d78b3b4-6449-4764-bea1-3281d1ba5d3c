/**
 * @fileoverview Droplet color system for essential oil visualization
 * Uses index-based color assignment with theme variables only
 */

/**
 * Droplet colors using CSS custom properties from theme
 * Colors are assigned by oil position/index in the array, NOT by oil name
 */
export const DROPLET_COLORS = [
  'hsl(var(--primary))',      // Oil index 0
  'hsl(var(--chart-1))',      // Oil index 1
  'hsl(var(--chart-2))',      // Oil index 2
  'hsl(var(--chart-3))',      // Oil index 3
  'hsl(var(--chart-4))',      // Oil index 4
  'hsl(var(--chart-5))',      // Oil index 5
  'hsl(var(--accent))',       // Oil index 6
  'hsl(var(--secondary))',    // Oil index 7
  'hsl(var(--muted))',        // Oil index 8
  'hsl(var(--destructive))'   // Oil index 9
] as const;

/**
 * Get droplet color by oil index position
 * @param oilIndex - Position of oil in the array (0-based)
 * @returns CSS color string using theme variables
 */
export const getDropletColor = (oilIndex: number): string => {
  return DROPLET_COLORS[oilIndex % DROPLET_COLORS.length];
};