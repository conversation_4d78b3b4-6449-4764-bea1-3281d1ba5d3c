/**
 * @fileoverview Utility functions for transforming and processing API data
 * in the Essential Oil Recipe Creator feature.
 */

import type {
  HealthConcernData,
  DemographicsData,
  PotentialCause,
  PotentialSymptom,
  TherapeuticProperty,
  EssentialOil,
  PropertyOilSuggestions,
  BaseApiRequest
} from '../types/recipe.types';

import { 
  DEFAULT_API_LANGUAGE,
  AGE_CATEGORY_OPTIONS 
} from '../constants/recipe.constants';

import { StreamRequest } from '@/lib/ai/utils/streaming-utils';
import { enrichOilsForAI } from './oil-data-enrichment';
import { performComprehensiveDataFlowVerification, logAIDataConfirmation } from './data-flow-verification';

// ============================================================================
// REQUEST TRANSFORMATION UTILITIES
// ============================================================================

/**
 * Creates a standardized StreamRequest object for AI streaming
 * @param feature The feature name (e.g., 'recipe-wizard')
 * @param step The step name (e.g., 'suggested-oils', 'therapeutic-properties', 'final-recipes')
 * @param healthConcern Health concern data
 * @param demographics Demographics data
 * @param selectedCauses Selected causes
 * @param selectedSymptoms Selected symptoms
 * @param userLanguage User language
 * @param property Optional therapeutic property for property-specific requests
 * @param additionalData Optional additional data for specific steps (e.g., timeSlot, suggestedOils)
 * @returns A properly formatted StreamRequest object
 */
export function createStreamRequest(
  feature: string,
  step: string,
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  userLanguage: string = DEFAULT_API_LANGUAGE,
  property?: TherapeuticProperty,
  additionalData?: any
): StreamRequest {
    // Special debug logging for potential-causes to trace the demographics issue
  if (step === 'potential-causes') {
    console.log('🔍 [DEBUG] createStreamRequest potential-causes input params:', {
      healthConcern,
      demographics,
      selectedCausesCount: selectedCauses.length,
      selectedSymptomsCount: selectedSymptoms.length,
      userLanguage,
      additionalData
    });
  }

  // Special debug logging for final-recipes to trace the demographics issue
  if (step === 'final-recipes') {
    console.log('🔍 [DEBUG] createStreamRequest final-recipes input params:', {
      healthConcern,
      demographics,
      selectedCausesCount: selectedCauses.length,
      selectedSymptomsCount: selectedSymptoms.length,
      userLanguage,
      additionalData
    });
  }

  const data = {
    health_concern: healthConcern.healthConcern.trim(),
    gender: demographics.gender,
    age_category: demographics.ageCategory,
    age_specific: demographics.specificAge.toString(),
    user_language: userLanguage,
    selected_causes: selectedCauses,
    selected_symptoms: selectedSymptoms
  };

  // Additional debug logging for potential-causes data object
  if (step === 'potential-causes') {
    console.log('🔍 [DEBUG] createStreamRequest potential-causes data object:', {
      health_concern: data.health_concern,
      gender: data.gender,
      age_category: data.age_category,
      age_specific: data.age_specific,
      user_language: data.user_language,
      selected_causes: data.selected_causes?.length || 0,
      selected_symptoms: data.selected_symptoms?.length || 0,
    });
  }

  // Additional debug logging for final-recipes data object
  if (step === 'final-recipes') {
    console.log('🔍 [DEBUG] createStreamRequest final-recipes data object:', {
      health_concern: data.health_concern,
      gender: data.gender,
      age_category: data.age_category,
      age_specific: data.age_specific,
      user_language: data.user_language,
      selected_causes: data.selected_causes?.length || 0,
      selected_symptoms: data.selected_symptoms?.length || 0,
      time_of_day: (data as any).time_of_day,
      suggested_oils: (data as any).suggested_oils?.length || 0
    });
  }

  // Special handling for final-recipes step
  if (step === 'final-recipes' && additionalData) {
    const { timeSlot, suggestedOils } = additionalData;

    // Use enhanced enrichment utility with safety library
    const enrichmentResult = enrichOilsForAI(suggestedOils || [], true);
    const enrichedOils = enrichmentResult.oils;
    const safetyLibrary = enrichmentResult.safety_library;
    
    console.log('🛡️ [SAFETY] Safety library included in AI prompt:', {
      internal_use_options: Object.keys(safetyLibrary.internal_use).length,
      dilution_options: Object.keys(safetyLibrary.dilution).length,
      phototoxicity_options: Object.keys(safetyLibrary.phototoxicity).length,
      total_safety_objects: Object.keys(safetyLibrary.internal_use).length + 
                            Object.keys(safetyLibrary.dilution).length + 
                            Object.keys(safetyLibrary.phototoxicity).length,
      safety_library_formatted_length: enrichmentResult.safety_library_formatted.length,
      safety_library_preview: enrichmentResult.safety_library_formatted.substring(0, 200) + '...'
    });

    // Perform comprehensive data flow verification
    performComprehensiveDataFlowVerification(
      suggestedOils || [], 
      `final-recipes-${timeSlot}`
    );

    // Log AI data confirmation
    logAIDataConfirmation(enrichedOils, `AI-final-recipes-${timeSlot}`);

    const finalData = {
      ...data,
      time_of_day: timeSlot,
      suggested_oils: enrichedOils,
      safety_library: safetyLibrary,
      safety_library_formatted: enrichmentResult.safety_library_formatted
    };

    // DEBUG: Log the final data being returned for final-recipes
    console.log('🔍 [DEBUG] createStreamRequest final-recipes FINAL data being returned:', {
      health_concern: finalData.health_concern,
      gender: finalData.gender,
      age_category: finalData.age_category,
      age_specific: finalData.age_specific,
      user_language: finalData.user_language,
      time_of_day: finalData.time_of_day,
      suggested_oils_count: finalData.suggested_oils?.length || 0,
      safety_library_included: !!finalData.safety_library,
      safety_library_stats: finalData.safety_library ? {
        internal_use: Object.keys(finalData.safety_library.internal_use).length,
        dilution: Object.keys(finalData.safety_library.dilution).length,
        phototoxicity: Object.keys(finalData.safety_library.phototoxicity).length,
        pregnancy_nursing: Object.keys(finalData.safety_library.pregnancy_nursing).length,
        child_safety: Object.keys(finalData.safety_library.child_safety).length
      } : null,
      safety_library_formatted_included: !!finalData.safety_library_formatted,
      enriched_oils_with_scores: finalData.suggested_oils?.map((oil: any) => ({
        oil_id: oil.oil_id,
        name_localized: oil.name_localized,
        final_relevance_score: oil.final_relevance_score,
        properties_count: oil.properties?.length || 0
      })) || [],
      selected_causes_count: finalData.selected_causes?.length || 0,
      selected_symptoms_count: finalData.selected_symptoms?.length || 0
    });

    return {
      feature,
      step,
      data: finalData
    };
  }

  // Add therapeutic property if provided
  if (property) {
    // Special handling for oil-enrichment step - extract suggested_oils to top level
    if (step === 'oil-enrichment' && property.suggested_oils) {
      return {
        feature,
        step,
        data: {
          ...data,
          therapeutic_property: {
            property_id: property.property_id,
            property_name_localized: property.property_name_localized,
            property_name_english: property.property_name_english,
            description_contextual_localized: property.description_contextual_localized
          },
          // Extract suggested_oils to top level for template variables
          suggested_oils: property.suggested_oils ? property.suggested_oils.map(oil => ({ ...oil, isEnriched: oil.isEnriched ?? false })) : []
        }
      };
    }
    return {
      feature,
      step,
      data: {
        ...data,
        therapeutic_property: property
      }
    };
  }

  // DEBUG: Log the final data being returned for potential-causes
  if (step === 'potential-causes') {
    console.log('🔍 [DEBUG] createStreamRequest potential-causes FINAL data being returned:', {
      health_concern: data.health_concern,
      gender: data.gender,
      age_category: data.age_category,
      age_specific: data.age_specific,
      user_language: data.user_language,
      selected_causes: data.selected_causes?.length || 0,
      selected_symptoms: data.selected_symptoms?.length || 0,
      hasDemographics: !!(data.gender && data.age_category && data.age_specific)
    });
  }

  return {
    feature,
    step,
    data
  };
}

/**
 * Transforms form data into API request format
 */
export function transformToApiRequest(
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  userLanguage: string = DEFAULT_API_LANGUAGE
): BaseApiRequest {
  return {
    health_concern: healthConcern.healthConcern.trim(),
    gender: demographics.gender,
    age_category: demographics.ageCategory,
    age_specific: demographics.specificAge.toString(),
    user_language: userLanguage
  };
}

/**
 * Validates age against selected age category
 */
export function validateAgeCategory(age: number, ageCategory: string): boolean {
  const category = AGE_CATEGORY_OPTIONS.find(cat => cat.value === ageCategory);
  if (!category) return false;
  
  return age >= category.minAge && age <= category.maxAge;
}

/**
 * Gets the appropriate age category for a given age
 */
export function getAgeCategoryForAge(age: number): string | null {
  const category = AGE_CATEGORY_OPTIONS.find(
    cat => age >= cat.minAge && age <= cat.maxAge
  );
  return category?.value || null;
}

/**
 * Sanitizes health concern input
 */
export function sanitizeHealthConcern(input: string): string {
  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s\-.,!?]/g, '') // Remove special characters except basic punctuation
    .substring(0, 500); // Ensure max length
}

// ============================================================================
// RESPONSE TRANSFORMATION UTILITIES
// ============================================================================

/**
 * Sorts potential causes by relevancy (if available) or alphabetically
 */
export function sortPotentialCauses(causes: PotentialCause[]): PotentialCause[] {
  return [...causes].sort((a, b) => {
    // Primary sort by cause name alphabetically
    return a.cause_name.localeCompare(b.cause_name);
  });
}

/**
 * Sorts potential symptoms by relevancy or alphabetically
 */
export function sortPotentialSymptoms(symptoms: PotentialSymptom[]): PotentialSymptom[] {
  return [...symptoms].sort((a, b) => {
    return a.symptom_name.localeCompare(b.symptom_name);
  });
}

/**
 * Sorts therapeutic properties by relevancy score (highest first)
 */
export function sortTherapeuticProperties(properties: TherapeuticProperty[]): TherapeuticProperty[] {
  return [...properties].sort((a, b) => {
    // Primary sort by relevancy_score (highest first)
    if (a.relevancy_score !== b.relevancy_score) {
      return b.relevancy_score - a.relevancy_score;
    }
    // Secondary sort by localized name alphabetically
    return a.property_name_localized.localeCompare(b.property_name_localized);
  });
}

/**
 * Sorts essential oils by relevancy score (highest first)
 */
export function sortEssentialOils(oils: EssentialOil[]): EssentialOil[] {
  return [...oils].sort((a, b) => {
    // Primary sort by relevancy (highest first)
    const aRelevancy = a.relevancy ?? 0;
    const bRelevancy = b.relevancy ?? 0;
    
    if (aRelevancy !== bRelevancy) {
      return bRelevancy - aRelevancy;
    }
    
    // Secondary sort by name alphabetically
    const aName = a.name_local_language || a.name_english || '';
    const bName = b.name_local_language || b.name_english || '';
    return aName.localeCompare(bName);
  });
}

/**
 * Groups and sorts oil suggestions by therapeutic property
 */
export function sortPropertyOilSuggestions(suggestions: PropertyOilSuggestions[]): PropertyOilSuggestions[] {
  return suggestions.map(suggestion => ({
    ...suggestion,
    suggested_oils: sortEssentialOils(suggestion.suggested_oils).map(oil => ({ ...oil, isEnriched: oil.isEnriched ?? false }))
  })).sort((a, b) => {
    // Sort by property name alphabetically
    return a.property_name_localized.localeCompare(b.property_name_localized);
  });
}

// ============================================================================
// DATA FILTERING UTILITIES
// ============================================================================

/**
 * Filters causes based on search query
 */
export function filterCausesBySearch(causes: PotentialCause[], searchQuery: string): PotentialCause[] {
  if (!searchQuery.trim()) return causes;
  
  const query = searchQuery.toLowerCase().trim();
  return causes.filter(cause =>
    cause.cause_name.toLowerCase().includes(query) ||
    cause.cause_suggestion.toLowerCase().includes(query) ||
    cause.explanation.toLowerCase().includes(query)
  );
}

/**
 * Filters symptoms based on search query
 */
export function filterSymptomsBySearch(symptoms: PotentialSymptom[], searchQuery: string): PotentialSymptom[] {
  if (!searchQuery.trim()) return symptoms;
  
  const query = searchQuery.toLowerCase().trim();
  return symptoms.filter(symptom =>
    symptom.symptom_name.toLowerCase().includes(query) ||
    symptom.symptom_suggestion.toLowerCase().includes(query) ||
    symptom.explanation.toLowerCase().includes(query)
  );
}

/**
 * Filters oils based on search query
 */
export function filterOilsBySearch(oils: EssentialOil[], searchQuery: string): EssentialOil[] {
  if (!searchQuery.trim()) return oils;
  
  const query = searchQuery.toLowerCase().trim();
  return oils.filter(oil =>
    oil.name_english.toLowerCase().includes(query) ||
    (oil.name_local_language || '').toLowerCase().includes(query) ||
    (oil.oil_description || '').toLowerCase().includes(query)
  );
}

/**
 * Filters therapeutic properties by minimum relevancy score
 */
export function filterPropertiesByRelevancy(
  properties: TherapeuticProperty[], 
  minRelevancy: number = 1
): TherapeuticProperty[] {
  return properties.filter(property => (property.relevancy_score ?? 0) >= minRelevancy);
}

// ============================================================================
// DATA VALIDATION UTILITIES
// ============================================================================

/**
 * Validates that required selections are made
 */
export function validateSelections(
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (selectedCauses.length === 0) {
    errors.push('At least one cause must be selected');
  }
  
  if (selectedSymptoms.length === 0) {
    errors.push('At least one symptom must be selected');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validates that API response data is complete
 */
export function validateApiResponseData(data: any, expectedFields: string[]): boolean {
  if (!data || typeof data !== 'object') return false;
  
  return expectedFields.every(field => {
    const value = data[field];
    return value !== undefined && value !== null && 
           (typeof value !== 'string' || value.trim().length > 0);
  });
}

// ============================================================================
// DATA FORMATTING UTILITIES
// ============================================================================

/**
 * Formats relevancy score for display
 */
export function formatRelevancyScore(score: number): string {
  const stars = '★'.repeat(score) + '☆'.repeat(5 - score);
  return `${stars} (${score}/5)`;
}

/**
 * Truncates text to specified length with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

/**
 * Formats oil names for display (prioritizes local language)
 */
export function formatOilName(oil: EssentialOil): string {
  if (oil.name_local_language && oil.name_local_language !== oil.name_english) {
    return `${oil.name_local_language} (${oil.name_english})`;
  }
  return oil.name_english;
}

/**
 * Creates a summary of selected items
 */
export function createSelectionSummary(
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[]
): string {
  const causesText = selectedCauses.length === 1 
    ? '1 cause' 
    : `${selectedCauses.length} causes`;
  
  const symptomsText = selectedSymptoms.length === 1 
    ? '1 symptom' 
    : `${selectedSymptoms.length} symptoms`;
  
  return `Selected: ${causesText} and ${symptomsText}`;
}

// ============================================================================
// DATA EXPORT UTILITIES
// ============================================================================

/**
 * Exports wizard data to a shareable format
 */
export function exportWizardData(
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  therapeuticProperties: TherapeuticProperty[],
  suggestedOils: PropertyOilSuggestions[]
) {
  return {
    healthConcern: healthConcern.healthConcern,
    demographics: {
      gender: demographics.gender,
      ageCategory: demographics.ageCategory,
      specificAge: demographics.specificAge
    },
    selectedCauses: selectedCauses.map(cause => ({
      name: cause.cause_name,
      suggestion: cause.cause_suggestion
    })),
    selectedSymptoms: selectedSymptoms.map(symptom => ({
      name: symptom.symptom_name,
      suggestion: symptom.symptom_suggestion
    })),
    therapeuticProperties: therapeuticProperties.map(prop => ({
      name: prop.property_name_localized,
      description: prop.description_contextual_localized,
      relevancy: prop.relevancy_score
    })),
    suggestedOils: suggestedOils.map(propOils => ({
      property: propOils.property_name_localized,
      description: propOils.description_contextual_localized,
      oils: propOils.suggested_oils.map(oil => ({
        name: oil.name_localized || oil.name_english,
        description: oil.oil_description,
        relevancy: oil.relevancy_to_property_score ?? 0
      }))
    })),
    exportedAt: new Date().toISOString()
  };
}

/**
 * Creates a text summary of the recipe
 */
export function createRecipeSummary(
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  suggestedOils: PropertyOilSuggestions[]
): string {
  const lines: string[] = [];
  
  lines.push(`Health Concern: ${healthConcern.healthConcern}`);
  lines.push(`Demographics: ${demographics.gender}, ${demographics.specificAge} years old`);
  lines.push('');
  
  lines.push('Selected Causes:');
  selectedCauses.forEach(cause => {
    lines.push(`• ${cause.cause_name}`);
  });
  lines.push('');
  
  lines.push('Selected Symptoms:');
  selectedSymptoms.forEach(symptom => {
    lines.push(`• ${symptom.symptom_name}`);
  });
  lines.push('');
  
  lines.push('Recommended Essential Oils:');
  suggestedOils.forEach(propOils => {
    lines.push(`\n${propOils.property_name_localized}:`);
    lines.push(`   ${propOils.description_contextual_localized}`);
    propOils.suggested_oils.forEach(oil => {
      lines.push(`• ${oil.name_localized || oil.name_english} (${oil.relevancy_to_property_score ?? 0}/5)`);
    });
  });
  
  return lines.join('\n');
}

// Add a utility to map raw API property to canonical TherapeuticProperty
export function mapToTherapeuticProperty(raw: any): TherapeuticProperty {
  return {
    property_id: raw.property_id,
    property_name_localized: raw.property_name_localized,
    property_name_english: raw.property_name_english,
    description_contextual_localized: raw.description_contextual_localized,
    addresses_cause_ids: raw.addresses_cause_ids || [],
    addresses_symptom_ids: raw.addresses_symptom_ids || [],
    relevancy_score: raw.relevancy_score,
    suggested_oils: (raw.suggested_oils || []).map((oil: any) => ({ ...oil, isEnriched: oil.isEnriched ?? false })),
    isLoadingOils: raw.isLoadingOils ?? false,
    errorLoadingOils: raw.errorLoadingOils ?? null,
    isEnriched: raw.isEnriched ?? false,
  };
}

// Fix mapToPropertyOilSuggestions to ensure suggested_oils is always EnrichedEssentialOil[]
export function mapToPropertyOilSuggestions(raw: any): PropertyOilSuggestions {
  return {
    property_id: raw.property_id,
    property_name_localized: raw.property_name_localized,
    property_name_english: raw.property_name_english,
    description_contextual_localized: raw.description_contextual_localized,
    suggested_oils: (raw.suggested_oils || []).map((oil: any) => ({ ...oil, isEnriched: oil.isEnriched ?? false })),
    isEnriched: raw.isEnriched ?? false,
  };
}
