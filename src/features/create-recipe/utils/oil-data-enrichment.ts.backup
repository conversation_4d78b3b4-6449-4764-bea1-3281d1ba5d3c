/**
 * @fileoverview Oil Data Enrichment Utility
 * 
 * This utility extracts and centralizes the oil enrichment logic from the debug overlay
 * to provide consistent data transformation across the application. It processes
 * therapeutic properties to create enriched oil data with scoring and safety information.
 */

import type { TherapeuticProperty } from '../types/recipe.types';
import { 
  enrichRecommendationDataWithScores, 
  calculateFinalRankedList,
  type EnrichedRecommendationData 
} from './recommendation-scoring';

/**
 * Interface for enriched oil data structure sent to AI
 */
export interface EnrichedOilForAI {
  oil_id: string;
  name_english: string;
  name_localized: string;
  name_scientific?: string;
  safety: SafetyDataReferences;
  final_relevance_score: number;
  properties: OilPropertyContext[];
  properties_formatted: string;
}

/**
 * Interface for oil property context within each oil
 */
export interface OilPropertyContext {
  property_id: string;
  match_rationale_localized: string | null;
  relevancy_to_property_score: number | null;
  recommendation_instance_score: number;
}

/**
 * Interface for safety data references using Supabase UUIDs
 */
export interface SafetyDataReferences {
  internal_use: string | null;
  dilution: string | null;
  phototoxicity: string | null;
  pregnancy_nursing: string[];
  child_safety: string[];
}

/**
 * Formats properties array into a readable string for template processing.
 * This function pre-processes the properties data to avoid nested handlebars loops.
 * 
 * @param properties - Array of oil property contexts
 * @returns Formatted string with proper indentation and separators
 */
export function formatPropertiesForTemplate(properties: OilPropertyContext[]): string {
  if (!properties || properties.length === 0) {
    return '';
  }
  
  return properties.map((prop, index) => {
    const lines = [
      `       - Property ID: ${prop.property_id}`,
      `       - Match Rationale: ${prop.match_rationale_localized || ''}`,
      `       - Property Relevancy Score: ${prop.relevancy_to_property_score ?? ''}/5`,
      `       - Recommendation Instance Score: ${prop.recommendation_instance_score ?? ''}`
    ];
    
    // Add separator between properties (but not after last)
    if (index < properties.length - 1) {
      lines.push('       ---');
    }
    
    return lines.join('\n');
  }).join('\n');
}

/**
 * Filters therapeutic properties to only include those ready for AI processing.
 * This ensures consistent filtering logic across debug overlay and final-recipes.
 * 
 * @param therapeuticProperties - Array of therapeutic properties
 * @returns Filtered array of properties that are enriched and have suggested oils
 */
export function filterPropertiesForAI(therapeuticProperties: TherapeuticProperty[]): TherapeuticProperty[] {
  return therapeuticProperties.filter(prop => 
    prop.isEnriched && 
    prop.suggested_oils && 
    prop.suggested_oils.length > 0
  );
}

/**
 * Enriches therapeutic properties data to create comprehensive oil data for AI processing.
 * This function extracts the exact logic from the debug overlay to ensure consistency.
 * 
 * @param therapeuticProperties - Array of therapeutic properties with suggested oils
 * @returns Array of enriched oil data with scoring and safety information
 */
export function enrichOilsForAI(therapeuticProperties: TherapeuticProperty[]): EnrichedOilForAI[] {
  // Safety map for deduplication using actual Supabase UUIDs
  const safetyMap = new Map();

  // Helper to get or create a safety entry using actual Supabase UUID or content hash
  function getSafetyId(safetySubObj: Record<string, any> | null | undefined, category: string, actualId?: string): string | null {
    if (!safetySubObj) return null;
    // Use the actual Supabase UUID if available, otherwise use a hash of the object content
    const dedupKey = actualId || `${category}_${JSON.stringify(safetySubObj)}`;
    if (!safetyMap.has(dedupKey)) {
      safetyMap.set(dedupKey, { ...(safetySubObj || {}), safety_id: dedupKey, category });
    }
    return dedupKey;
  }

  // 1. Build a mapping of oil_id to property context for each oil
  const oilPropertiesMap: Record<string, any[]> = {};
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (!oil?.oil_id) return;
      if (!oilPropertiesMap[oil.oil_id]) oilPropertiesMap[oil.oil_id] = [];
      // Calculate recommendation_instance_score using the scoring system formula
      const propertyCoverageScore = prop.relevancy_score / 5; // MAX_RELEVANCY_SCORE = 5
      const recommendationInstanceScore = ((oil as any)['relevancy_to_property_score'] ?? 0) * prop.relevancy_score * propertyCoverageScore;
      oilPropertiesMap[oil.oil_id]?.push({
        property_id: prop.property_id,
        match_rationale_localized: (oil as any)['match_rationale_localized'] ?? null,
        relevancy_to_property_score: (oil as any)['relevancy_to_property_score'] ?? null,
        recommendation_instance_score: Math.round(recommendationInstanceScore * 100) / 100
      });
    });
  });

  // 2. Collect all unique oils from all properties' suggested_oils
  const oilMap = new Map();
  therapeuticProperties.forEach(prop => {
    (prop.suggested_oils || []).forEach(oil => {
      if (oil?.oil_id && !oilMap.has(oil.oil_id)) {
        // Only include the essential, intrinsic oil fields (no property-specific context)
        // Remove unwanted fields as before
        const { isEnriched, enrichment_status, botanical_mismatch, similarity_score, search_query, enrichment_timestamp, name_botanical, match_rationale_localized, relevancy_to_property_score, ...oilRest } = oil;
        oilMap.set(oil.oil_id, {
          ...oilRest
        });
      }
    });
  });

  // 3. Create enriched recommendation data with calculated scores
  // Use the original therapeuticProperties (with suggested_oils) for scoring
  const recommendationData = {
    therapeuticProperties: therapeuticProperties,
    oils: Array.from(oilMap.values())
  };

  // 4. Enrich the data with calculated scores
  const enrichedData: EnrichedRecommendationData = enrichRecommendationDataWithScores(recommendationData);
  
  // 5. Calculate final ranked list
  const finalRankedOils = calculateFinalRankedList(enrichedData);
  
  // 6. Merge final_relevance_score and properties array into each oil in the deduplicated oils array
  const oilScoreMap = new Map();
  finalRankedOils.forEach(oil => {
    oilScoreMap.set(oil.oil_id, oil.final_relevance_score);
  });
  
  const oilsWithScore = Array.from(oilMap.values()).map((oil: Record<string, any>) => {
    let safetyRef: SafetyDataReferences = {
      internal_use: null,
      dilution: null,
      phototoxicity: null,
      pregnancy_nursing: [],
      child_safety: []
    };
    
    if (oil['safety']) {
      const safety = oil['safety'];
      safetyRef = {
        internal_use: getSafetyId(safety['internal_use'], 'internal_use', safety['internal_use_id']),
        dilution: getSafetyId(safety['dilution'], 'dilution', safety['dilution_id']),
        phototoxicity: getSafetyId(safety['phototoxicity'], 'phototoxicity', safety['phototoxicity_id']),
        pregnancy_nursing: Array.isArray(safety['pregnancy_nursing'])
          ? (safety['pregnancy_nursing'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['id'] || undefined;
              return getSafetyId(safeItem, 'pregnancy_nursing', actualId);
            }).filter(Boolean) as string[]
          : [],
        child_safety: Array.isArray(safety['child_safety'])
          ? (safety['child_safety'] as Array<unknown>).map((item) => {
              const safeItem = item as Record<string, any>;
              const actualId = safeItem['age_range_id'] || undefined;
              return getSafetyId(safeItem, 'child_safety', actualId);
            }).filter(Boolean) as string[]
          : []
      };
    }
    
    // Build the properties array for this oil, including relevancy and match_rationale_localized per property context
    const properties: OilPropertyContext[] = (therapeuticProperties || []).reduce((acc: OilPropertyContext[], prop) => {
      const oilInProp = (prop.suggested_oils || []).find((o: any) => o.oil_id === oil['oil_id']);
      if (oilInProp) {
        // Ensure we have valid numbers for calculation
        const relevancyToProperty = oilInProp.relevancy_to_property_score ?? 0;
        const propertyRelevancy = prop.relevancy_score ?? 0;
        const propertyCoverageScore = propertyRelevancy / 5; // MAX_RELEVANCY_SCORE = 5
        const recommendationInstanceScore = relevancyToProperty * propertyRelevancy * propertyCoverageScore;
        
        acc.push({
          property_id: prop.property_id,
          match_rationale_localized: oilInProp.match_rationale_localized ?? null,
          relevancy_to_property_score: oilInProp.relevancy_to_property_score ?? null,
          recommendation_instance_score: Math.round(recommendationInstanceScore * 100) / 100
        });
      }
      return acc;
    }, []);
    
    // Get the final relevance score, ensuring it's a valid number
    const finalRelevanceScore = oilScoreMap.get(oil['oil_id']) ?? 0;
    
    return {
      ...oil,
      final_relevance_score: finalRelevanceScore,
      properties,
      properties_formatted: formatPropertiesForTemplate(properties),
      safety: safetyRef
    } as EnrichedOilForAI;
  });

  return oilsWithScore;
}