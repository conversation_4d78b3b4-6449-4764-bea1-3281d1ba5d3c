/**
 * @fileoverview TypeScript type definitions for the Essential Oil Recipe Creator feature.
 * These types are based on the AromaRx API responses and wizard step requirements.
 */

// ============================================================================
// WIZARD STEP TYPES
// ============================================================================

/**
 * Enum representing the available wizard steps
 */
export enum RecipeStep {
  HEALTH_CONCERN = 'health-concern',
  DEMOGRAPHICS = 'demographics',
  CAUSES = 'causes',
  SYMPTOMS = 'symptoms',
  PROPERTIES = 'properties',
  FINAL_RECIPES = 'final-recipes'
  // Note: FINAL_RECIPES step replaces the unused OILS step
}

/**
 * Type for wizard step navigation
 */
export type RecipeStepKey = keyof typeof RecipeStep;

// ============================================================================
// FORM DATA TYPES
// ============================================================================

/**
 * Step 1: Health concern input data
 */
export interface HealthConcernData {
  healthConcern: string;
}

/**
 * Step 2: Demographics form data
 */
export interface DemographicsData {
  gender: 'male' | 'female';
  ageCategory: string;
  specificAge: number;
}

/**
 * Step 3: Potential cause selection data
 */
export interface PotentialCause {
  cause_id: string; // AI-generated unique ID
  cause_name: string;
  cause_suggestion: string;
  explanation: string;
}

/**
 * Step 4: Symptom selection data
 */
export interface PotentialSymptom {
  symptom_id: string; // AI-generated unique ID
  symptom_name: string;
  symptom_suggestion: string;
  explanation: string;
}

/**
 * Step 5: Therapeutic property data
 */
export interface TherapeuticProperty {
  property_id: string;
  property_name_localized: string;
  property_name_english: string;
  description_contextual_localized: string;
  addresses_cause_ids: string[];
  addresses_symptom_ids: string[];
  relevancy_score: number;
  suggested_oils?: EnrichedEssentialOil[];
  isLoadingOils?: boolean;
  errorLoadingOils?: string | null;
  isEnriched: boolean;
}

/**
 * Step 6: Essential oil suggestion data
 */
export interface EssentialOil {
  oil_id: string;
  name_english: string;
  name_botanical: string;
  name_localized: string;
  match_rationale_localized: string;
  relevancy_to_property_score: number; // Changed: Now integer 1-5 (was decimal 0-1)
  // Legacy fields for backward compatibility
  name_local_language?: string;
  oil_description?: string;
  relevancy?: number;
  isEnriched?: boolean;
}

/**
 * Enriched essential oil with safety information
 */
export interface EnrichedEssentialOil extends EssentialOil {
  supabase_id?: string;
  name_scientific?: string;  // Scientific name from safety database
  description?: string;
  safety?: OilSafetyInfo;
  isEnriched: boolean;
  botanical_mismatch?: boolean;  // Flag indicating if botanical names differ
  similarity_score?: number;  // Similarity score from vector search
  enrichment_status?: 'enriched' | 'not_found' | 'discarded';  // Status from enrichment process
  search_query?: string;  // The query used for enrichment
  enrichment_timestamp?: string;  // When the enrichment was performed
}

/**
 * Oil safety information structure
 */
export interface OilSafetyInfo {
  // Safety IDs from Supabase
  internal_use_id?: string;
  dilution_id?: string;
  phototoxicity_id?: string;
  pregnancy_nursing_ids?: string[];
  child_safety_ids?: string[];
  
  // Safety data objects
  internal_use?: {
    name?: string | null;
    code?: string | null;
    description?: string | null;
    guidance?: string | null;
  };
  dilution?: {
    name?: string | null;
    description?: string | null;
    percentage_max?: number | null;
    percentage_min?: number | null;
    ratio?: string | null;
  };
  phototoxicity?: {
    status?: string | null;
    guidance?: string | null;
    description?: string | null;
  };
  pregnancy_nursing?: Array<{
    id?: string | null;
    name?: string | null;
    status_description?: string | null;
    code?: string | null;
    usage_guidance?: string | null;
    description?: string | null;
  }>;
  child_safety?: Array<{
    age_range_id?: string | null;
    age_range?: string | null;
    safety_notes?: string | null;
  }>;
}

/**
 * Represents an essential oil suggestion for a therapeutic property
 */
export interface PropertyOilSuggestions {
  property_id: string;
  property_name_localized: string;
  property_name_english: string;
  description_contextual_localized: string;
  suggested_oils: EnrichedEssentialOil[];
  isEnriched: boolean;
}

// ============================================================================
// FINAL RECIPES STEP TYPES
// ============================================================================

/**
 * Time slots for recipe protocols
 */
export type RecipeTimeSlot = 'morning' | 'mid-day' | 'night';

/**
 * Container recommendation for recipe preparation
 */
export interface ContainerRecommendation {
  size_ml: number;
  container_type: 'roller_bottle' | 'dropper_bottle' | 'spray_bottle' | 'jar';
  material: 'amber_glass' | 'clear_glass' | 'plastic';
  dispenser_type: 'roller' | 'dropper' | 'spray' | 'none';
  rationale_localized: string;
}

/**
 * Safety warning for recipe usage
 */
export interface SafetyWarning {
  warning_type: 'age_restriction' | 'pregnancy' | 'phototoxicity' | 'dilution' | 'general';
  severity: 'low' | 'medium' | 'high';
  message_localized: string;
  guidance_localized: string;
}

/**
 * Individual recipe protocol for a specific time of day
 */
export interface FinalRecipeProtocol {
  // ... existing fields ...
  recipe_id: string;
  time_slot: RecipeTimeSlot;
  recipe_name_localized: string;
  description_localized: string;
  
  // ADD THESE NEW FIELDS
  recipe_theme_localized: string;
  holistic_benefit_localized: string;
  ritual_suggestion_localized: string;
  disclaimer_localized: string;

  selected_oils: Array<{
    oil_id: string;
    name_localized: string;
    name_botanical: string;
    drops_count: number;
    rationale_localized: string; // Ensure this field exists
  }>;
  carrier_oil: {
    name_localized: string;
    amount_ml: number;
  };
  total_drops: number;
  total_volume_ml: number;
  application_method_localized: string;
  frequency_localized: string;
  duration_localized: string;
  container_recommendation: ContainerRecommendation;
  safety_warnings: SafetyWarning[];
  preparation_steps_localized: string[];
  usage_instructions_localized: string[];
}

/**
 * Status tracking for individual recipe generation
 */
export interface FinalRecipeStatus {
  status: 'idle' | 'loading' | 'success' | 'error';
  error_message?: string;
  retry_count: number;
  last_attempt?: Date;
}

/**
 * Complete final recipes state for all time slots
 */
export interface FinalRecipesState {
  morning: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  midDay: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  night: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  isGenerating: boolean;
  hasStartedGeneration: boolean;
  globalError: string | null;
}

// ============================================================================
// OIL ENRICHMENT RESPONSE TYPES
// ============================================================================

/**
 * Oil enrichment response metadata following standardized schema
 */
export interface OilEnrichmentMeta {
  step_name: string;
  request_id: string; // UUID format
  timestamp_utc: string; // ISO date format
  version: string;
  user_language: string;
  status: string;
  message: string;
}

/**
 * Therapeutic property context for oil enrichment
 */
export interface TherapeuticPropertyContext {
  property_id: string; // UUID format
  property_name_localized: string;
  property_name_english: string;
  description_localized: string;
}

/**
 * Echo data structure for oil enrichment response
 */
export interface OilEnrichmentEcho {
  health_concern_input: string;
  user_info_input: {
    gender: string;
    age_category: string;
    age_specific: string;
    age_unit: string;
  };
  selected_cause_ids: string[];
  selected_symptom_ids: string[];
  therapeutic_property_ids: string[];
}

/**
 * Complete oil enrichment response following the new standardized schema
 */
export interface OilEnrichmentResponse {
  meta: OilEnrichmentMeta;
  data: {
    therapeutic_property_context: TherapeuticPropertyContext;
    enriched_oils: EnrichedEssentialOil[];
  };
  echo: OilEnrichmentEcho;
}

// ============================================================================
// API REQUEST/RESPONSE TYPES
// ============================================================================

/**
 * Base API request structure
 */
export interface BaseApiRequest {
  health_concern: string;
  gender: 'male' | 'female';
  age_category: string;
  age_specific: string;
  user_language: string;
}

/**
 * API request for potential causes step
 */
export interface PotentialCausesRequest extends BaseApiRequest {
  step: 'PotentialCauses';
}

/**
 * API request for potential symptoms step
 */
export interface PotentialSymptomsRequest extends BaseApiRequest {
  selected_causes: PotentialCause[];
  step: 'PotentialSymptoms';
}

/**
 * API request for medical properties step
 */
export interface MedicalPropertiesRequest extends BaseApiRequest {
  selected_causes: PotentialCause[];
  selected_symptoms: PotentialSymptom[];
  step: 'MedicalProperties';
}

/**
 * API request for suggested oils step
 */
export interface SuggestedOilsRequest extends BaseApiRequest {
  selected_causes: PotentialCause[];
  selected_symptoms: PotentialSymptom[];
  therapeutic_properties: TherapeuticProperty[];
  step: 'SuggestedOils';
}

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T> {
  index: number;
  message: {
    role: 'assistant';
    content: T;
    refusal: null;
    annotations: any[];
  };
  logprobs: null;
  finish_reason: 'stop';
}

/**
 * API response for potential causes
 */
export interface PotentialCausesResponse {
  potential_causes: PotentialCause[];
}

/**
 * API response for potential symptoms
 */
export interface PotentialSymptomsResponse {
  potential_symptoms: PotentialSymptom[];
}

/**
 * API response for medical properties
 */
export interface MedicalPropertiesResponse {
  health_concern_in_english: string;
  therapeutic_properties: TherapeuticProperty[];
}

/**
 * API response for suggested oils - Updated to match suggested-oils.yaml schema
 */
export interface SuggestedOilsResponse {
  therapeutic_property_context: {
    property_id: string;
    property_name_localized: string;
    property_name_english: string;
    description_localized: string;
  };
  suggested_oils: EssentialOil[];
}

// ============================================================================
// WIZARD STATE TYPES
// ============================================================================

/**
 * Complete wizard state containing all step data
 */
export interface RecipeWizardState {
  // Step data
  healthConcern: HealthConcernData | null;
  demographics: DemographicsData | null;
  selectedCauses: PotentialCause[];
  selectedSymptoms: PotentialSymptom[];
  therapeuticProperties: TherapeuticProperty[];
  suggestedOils: PropertyOilSuggestions[];
  finalRecipes: FinalRecipesState;

  // API response data
  potentialCauses: PotentialCause[];
  potentialSymptoms: PotentialSymptom[];

  // Navigation state
  currentStep: RecipeStep;
  completedSteps: RecipeStep[];

  // Loading and error states
  isLoading: boolean;
  error: string | null;

  // AI Streaming states
  isStreamingCauses: boolean;
  isStreamingSymptoms: boolean;
  isStreamingProperties: boolean;
  isStreamingOils: boolean;
  isStreamingFinalRecipes: boolean;
  streamingError: string | null;

  // Oil enrichment states
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;

  // Auto-analysis state
  shouldAutoAnalyzeProperties: boolean;

  // Metadata
  lastUpdated: Date;
  sessionId: string;
}

/**
 * Actions for the recipe wizard store
 */
export interface RecipeWizardActions {
  // Step navigation
  setCurrentStep: (step: RecipeStep) => void;
  markStepCompleted: (step: RecipeStep) => void;
  canNavigateToStep: (step: RecipeStep) => boolean;
  
  // Data updates
  updateHealthConcern: (data: HealthConcernData) => void;
  updateDemographics: (data: DemographicsData) => void;
  updateSelectedCauses: (causes: PotentialCause[]) => void;
  updateSelectedSymptoms: (symptoms: PotentialSymptom[]) => void;
  updateTherapeuticProperties: (properties: TherapeuticProperty[], source: string) => void;
  updateSuggestedOils: (oils: PropertyOilSuggestions[]) => void;
  updateFinalRecipes: (timeSlot: RecipeTimeSlot, recipe: FinalRecipeProtocol) => void;
  
  // API data updates
  setPotentialCauses: (causes: PotentialCause[]) => void;
  setPotentialSymptoms: (symptoms: PotentialSymptom[]) => void;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetWizard: () => void;
  clearError: () => void;

  // AI Streaming state management
  setStreamingCauses: (isStreaming: boolean) => void;
  setStreamingSymptoms: (isStreaming: boolean) => void;
  setStreamingProperties: (isStreaming: boolean) => void;
  setStreamingOils: (isStreaming: boolean) => void;
  setStreamingFinalRecipes: (isStreaming: boolean) => void;
  setStreamingError: (error: string | null) => void;
  clearStreamingError: () => void;

  // State clearing for navigation consistency
  clearStepsAfter: (currentStep: RecipeStep) => void;
  clearStepData: (step: RecipeStep) => void;
  
  // Oil enrichment state management
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => void;
  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => void;
  
  // Auto-analysis state management
  setShouldAutoAnalyzeProperties: (should: boolean) => void;

  // Final recipes state management
  setFinalRecipeStatus: (timeSlot: RecipeTimeSlot, status: FinalRecipeStatus) => void;
  setFinalRecipesGenerating: (isGenerating: boolean) => void;
  setFinalRecipesGlobalError: (error: string | null) => void;
  resetFinalRecipes: () => void;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Type for API error responses
 */
export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

/**
 * Type for form validation errors
 */
export interface ValidationError {
  field: string;
  message: string;
}

/**
 * Type for local storage persistence
 */
export interface PersistedRecipeData {
  state: RecipeWizardState;
  timestamp: number;
  version: string;
}
