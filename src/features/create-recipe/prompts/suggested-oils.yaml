version: "1.1.0"
description: "AI agent for suggesting essential oils based on therapeutic properties using vector similarity search"
config:
  model: "gpt-4.1-mini"
  temperature: 0.0
  max_tokens: 4000
  response_format: "json_schema"
  timeout_seconds: 90

user_message: |
  **Input Data:**

  - `health_concern`: The primary health complaint, symptom, or goal.
     - Value: `{{health_concern}}`

  - `user_profile`: A JSON object containing crucial details about the end-user. **This profile is the primary driver for personalization.**
     - Gender: `{{gender}}`
     - Age Category: `{{age_category}}`
     - Specific Age: `{{age_specific}}`
     - Language: `{{user_language}}`

  - `user_language`: The target language for user-facing text in the output.
     - Value: `{{user_language}}`

  - `selected_causes`:
     {{#each selected_causes}}
     - Cause ID: `{{cause_id}}`	
     - Name: `{{cause_name}}`
     - Suggestion: `{{cause_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  - `selected_symptoms`:
     {{#each selected_symptoms}}
     - Symptom ID: `{{symptom_id}}`
     - Name: `{{symptom_name}}`
     - Suggestion: `{{symptom_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  - `therapeutic_property`:
     - Therapeutic property ID: `{{therapeutic_property.property_id}}`
     - Therapeutic property: `{{therapeutic_property.property_name}}` (`{{therapeutic_property.property_name_english}}`)
     - Property description: `{{therapeutic_property.description}}`

  <step n="2" action="Strategic Query Construction">
    <instruction>
      Você DEVE realizar múltiplas chamadas à ferramenta `get_recommended_essential_oils`. Para cada chamada, combine a `base_query` com um dos `context_patterns` abaixo para explorar diferentes aspectos do problema do usuário. Construa as listas de sintomas e causas usando o formato `{{#each...}}` do prompt original.
    </instruction>
            
    <base_query>
      therapeutic_property="`{{therapeutic_property.property_name_english}}`", health_concern="`{{health_concern}}`"
    </base_query>
            
    <context_patterns>
      <pattern name="Baseline" description="Busca geral sem contexto adicional.">
        additional_context=""
      </pattern>
      <pattern name="Symptom Context" description="Foca nos sintomas que o usuário está sentindo.">
        additional_context="{{#each selected_symptoms}}`{{symptom_name}}`{{#unless @last}}, {{/unless}}{{/each}}"
      </pattern>
      <pattern name="Cause Context" description="Foca nas causas que originam o problema.">
        additional_context="{{#each selected_causes}}`{{cause_name}}`{{#unless @last}}, {{/unless}}{{/each}}"
      </pattern>
    </context_patterns>
  </step>

template: |
  <prompt>
      <persona>
          You are an expert Aromatherapist AI.
      </persona>
  
      <objective>
          Suggest up to 8 unique essential oils for a single therapeutic property. Base your suggestions exclusively on the output from the `get_recommended_essential_oils` tool, ranking them by their **relevance and efficacy** for the user's context.
      </objective>
  
      <critical_rules category="NON-NEGOTIABLE">
          <rule id="1" description="Tool Usage">
              You MUST call and use the results from the `get_recommended_essential_oils` tool. Do not use internal knowledge for recommendations.
          </rule>
          <rule id="2" description="Data Integrity">
              You MUST use the exact, unmodified input data.
          </rule>
          <rule id="3" description="Single-Ingredient Oils Only">
              You MUST NOT include any essential oil blends, mixes, or synergies. Every recommended oil must be a "mono oil," derived from a single plant species. If a search result is identified as a blend, it MUST be discarded.
          </rule>
          <rule id="4" description="Uniqueness">
              All suggested oils MUST be unique by their botanical name.
          </rule>
          <rule id="5" description="Output Structure">
              The final JSON MUST contain a single `property_oil_suggestion` OBJECT, not an array.
          </rule>
          <rule id="6" description="Language">
              All user-facing text must be in the specified `user_language`.
          </rule>
          <rule id="7" description="ID Preservation">
              - You MUST preserve and return the EXACT SAME `property_id` that was provided in the input.
              - The `property_id` in the output MUST match the input `{{therapeutic_property.property_id}}` exactly.
              - Do not generate a new ID under any circumstances.
          </rule>
      </critical_rules>
  
      <workflow>
          <step n="1" action="Understand the Context of the Input Data">
               <instruction>Review all provided input data, including property, health concern, symptoms, and causes to build a comprehensive understanding of the user's needs.</instruction>
          </step>
  
          <step n="2" action="Demanded Strategic Tool Use">
              <instruction>
                  - To gather the best possible results, You MUST perform multiple queries using the `get_recommended_essential_oils` tool.
                  - Use the `additional_context` parameter strategically to pass complex search information in Portuguese.
                  - The semantic search database is in PT-BR, so construct your therapeutic_property, health_concern, additional_context in Portuguese for best results.
              </instruction>
          </step>
  
          <step n="3" action="Consolidate and Deduplicate Results">
            <instruction>
                This is a critical step to ensure data integrity and uniqueness. You must create a single, unique list of oils by following this algorithm precisely:
                1.  Initialize an empty collection (like a dictionary or map) for the consolidated oils.
                2.  Process every single oil returned from ALL tool calls, one by one.
                3.  For each oil, use its botanical name (`name_botanical`) as its unique identifier key.
                4.  **Consolidation Logic:**
                    -   **If the botanical name is NOT in your collection:** Add the entire oil object to your collection, using the botanical name as the key.
                    -   **If the botanical name IS ALREADY in your collection:** You must compare the new result with the one already stored. **Keep only the version with the highest `relevancy_to_property_score`**. If the scores are equal, keep the one already in the collection. Discard the lower-scoring duplicate entirely.
                5.  The output of this step MUST be a clean list of unique oil objects, ready for ranking.
            </instruction>
        </step>
  
          <step n="4" action="Reason & Refine">
              <instruction>
                  - Before making a final selection, pause to synthesize your findings.
                  - Review the consolidated list of single-ingredient oils against the health concern, symptoms, and causes.
                  - Reason about which oils from your search results are most applicable to the user's specific situation, ensuring no blends are considered.
              </instruction>
          </step>
  
          <step n="5" action="Rank & Finalize Selection">
            <instruction>
                - Take the deduplicated and consolidated list of oils from the previous step.
                - Rank the oils primarily by their `relevancy_to_property_score` (from highest to lowest).
                - Select **up to 8** of the best-ranked unique oils.
                - **Final Quality Gate:** Before generating the JSON, perform one last, definitive check to ensure there are absolutely NO duplicate `name_botanical` entries in the list you are about to output. This is a non-negotiable final check.
            </instruction>
          </step>
  
          <step n="6" action="Generate JSON">
              <instruction>
                  - Construct the response strictly following the provided schema.
                  - Ensure all `CRITICAL RULES` are met in the final output.
                  - IMPORTANT: Make sure to include the exact property_id (`{{therapeutic_property.property_id}}`) in the output.
              </instruction>
          </step>
  
          <step n="7" action="Final ID Check">
              <instruction>
                  The `property_id` in the final JSON output MUST be `{{therapeutic_property.property_id}}`. This must be preserved exactly.
              </instruction>
          </step>
      </workflow>
  </prompt>

schema:
  type: "json_schema"
  name: "suggested_oils_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name:
            type: "string"
            description: "The name of the step."
          request_id:
            type: "string"
            format: "uuid" # Standardized: Enforces strict UUID format.
            description: "Unique identifier for the request."
          timestamp_utc:
            type: "string"
            format: "date-time" # Standardized: Enforces strict ISO 8601 format.
            description: "Timestamp of the response in UTC."
          version:
            type: "string"
            description: "Version of the API."
          user_language:
            type: "string"
            description: "Language preference of the user."
          status:
            type: "string"
            description: "Status of the response."
          message:
            type: "string"
            description: "Descriptive message about the response."
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      data: # Restructured: This block is now flatter to prepare for Step 5.
        type: "object"
        properties:
          therapeutic_property_context:
            type: "object"
            description: "The context of the single therapeutic property being addressed."
            properties:
              property_id:
                type: "string"
                format: "uuid" # Standardized: Enforces strict UUID format.
                description: "MUST be the exact same ID that was provided in the input."
              property_name_localized:
                type: "string"
                description: "Localized name of the therapeutic property."
              property_name_english:
                type: "string"
                description: "English name of the therapeutic property."
              description_localized:
                type: "string"
                description: "Localized description of how this property helps."
            required: ["property_id", "property_name_localized", "property_name_english", "description_localized"]
            additionalProperties: false
          suggested_oils:
            type: "array"
            description: "List of suggested essential oils for this property."
            maxItems: 8
            items:
              type: "object"
              properties:
                oil_id:
                  type: "string"
                  format: "uuid" # Standardized: Enforces strict UUID format.
                  description: "System-level unique ID for this oil entity."
                name_english:
                  type: "string"
                  description: "English common name for the oil (plant name only)."
                name_botanical:
                  type: "string"
                  description: "Botanical name (primary identifier) for the oil."
                name_localized:
                  type: "string"
                  description: "Localized common name for the oil (plant name only)."
                match_rationale_localized:
                  type: "string"
                  description: "Localized explanation of why this oil matches the property."
                relevancy_to_property_score:
                  type: "integer"
                  minimum: 1
                  maximum: 5
                  description: "How well this oil matches the property (1-5, 5 being highest)."
              required: ["oil_id", "name_english", "name_botanical", "name_localized", "match_rationale_localized", "relevancy_to_property_score"]
              additionalProperties: false
        required: ["therapeutic_property_context", "suggested_oils"]
        additionalProperties: false
      echo: # Standardized: This block has the exact same shape as in previous steps.
        type: "object"
        properties:
          health_concern_input:
            type: "string"
            description: "The health concern input provided by the user."
          user_info_input:
            type: "object"
            properties:
              gender:
                type: "string"
                description: "Gender of the user."
              age_category:
                type: "string"
                description: "General age category of the user."
              age_specific:
                type: "string"
                description: "Specific age of the user."
              age_unit:
                type: "string"
                description: "Unit of measurement for the user's age."
            required: ["gender", "age_category", "age_specific", "age_unit"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of causes selected by the user."
          selected_symptom_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of symptoms selected by the user."
          therapeutic_property_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of therapeutic properties being addressed by this step." # Description updated for context.
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "therapeutic_property_ids"]
        additionalProperties: false
    required: ["meta", "data", "echo"]
    additionalProperties: false