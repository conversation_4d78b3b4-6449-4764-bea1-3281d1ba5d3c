version: "2.0.1"
description: "AI agent for generating personalized, empathetic, and holistic essential oil recipes."
config:
  model: "gpt-4.1-nano"
  temperature: 0.4
  max_tokens: 4000
  response_format: "json_schema"
  timeout_seconds: 90

user_message: |
  **Input Data:**

  - `health_concern`: The primary health complaint, symptom, or goal.
     - Value: `{{health_concern}}`

  - `user_profile`: A JSON object containing crucial details about the end-user. **This profile is the primary driver for personalization.**
     - Gender: `{{gender}}`
     - Age Category: `{{age_category}}`
     - Specific Age: `{{age_specific}}`
     - Language: `{{user_language}}`

  - `user_language`: The target language for user-facing text in the output.
     - Value: `{{user_language}}`

  - `selected_causes`:
     {{#each selected_causes}}
     - Cause ID: `{{cause_id}}`	
     - Name: `{{cause_name}}`
     - Suggestion: `{{cause_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  - `selected_symptoms`:
     {{#each selected_symptoms}}
     - Symptom ID: `{{symptom_id}}`
     - Name: `{{symptom_name}}`
     - Suggestion: `{{symptom_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  - `suggested_oils`:
     {{#each suggested_oils}}
     - Oil ID: {{oil_id}}
     - English Name: {{name_english}}
     - Localized Name: {{name_localized}}
     - Scientific Name: {{name_scientific}} {{name_botanical}}
     - Final Relevance Score: {{final_relevance_score}}
     - Safety Data: {{safety}}
     - Therapeutic Property Contexts:
       {{{properties_formatted}}}
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

  - `safety_library`: Complete safety reference library for all oils. Use these safety objects when analyzing safety data referenced by IDs in the oil safety data above.
     
     {{{safety_library_formatted}}}

  - `time_of_day`: {{time_of_day}}

template: |
  # Empathetic Aromatherapy Guide Agent

  You are an experienced and caring holistic wellness guide specializing in aromatherapy. Your voice is supportive, educational, and empowering. You are creating ONE personalized recipe for the specified time of day. Your goal is not just to provide a recipe, but to create a positive wellness experience.

  ## Core Directives (NON-NEGOTIABLE)
  1.  **Deep Personalization**: Your language MUST directly reflect the user's context. In `description_localized` and `oil_rationales[].rationale_localized`, explicitly reference the user's `health_concern` and `selected_symptoms`. For example: "This blend is designed to help with your '{{health_concern}}' by addressing symptoms like '{{selected_symptoms.[0].symptom_name}}'."
  2.  **Safety First**: Adhere strictly to the safety filtering rules. Your tone should be reassuringly cautious. Place all safety warnings in the top-level `safety_warnings` array.
  3.  **Holistic Focus**: Frame the recipe as part of a wellness ritual. The `ritual_suggestion_localized` should be a simple, mindful practice.
  4.  **Clarity and Simplicity**: All instructions must be crystal clear, simple, and easy for a beginner to follow. Avoid jargon.
  5.  **Positive & Empowering Tone**: Use encouraging and positive language. Make the user feel capable and cared for.
  6.  **No Medical Claims**: You are a wellness guide, not a doctor. Frame benefits in terms of well-being (e.g., "promotes a sense of calm," "helps ease tension") and include clear safety warnings.

  ## Recipe Generation Process
  1.  **Filter Safe Oils**: Based on user's age and profile. Use the safety_library to understand the full safety details for each oil by looking up the IDs referenced in each oil's safety data.
  2.  **Select 3-5 Oils**: Choose the most suitable oils for the `time_of_day` that synergize to address the user's `health_concern` and `selected_symptoms`. Place these in `data.recipe_protocol.ingredients.essential_oils`.
  3.  **Create a Theme**: Give the recipe an inspiring `recipe_theme_localized` (e.g., "Morning Clarity," "Peaceful Slumber").
  4.  **Write Personalized Descriptions**: Craft the `description_localized`, `holistic_benefit_localized`, and `oil_rationales[].rationale_localized` to directly connect with the user's inputs.

  5.  **Calculate Dilution**: Use conservative, age-appropriate ratios (0.5-1% for children, 1-2% for adults). Place calculation details in `data.recipe_protocol.formulation` including `total_drops`, `dilution_percentage`, `bottle_size_ml`, and `bottle_type_localized`.
  6.  **Write Clear Instructions**: Create simple, numbered steps for `preparation_steps_localized` and detailed object-based instructions for `usage_instructions_localized` with `method_code`, `method`, `description`, and `frequency` fields.
  7.  **Create Summaries**: Write concise `preparation_summary_localized` and `usage_summary_localized` suitable for UI flip cards - these should be brief, user-friendly overviews.
  8.  **Suggest Carrier Oils**: Provide both `recommended` and `alternative` carrier oils in `data.recipe_protocol.ingredients.carrier_oil` with localized names and properties.
  9.  **Document Therapeutic Properties**: List targeted therapeutic properties in `therapeutic_properties_targeted` with property IDs, localized names, and descriptions.
  10. **Synergy Rationale**: Write a comprehensive, localized explanation in `synergy_rationale_localized` that covers:
      - A scientific explanation of each oil's properties and mechanisms
      - How the selected oils work together synergistically to address the user's health concern
      - How the carrier oil contributes to the overall effectiveness
      - All content must be in the user's language
      - The rationale should be suitable for a short user-facing display
  11. **Suggest a Ritual**: Provide a simple, mindful `ritual_suggestion_localized`.
  12. **Add Safety Warnings**: Include specific, relevant warnings in the top-level `safety_warnings` array based on the safety_library data. Address phototoxicity, pregnancy/nursing concerns, and age-specific guidance as applicable.

  ## Key Schema Structure Requirements
  - **Essential Oils**: Must be in `data.recipe_protocol.ingredients.essential_oils[]` with `oil_id`, `name_localized`, `scientific_name`, and `drops`
  - **Carrier Oils**: Must be in `data.recipe_protocol.ingredients.carrier_oil.recommended` and `ingredients.carrier_oil.alternative` with `name_localized` and `properties_localized`
  - **Formulation**: All dosage info goes in `data.recipe_protocol.formulation` (total_drops, dilution_percentage, bottle_size_ml, bottle_type_localized)
  - **Usage Instructions**: Must be object array with `method_code`, `method`, `description`, `frequency` fields (NOT simple strings)
  - **Oil Rationales**: Separate array `data.recipe_protocol.oil_rationales[]` with detailed explanations for each oil selection
  - **Safety Warnings**: Top-level array `safety_warnings[]` with `type`, `title_localized`, `warning_text_localized`
  - **Summary Fields**: Include both `preparation_summary_localized` and `usage_summary_localized` for quick reference
  - **Time Information**: Include both `time_slot` (enum) and `time_of_day_localized`, `time_range_localized` for user-friendly display

schema:
  type: "json_schema"
  name: "final_recipe_response_v2"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name: { type: "string" }
          request_id: { type: "string", format: "uuid" }
          timestamp_utc: { type: "string", format: "date-time" }
          version: { type: "string" }
          user_language: { type: "string" }
          status: { type: "string" }
          message: { type: "string" }
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      
      data:
        type: "object"
        properties:
          recipe_protocol:
            type: "object"
            properties:
              recipe_id: { type: "string", format: "uuid" }
              time_slot: { type: "string", enum: ["morning", "mid-day", "night"] }
              
              time_of_day_localized: { type: "string" }
              time_range_localized: { type: "string" }
              recipe_theme_localized: { type: "string" }
              description_localized: { type: "string" }
              holistic_benefit_localized: { type: "string" }
              application_type_localized: { type: "string" }
              synergy_rationale_localized: { type: "string" }
              
              formulation:
                type: "object"
                properties:
                  total_drops: { type: "integer" }
                  dilution_percentage: { type: "number" }
                  bottle_size_ml: { type: "integer" }
                  bottle_type_localized: { type: "string" }
                required: ["total_drops", "dilution_percentage", "bottle_size_ml", "bottle_type_localized"]
                additionalProperties: false

              ingredients:
                type: "object"
                properties:
                  essential_oils:
                    type: "array"
                    items:
                      type: "object"
                      properties:
                        oil_id: { type: "string", format: "uuid" }
                        name_localized: { type: "string" }
                        scientific_name: { type: "string" }
                        drops: { type: "integer" }
                      required: ["oil_id", "name_localized", "scientific_name", "drops"]
                      additionalProperties: false
                  carrier_oil:
                    type: "object"
                    properties:
                      recommended:
                        type: "object"
                        properties:
                          name_localized: { type: "string" }
                          properties_localized: { type: "string" }
                        required: ["name_localized", "properties_localized"]
                        additionalProperties: false
                      alternative:
                        type: "object"
                        properties:
                          name_localized: { type: "string" }
                          properties_localized: { type: "string" }
                        required: ["name_localized", "properties_localized"]
                        additionalProperties: false
                    required: ["recommended", "alternative"]
                    additionalProperties: false
                required: ["essential_oils", "carrier_oil"]
                additionalProperties: false

              therapeutic_properties_targeted:
                type: "array"
                items:
                  type: "object"
                  properties:
                    property_id: { type: "string", format: "uuid" }
                    name_localized: { type: "string" }
                    description_localized: { type: "string" }
                  required: ["property_id", "name_localized", "description_localized"]
                  additionalProperties: false

              preparation_summary_localized: { type: "string" }
              usage_summary_localized: { type: "string" }

              preparation_steps_localized:
                type: "array"
                items: { type: "string" }
              
              usage_instructions_localized:
                type: "array"
                items:
                  type: "object"
                  properties:
                    method_code: { type: "string" }
                    method: { type: "string" }
                    description: { type: "string" }
                    frequency: { type: "string" }
                  required: ["method_code", "method", "description", "frequency"]
                  additionalProperties: false

              ritual_suggestion_localized: { type: "string" }

              oil_rationales:
                type: "array"
                items:
                  type: "object"
                  properties:
                    oil_id: { type: "string", format: "uuid" }
                    name_localized: { type: "string" }
                    properties: { type: "array", items: { type: "string" } }
                    rationale_localized: { type: "string" }
                  required: ["oil_id", "name_localized", "properties", "rationale_localized"]
                  additionalProperties: false
            
            required:
              - "recipe_id"
              - "time_slot"
              - "time_of_day_localized"
              - "time_range_localized"
              - "recipe_theme_localized"
              - "description_localized"
              - "holistic_benefit_localized"
              - "application_type_localized"
              - "synergy_rationale_localized"
              - "formulation"
              - "ingredients"
              - "therapeutic_properties_targeted"
              - "preparation_summary_localized"
              - "usage_summary_localized"
              - "preparation_steps_localized"
              - "usage_instructions_localized"
              - "ritual_suggestion_localized"
              - "oil_rationales"
            additionalProperties: false
        required: ["recipe_protocol"]
        additionalProperties: false

      safety_warnings:
        type: "array"
        items:
          type: "object"
          properties:
            type: { type: "string" }
            title_localized: { type: "string" }
            warning_text_localized: { type: "string" }
          required: ["type", "title_localized", "warning_text_localized"]
          additionalProperties: false

      echo:
        type: "object"
        properties:
          health_concern_input:
            type: "string"
            description: "The health concern input provided by the user."
          user_info_input:
            type: "object"
            properties:
              gender:
                type: "string"
                description: "Gender of the user."
              age_category:
                type: "string"
                description: "General age category of the user."
              age_specific:
                type: "string"
                description: "Specific age of the user."
              age_unit:
                type: "string"
                description: "Unit of measurement for the user's age."
            required: ["gender", "age_category", "age_specific", "age_unit"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items:
              type: "string"
              format: "uuid"
            description: "IDs of causes selected by the user."
          selected_symptom_ids:
            type: "array"
            items:
              type: "string"
              format: "uuid"
            description: "IDs of symptoms selected by the user."
          suggested_oil_ids:
            type: "array"
            items:
              type: "string"
              format: "uuid"
            description: "IDs of suggested oils for the recipe."
          time_of_day:
            type: "string"
            description: "Time of day for the recipe application."
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "suggested_oil_ids", "time_of_day"]
        additionalProperties: false
          
    required: ["meta", "data", "safety_warnings", "echo"]
    additionalProperties: false