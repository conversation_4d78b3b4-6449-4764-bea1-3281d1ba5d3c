version: "1.1.0"
description: "AI agent for enriching essential oils with safety information using vector similarity search"
config:
  model: "gpt-4.1-nano"
  temperature: 0.0
  max_tokens: 4000
  response_format: "json_schema"
  timeout_seconds: 90

user_message: |
  ## Input Data
  1. `therapeutic_property`:
     - Therapeutic property ID: `{{therapeutic_property.property_id}}`
     - Therapeutic property: `{{therapeutic_property.property_name}}` (`{{therapeutic_property.property_name_english}}`)
     - Property description: `{{therapeutic_property.description}}`

  6. `suggested_oils`:
     {{#each suggested_oils}}
     - Oil ID: `{{oil_id}}`
     - English Name: `{{name_english}}`
     - Botanical Name: `{{name_botanical}}`
     - Localized Name: `{{name_localized}}`
     - Rationale: `{{match_rationale_localized}}`
     - Relevancy Score: `{{relevancy_to_property_score}}`
     {{#unless @last}}
     ---     
     {{/unless}}
     {{/each}}
     
template: |
  # Oil Enrichment Agent

  You are an agent that enriches essential oils with additional data from a Supabase vector search.
  The data includes human-readable safety information from the essential_oils_with_safety view.

  ## Core Directives (NON-NEGOTIABLE)
  1.  **Preserve Original Data & ID**: You MUST preserve the original `oil_id`, names, rationale, and score for every oil. Do NOT generate or alter `oil_id`s. If the database returns a different ID (`supabase_id`), add it as metadata but keep the original `oil_id` as the primary identifier.
  2.  **Mandatory Tool Use**: You MUST call the `enrich_oil_with_vector_search` tool for EACH oil in the input list to fetch its database information.
  3.  **Complete Processing**: Process ALL oils from the input list. No oil should be omitted. If an oil cannot be enriched (no match found), it must still be included with its original data, marked appropriately (e.g., `isEnriched: false`).
  4.  **Maintain Order**: The final list of oils in your response MUST be in the exact same order as the input list.

  ## Workflow
  1.  **Receive Input**: Parse the incoming list of suggested oils and their therapeutic property context.
  2.  **Iterate and Enrich**: For each oil in the list, call the `enrich_oil_with_vector_search` tool.
  3.  **Merge Data**:
      -   **On Success**: Merge the safety information from the tool's response with the original oil data.
      -   **On Failure (No Match)**: Keep all the original oil data as-is.
  4.  **Aggregate Results**: Collect all processed oils (both enriched and not) into a single list.
  5.  **Format Output**: Return the complete list, ensuring it adheres to the specified schema and all core directives.

  ## Expected Workflow
  1. Parse the input oils list
  2. For each oil, call enrich_oil_with_vector_search(name_english, name_botanical)
  3. Merge the enrichment data with original oil information
  4. If database match is found: use safety data and real botanical names but keep original oil_id
  5. If no database match: mark as not enriched but preserve original data
  6. Return all oils in the exact format specified by the schema

  ## Output Format
  -   Follow the required JSON schema provided by the tool definition.
  -   Ensure EVERY oil from the input is present in the output, in the original order.
  -   Each oil object MUST contain the `isEnriched` flag: `true` if database information was successfully merged, `false` otherwise.
  -   The primary `oil_id` for each object MUST match the `oil_id` from the input.

  ## Notes
  - Ensure no oil data is lost or misordered during processing.
  - Pay particular attention to integrating the vector search data without altering the original identifiers.

schema:
  type: "json_schema"
  name: "oil_enrichment_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name:
            type: "string"
            description: "The name of the step."
          request_id:
            type: "string"
            format: "uuid"
            description: "Unique identifier for the request."
          timestamp_utc:
            type: "string"
            format: "date-time"
            description: "Timestamp of the response in UTC."
          version:
            type: "string"
            description: "Version of the API."
          user_language:
            type: "string"
            description: "Language preference of the user."
          status:
            type: "string"
            description: "Status of the response."
          message:
            type: "string"
            description: "Descriptive message about the response."
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      data:
        type: "object"
        description: "Contains the enriched oil data, grouped by the therapeutic property."
        properties:
          therapeutic_property_context:
            type: "object"
            description: "The context of the single therapeutic property for which oils were enriched."
            properties:
              property_id:
                type: "string"
                format: "uuid"
                description: "The unique ID of the therapeutic property. MUST be the same ID from the input."
              property_name_localized:
                type: "string"
                description: "Localized name of the therapeutic property."
              property_name_english:
                type: "string"
                description: "English name of the therapeutic property."
              description_localized:
                type: "string"
                description: "Localized contextual description of the property."
            required: ["property_id", "property_name_localized", "property_name_english", "description_localized"]
            additionalProperties: false
          enriched_oils:
            type: "array"
            description: "The list of suggested oils, now enriched with detailed safety information."
            items:
              type: "object"
              properties:
                oil_id:
                  type: "string"
                  format: "uuid"
                  description: "MUST be the exact same ID from the input - do NOT change this."
                name_english:
                  type: "string"
                  description: "English common name for the oil."
                name_botanical:
                  type: "string"
                  description: "Botanical name for the oil."
                name_localized:
                  type: "string"
                  description: "Localized common name for the oil."
                match_rationale_localized:
                  type: "string"
                  description: "Localized explanation of why this oil was chosen."
                relevancy_to_property_score:
                  type: "integer"
                  minimum: 1
                  maximum: 5
                  description: "Score indicating how well the oil matches the property."
                supabase_id:
                  type: "string"
                  description: "The database ID if found during enrichment."
                isEnriched:
                  type: "boolean"
                  description: "True if successfully enriched with database data, false otherwise."
                safety:
                  type: "object"
                  description: "Detailed safety and usage guidelines for the oil."
                  properties:
                    internal_use:
                      anyOf:
                        - type: "object"
                          properties:
                            name: { type: "string" }
                            code: { type: "string" }
                            description: { type: "string" }
                            guidance: { type: "string" }
                          required: ["name", "code", "description", "guidance"]
                          additionalProperties: false
                        - type: "null"
                    dilution:
                      type: "object"
                      properties:
                        name: { anyOf: [{ type: "string" }, { type: "null" }] }
                        description: { anyOf: [{ type: "string" }, { type: "null" }] }
                        percentage_max: { anyOf: [{ type: "number" }, { type: "null" }] }
                        percentage_min: { anyOf: [{ type: "number" }, { type: "null" }] }
                        ratio: { anyOf: [{ type: "string" }, { type: "null" }] }
                      required: ["name", "description", "percentage_max", "percentage_min", "ratio"]
                      additionalProperties: false
                    phototoxicity:
                      type: "object"
                      properties:
                        status: { anyOf: [{ type: "string" }, { type: "null" }] }
                        guidance: { anyOf: [{ type: "string" }, { type: "null" }] }
                        description: { anyOf: [{ type: "string" }, { type: "null" }] }
                      required: ["status", "guidance", "description"]
                      additionalProperties: false
                    pregnancy_nursing:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          id: { anyOf: [{ type: "string" }, { type: "null" }] }
                          name: { anyOf: [{ type: "string" }, { type: "null" }] }
                          status_description: { anyOf: [{ type: "string" }, { type: "null" }] }
                          code: { anyOf: [{ type: "string" }, { type: "null" }] }
                          usage_guidance: { anyOf: [{ type: "string" }, { type: "null" }] }
                          description: { anyOf: [{ type: "string" }, { type: "null" }] }
                        required: ["id", "name", "status_description", "code", "usage_guidance", "description"]
                        additionalProperties: false
                    child_safety:
                      type: "array"
                      items:
                        type: "object"
                        properties:
                          age_range: { anyOf: [{ type: "string" }, { type: "null" }] }
                          safety_notes: { anyOf: [{ type: "string" }, { type: "null" }] }
                        required: ["age_range", "safety_notes"]
                        additionalProperties: false
                  required: ["internal_use", "dilution", "phototoxicity", "pregnancy_nursing", "child_safety"]
                  additionalProperties: false
              required: ["oil_id", "name_english", "name_botanical", "name_localized", "match_rationale_localized", "relevancy_to_property_score", "supabase_id", "isEnriched", "safety"]
              additionalProperties: false
        required: ["therapeutic_property_context", "enriched_oils"]
        additionalProperties: false
      echo:
        type: "object"
        properties:
          health_concern_input:
            type: "string"
            description: "The health concern input provided by the user."
          user_info_input:
            type: "object"
            properties:
              gender: { type: "string" }
              age_category: { type: "string" }
              age_specific: { type: "string" }
              age_unit: { type: "string" }
            required: ["gender", "age_category", "age_specific", "age_unit"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items: { type: "string" }
            description: "IDs of causes selected by the user."
          selected_symptom_ids:
            type: "array"
            items: { type: "string" }
            description: "IDs of symptoms selected by the user."
          therapeutic_property_ids:
            type: "array"
            items: { type: "string" }
            description: "IDs of therapeutic properties being addressed by this step."
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "therapeutic_property_ids"]
        additionalProperties: false
    required: ["meta", "data", "echo"]
    additionalProperties: false
