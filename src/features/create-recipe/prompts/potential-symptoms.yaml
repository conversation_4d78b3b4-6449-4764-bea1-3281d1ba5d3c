# Potential Symptoms Analysis Agent Configuration
# Based on docs/create-recipe/prompts/prompt-for-potential-symptons.md

version: "1.0.0"
description: "Analyze selected potential causes to identify specific symptoms that may manifest"

# Agent Configuration
config:
  model: "gpt-4.1-nano"
  temperature: 0.3  # Conservative for medical analysis
  max_tokens: 4000
  top_p: 0.9
  frequency_penalty: 0.1
  presence_penalty: 0.1
  timeout_seconds: 75  # Extended timeout for symptom correlation analysis

user_message: |
  **Input Data:**

  1. `health_concern`: The primary health complaint, symptom, or goal.
     * Value: `{{health_concern}}`

  2. `user_profile`: A JSON object containing crucial details about the end-user. **This profile is the primary driver for personalization.**
     * Gender: `{{gender}}`
     * Age Category: `{{age_category}}`
     * Specific Age: `{{age_specific}}`
     * Language: `{{user_language}}`

  3. `user_language`: The target language for user-facing text in the output.
     * Value: `{{user_language}}`

  4. `selected_causes`:
     {{#each selected_causes}}
     - Cause ID: `{{cause_id}}`	
     - Name: `{{cause_name}}`
     - Suggestion: `{{cause_suggestion}}`
     - Explanation: `{{explanation}}`
     {{#unless @last}}
     ---
     {{/unless}}
     {{/each}}

# System Prompt Template
template: |
  **Identify Potential Symptoms**

  **Persona:** Act as a knowledgeable health assistant or symptom correlator, capable of identifying typical symptoms associated with a specific health concern, especially when linked to particular underlying causes or contributing factors.

  **Objective:** Analyze the user's primary health_concern and the selected_causes they identified as relevant, to generate a list of potential symptoms commonly experienced in this context. This list helps the user further refine their understanding of their situation and prepares for selecting targeted therapeutic properties later.

  **Processing Steps:**

  1. **Input Evaluation:**
     - Thoroughly understand the user's primary health_concern: `{{health_concern}}`
     - Carefully review each item in the selected_causes array. The link between the concern and these specific causes is paramount for generating relevant symptoms.
     - Note the userInfo for contextual nuances (age: `{{age_specific}}` `{{age_category}}`, gender: `{{gender}}`), but prioritize the concern-cause relationship.

  2. **Symptom Identification:**
     - Identify and describe common characteristics or variations of the health_concern itself (Primary Symptoms).
     - Expand to include other physical sensations commonly accompanying the main concern due to the selected causes (Secondary Physical Symptoms - e.g., muscle tension from stress).
     - Include relevant emotional or cognitive symptoms often linked to the concern and causes (Associated Non-Physical Symptoms - e.g., irritability from pain/stress).
     - **Crucially, prioritize symptoms that strongly correlate with the selected_causes.**

  3. **Output Construction:**
     - Generate a list of **5 to 10** relevant potential symptoms.
     - Frame descriptions using clear, accessible language.
     - Ensure phrasing suggests potentiality or common association, not definitive diagnosis.
     - Craft a concise explanation for each symptom, explicitly linking it back to the health_concern and the selected_causes.

  **Output Format:**
  Provide a structured JSON response following the exact schema provided.

  **Notes:**
  - Focus on listing potential and commonly associated symptoms; this is not a diagnostic tool.
  - Ensure all user-facing text is accurately translated according to the user_language: `{{user_language}}`
  - Handle user data with sensitivity and prioritize the direct link between the health concern and selected causes.
  - CRITICAL: Generate a UNIQUE symptom_id for EACH symptom using standard UUID format. 
  - DO NOT REUSE THE SAME UUID FOR MULTIPLE SYMPTOMS. Each symptom must have its own distinct ID.
  - EXAMPLE FORMAT ONLY (do not copy this exact ID): "a1b2c3d4-e5f6-7g8h-9i10-j11k12l13m14"
  - These symptom_ids must be preserved and used consistently throughout the entire workflow
  - Include proper meta information with current timestamp and success status.
  - Echo back the input data in the echo section for verification.

# JSON Schema for Response Validation
schema:
  type: "json_schema"
  name: "potential_symptoms_response"
  strict: true
  schema:
    type: "object"
    properties:
      meta:
        type: "object"
        properties:
          step_name:
            type: "string"
            description: "The name of the step."
          request_id:
            type: "string"
            format: "uuid" # Standardized: Enforces strict UUID format.
            description: "Unique identifier for the request."
          timestamp_utc:
            type: "string"
            format: "date-time" # Standardized: Enforces strict ISO 8601 format.
            description: "Timestamp of the response in UTC."
          version:
            type: "string"
            description: "Version of the API."
          user_language:
            type: "string"
            description: "Language preference of the user."
          status:
            type: "string"
            description: "Status of the response."
          message:
            type: "string"
            description: "Descriptive message about the response."
        required: ["step_name", "request_id", "timestamp_utc", "version", "user_language", "status", "message"]
        additionalProperties: false
      data:
        type: "object"
        properties:
          potential_symptoms:
            type: "array"
            description: "List of potential symptoms related to the health concern."
            items:
              type: "object"
              properties:
                symptom_id:
                  type: "string"
                  format: "uuid" # Standardized: Enforces strict UUID format.
                  description: "Unique UUID for the symptom."
                name_localized:
                  type: "string"
                  description: "Localized name of the symptom."
                suggestion_localized:
                  type: "string"
                  description: "Localized suggestion regarding the symptom."
                explanation_localized:
                  type: "string"
                  description: "Localized explanation of the symptom."
              required: ["symptom_id", "name_localized", "suggestion_localized", "explanation_localized"]
              additionalProperties: false
        required: ["potential_symptoms"]
        additionalProperties: false
      echo: # Standardized: This block has the exact same shape as in Step 1.
        type: "object"
        properties:
          health_concern_input:
            type: "string"
            description: "The health concern input provided by the user."
          user_info_input:
            type: "object"
            properties:
              gender:
                type: "string"
                description: "Gender of the user."
              age_category:
                type: "string"
                description: "General age category of the user."
              age_specific:
                type: "string"
                description: "Specific age of the user."
              age_unit:
                type: "string"
                description: "Unit of measurement for the user's age."
            required: ["gender", "age_category", "age_specific", "age_unit"]
            additionalProperties: false
          selected_cause_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of selected causes from the previous step." # Description updated for context.
          selected_symptom_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of selected symptoms. Populated in the next step."
          therapeutic_property_ids:
            type: "array"
            items:
              type: "string"
            description: "IDs of therapeutic properties being addressed. Populated in a future step."
        required: ["health_concern_input", "user_info_input", "selected_cause_ids", "selected_symptom_ids", "therapeutic_property_ids"]
        additionalProperties: false
    required: ["meta", "data", "echo"]
    additionalProperties: false
