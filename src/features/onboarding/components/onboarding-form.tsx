'use client';

import { OnboardingWizard } from './wizard/onboarding-wizard';
import { OnboardingDebugOverlay } from './onboarding-debug-overlay';
import type { Locale } from '../../../../i18n-config';

interface OnboardingFormProps {
  locale: Locale;
  translations: any;
}

/**
 * Main onboarding form component - now uses the multi-step wizard
 * Maintains backward compatibility while providing enhanced functionality
 */
export function OnboardingForm({ locale, translations }: OnboardingFormProps) {
  return (
    <>
      <OnboardingWizard 
        locale={locale} 
        translations={translations} 
      />
      <OnboardingDebugOverlay />
    </>
  );
}