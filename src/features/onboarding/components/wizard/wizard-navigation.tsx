'use client';

import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface WizardNavigationProps {
  canGoBack: boolean;
  canGoNext: boolean;
  isSubmitting: boolean;
  onBack: () => void;
  onNext: () => void;
  nextLabel?: string;
  backLabel?: string;
  className?: string;
}

export function WizardNavigation({
  canGoBack,
  canGoNext,
  isSubmitting,
  onBack,
  onNext,
  nextLabel = 'Continue',
  backLabel = 'Back',
  className,
}: WizardNavigationProps) {
  return (
    <div className={cn('flex justify-between items-center pt-6', className)}>
      {/* Back button */}
      <Button
        type="button"
        variant="outline"
        onClick={onBack}
        disabled={!canGoBack || isSubmitting}
        className={cn({
          'invisible': !canGoBack,
        })}
      >
        {backLabel}
      </Button>

      {/* Next button */}
      <Button
        type="button"
        onClick={onNext}
        disabled={!canGoNext || isSubmitting}
        className="min-w-[120px]"
      >
        {isSubmitting ? (
          <div className="flex items-center">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Processing...
          </div>
        ) : (
          nextLabel
        )}
      </Button>
    </div>
  );
}