'use client';

import { useRef } from 'react';
import { useUser } from '@clerk/nextjs';
import { WizardProgress } from './wizard-progress';
import { WizardNavigation } from './wizard-navigation';
import { useOnboardingWizard } from '../../hooks/use-onboarding-wizard';
import { BasicInfoStep, type BasicInfoStepRef } from '../steps/basic-info-step';
import { SecondaryEmailStep } from '../steps/secondary-email-step';
import { CompletionStep } from '../steps/completion-step';
import type { Locale } from '../../../../../i18n-config';

interface OnboardingWizardProps {
  locale: Locale;
  translations: any;
}

export function OnboardingWizard({ locale, translations }: OnboardingWizardProps) {
  const { user, isLoaded: userLoaded } = useUser();
  const wizard = useOnboardingWizard(locale);
  const basicInfoStepRef = useRef<BasicInfoStepRef>(null);

  // Show loading state while Clerk is loading user data
  if (!userLoaded) {
    return (
      <div className="w-full max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center space-y-4">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
            <h2 className="text-xl font-semibold text-gray-900">
              {translations.onboarding?.loading || 'Loading...'}
            </h2>
            <p className="text-gray-600">
              {translations.onboarding?.loadingMessage || 'Please wait while we prepare your setup.'}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show error if no user is found
  if (!user) {
    return (
      <div className="w-full max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900">
              {translations.onboarding?.authError || 'Authentication Required'}
            </h2>
            <p className="text-gray-600">
              {translations.onboarding?.authErrorMessage || 'Please sign in to continue with onboarding.'}
            </p>
            <button 
              onClick={() => window.location.href = `/${locale}/login`}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              {translations.onboarding?.signIn || 'Sign In'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  const handleNext = () => {
    if (wizard.currentStep === 'completion') {
      wizard.completeOnboarding();
    } else if (wizard.currentStep === 'basic-info') {
      // For basic-info step, trigger form submission
      basicInfoStepRef.current?.submitForm();
    } else {
      wizard.goNext();
    }
  };

  const renderCurrentStep = () => {
    const stepProps = {
      data: wizard.data,
      onNext: wizard.goNext,
      onBack: wizard.goBack,
      isSubmitting: wizard.isSubmitting,
      error: wizard.error,
      translations,
    };

    switch (wizard.currentStep) {
      case 'basic-info':
        return <BasicInfoStep ref={basicInfoStepRef} {...stepProps} />;
      case 'secondary-email':
        return <SecondaryEmailStep {...stepProps} />;
      case 'completion':
        return <CompletionStep {...stepProps} />;
      default:
        return null;
    }
  };

  const getNextLabel = () => {
    switch (wizard.currentStep) {
      case 'basic-info':
        return translations.onboarding?.continue || 'Continue';
      case 'secondary-email':
        return translations.onboarding?.continue || 'Continue';
      case 'completion':
        return translations.onboarding?.complete || 'Complete Setup';
      default:
        return 'Continue';
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Progress indicator */}
      <WizardProgress
        currentStep={wizard.currentStep}
        completedSteps={wizard.completedSteps}
        totalSteps={wizard.totalSteps}
        className="mb-8"
      />

      {/* Current step content */}
      <div className="bg-white rounded-lg shadow-lg p-8">
        {renderCurrentStep()}

        {/* Error display */}
        {wizard.error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{wizard.error}</p>
          </div>
        )}

        {/* Navigation */}
        <WizardNavigation
          canGoBack={wizard.canGoBack}
          canGoNext={wizard.canGoNext}
          isSubmitting={wizard.isSubmitting}
          onBack={wizard.goBack}
          onNext={handleNext}
          nextLabel={getNextLabel()}
          backLabel={translations.onboarding?.back || 'Back'}
        />
      </div>
    </div>
  );
}