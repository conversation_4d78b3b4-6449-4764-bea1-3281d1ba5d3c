'use client';

import { cn } from '@/lib/utils';
import type { ReactNode } from 'react';

interface WizardStepProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  className?: string;
}

export function WizardStep({ 
  title, 
  subtitle, 
  children, 
  className 
}: WizardStepProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {/* Step header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {title}
        </h2>
        {subtitle && (
          <p className="text-gray-600">
            {subtitle}
          </p>
        )}
      </div>

      {/* Step content */}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}