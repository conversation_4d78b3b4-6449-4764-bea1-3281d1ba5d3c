'use client';

import { cn } from '@/lib/utils';
import type { OnboardingStep } from '../../types/onboarding.types';

interface WizardProgressProps {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  totalSteps: number;
  className?: string;
}

const STEP_LABELS: Record<OnboardingStep, string> = {
  'basic-info': 'Basic Info',
  'secondary-email': 'Email Setup',
  'completion': 'Complete',
};

const STEPS: OnboardingStep[] = ['basic-info', 'secondary-email', 'completion'];

export function WizardProgress({ 
  currentStep, 
  completedSteps, 
  totalSteps, 
  className 
}: WizardProgressProps) {
  const currentStepIndex = STEPS.indexOf(currentStep);

  return (
    <div className={cn('w-full', className)}>
      {/* Progress bar */}
      <div className="flex items-center justify-between mb-4">
        {STEPS.map((step, index) => {
          const isCompleted = completedSteps.includes(step);
          const isCurrent = step === currentStep;
          const isPast = index < currentStepIndex;
          
          return (
            <div key={step} className="flex items-center flex-1">
              {/* Step circle */}
              <div
                className={cn(
                  'flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium transition-colors',
                  {
                    'bg-blue-600 text-white': isCurrent,
                    'bg-green-600 text-white': isCompleted || isPast,
                    'bg-gray-200 text-gray-600': !isCurrent && !isCompleted && !isPast,
                  }
                )}
              >
                {isCompleted || isPast ? (
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                ) : (
                  index + 1
                )}
              </div>
              
              {/* Step label */}
              <span
                className={cn(
                  'ml-2 text-sm font-medium transition-colors',
                  {
                    'text-blue-600': isCurrent,
                    'text-green-600': isCompleted || isPast,
                    'text-gray-500': !isCurrent && !isCompleted && !isPast,
                  }
                )}
              >
                {STEP_LABELS[step]}
              </span>
              
              {/* Connector line */}
              {index < STEPS.length - 1 && (
                <div
                  className={cn(
                    'flex-1 h-0.5 mx-4 transition-colors',
                    {
                      'bg-green-600': isPast || isCompleted,
                      'bg-gray-200': !isPast && !isCompleted,
                    }
                  )}
                />
              )}
            </div>
          );
        })}
      </div>
      
      {/* Progress percentage */}
      <div className="text-center">
        <span className="text-sm text-gray-600">
          Step {currentStepIndex + 1} of {totalSteps}
        </span>
      </div>
    </div>
  );
}