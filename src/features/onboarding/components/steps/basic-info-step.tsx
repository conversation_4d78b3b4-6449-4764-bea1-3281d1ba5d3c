'use client';

import { useState, useImperativeHandle, forwardRef } from 'react';
import { useUser } from '@clerk/nextjs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LanguageSelector } from '@/components/ui/language-selector';
import { WizardStep } from '../wizard/wizard-step';
import { LanguageService, type SupportedLanguage } from '@/features/auth/services/language.service';
import type { WizardStepProps, BasicInfoData } from '../../types/onboarding.types';

interface BasicInfoStepProps extends WizardStepProps {
  translations: any;
}

export interface BasicInfoStepRef {
  submitForm: () => boolean;
}

export const BasicInfoStep = forwardRef<BasicInfoStepRef, BasicInfoStepProps>(({ 
  data, 
  onNext, 
  isSubmitting, 
  error: _error, // Renamed to indicate it's intentionally unused
  translations 
}, ref) => {
  const { user } = useUser();
  const t = translations.onboarding || {};

  // Local state for form data
  const [formData, setFormData] = useState<BasicInfoData>({
    firstName: data.firstName || user?.firstName || '',
    lastName: data.lastName || user?.lastName || '',
    language: (data.language as SupportedLanguage) || 
              (user ? LanguageService.getUserLanguage(user) : LanguageService.DEFAULT_LANGUAGE),
  });

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      errors['firstName'] = t.firstNameRequired || 'First name is required';
    }
    if (!formData.lastName.trim()) {
      errors['lastName'] = t.lastNameRequired || 'Last name is required';  
    }
    if (!formData.language) {
      errors['language'] = t.languageRequired || 'Language selection is required';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault();
    }
    
    if (validateForm()) {
      onNext(formData);
      return true;
    }
    return false;
  };

  // Expose form submission to parent component
  useImperativeHandle(ref, () => ({
    submitForm: () => handleSubmit()
  }), [handleSubmit]);

  const handleInputChange = (field: keyof BasicInfoData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <WizardStep
      title={t.welcomeTitle || 'Welcome! Let\'s get you started'}
      subtitle={t.welcomeSubtitle || 'Please tell us a bit about yourself'}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Language Selection */}
        <div className="space-y-2">
          <Label htmlFor="language">
            {t.language || 'Preferred Language'}
          </Label>
          <LanguageSelector
            id="language"
            user={user}
            value={formData.language}
            onValueChange={(value) => handleInputChange('language', value)}
            placeholder={t.languagePlaceholder || 'Select your preferred language'}
          />
          {fieldErrors['language'] && (
            <p className="text-red-600 text-sm">{fieldErrors['language']}</p>
          )}
        </div>

        {/* First Name */}
        <div className="space-y-2">
          <Label htmlFor="firstName">
            {t.firstName || 'First Name'}
          </Label>
          <Input
            id="firstName"
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder={t.firstNamePlaceholder || 'Enter your first name'}
            disabled={isSubmitting}
            className={fieldErrors['firstName'] ? 'border-red-500' : ''}
          />
          {fieldErrors['firstName'] && (
            <p className="text-red-600 text-sm">{fieldErrors['firstName']}</p>
          )}
        </div>

        {/* Last Name */}
        <div className="space-y-2">
          <Label htmlFor="lastName">
            {t.lastName || 'Last Name'}
          </Label>
          <Input
            id="lastName"
            type="text"
            value={formData.lastName}
            onChange={(e) => handleInputChange('lastName', e.target.value)}
            placeholder={t.lastNamePlaceholder || 'Enter your last name'}
            disabled={isSubmitting}
            className={fieldErrors['lastName'] ? 'border-red-500' : ''}
          />
          {fieldErrors['lastName'] && (
            <p className="text-red-600 text-sm">{fieldErrors['lastName']}</p>
          )}
        </div>
      </form>
    </WizardStep>
  );
});