'use client';

import { WizardStep } from '../wizard/wizard-step';
import { LanguageService } from '@/features/auth/services/language.service';
import type { WizardStepProps } from '../../types/onboarding.types';

interface CompletionStepProps extends WizardStepProps {
  translations: any;
}

export function CompletionStep({ 
  data, 
  translations 
}: CompletionStepProps) {
  const t = translations.onboarding || {};

  const languageOption = LanguageService.getLanguageOption(data.language || 'en');

  return (
    <WizardStep
      title={t.completionTitle || 'You\'re All Set!'}
      subtitle={t.completionSubtitle || 'Review your information and complete your setup'}
    >
      <div className="space-y-6">
        {/* Summary card */}
        <div className="bg-gray-50 rounded-lg p-6 space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">
            {t.summaryTitle || 'Your Information'}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Basic info */}
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">
                  {t.firstName || 'First Name'}
                </label>
                <p className="text-gray-900">{data.firstName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">
                  {t.lastName || 'Last Name'}
                </label>
                <p className="text-gray-900">{data.lastName}</p>
              </div>
            </div>

            {/* Language and email */}
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">
                  {t.language || 'Language'}
                </label>
                <p className="text-gray-900 flex items-center">
                  <span className="mr-2">{languageOption?.flag}</span>
                  {languageOption?.label}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-600">
                  {t.secondaryEmail || 'Secondary Email'}
                </label>
                <p className="text-gray-900">
                  {data.skipSecondaryEmail ? (
                    <span className="text-gray-500 italic">
                      {t.skipped || 'Skipped'}
                    </span>
                  ) : data.secondaryEmail ? (
                    <span className="flex items-center">
                      {data.secondaryEmail}
                      {data.isVerified && (
                        <svg className="w-4 h-4 text-green-600 ml-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      )}
                    </span>
                  ) : (
                    <span className="text-gray-500 italic">
                      {t.notProvided || 'Not provided'}
                    </span>
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Welcome message */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
            <svg className="w-8 h-8 text-green-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {t.welcomeMessage || `Welcome, ${data.firstName}!`}
            </h3>
            <p className="text-gray-600">
              {t.readyMessage || 'You\'re ready to start using our platform. Click below to access your dashboard.'}
            </p>
          </div>
        </div>

        {/* Next steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-semibold text-blue-900 mb-2">
            {t.nextStepsTitle || 'What\'s Next?'}
          </h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li className="flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              {t.nextStep1 || 'Explore your personalized dashboard'}
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              {t.nextStep2 || 'Update your profile settings anytime'}
            </li>
            <li className="flex items-center">
              <svg className="w-4 h-4 text-blue-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              {t.nextStep3 || 'Get help from our support team if needed'}
            </li>
          </ul>
        </div>
      </div>
    </WizardStep>
  );
}