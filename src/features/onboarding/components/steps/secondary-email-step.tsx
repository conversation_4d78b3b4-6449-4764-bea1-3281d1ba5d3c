'use client';

import { useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { WizardStep } from '../wizard/wizard-step';
import { useSecondaryEmail } from '../../hooks/use-secondary-email';
import type { WizardStepProps } from '../../types/onboarding.types';

interface SecondaryEmailStepProps extends WizardStepProps {
  translations: any;
}

export function SecondaryEmailStep({ 
  data, 
  onNext, 
  isSubmitting: wizardSubmitting, 
  error: wizardError, 
  translations 
}: SecondaryEmailStepProps) {
  const t = translations.onboarding || {};
  
  const {
    email,
    setEmail,
    code,
    setCode,
    isVerifying,
    isSubmitting,
    error,
    addEmail,
    verifyCode,
    skipEmail,
    reset,
  } = useSecondaryEmail();

  // Initialize with existing data if available
  useEffect(() => {
    if (data.secondaryEmail && !email) {
      setEmail(data.secondaryEmail);
    }
  }, [data.secondaryEmail, email, setEmail]);

  const handleAddEmail = async () => {
    const success = await addEmail();
    if (!success) {
      // Error is handled by the hook
      return;
    }
  };

  const handleVerifyCode = async () => {
    const success = await verifyCode();
    if (success) {
      // Email verified successfully, proceed to next step
      onNext({
        secondaryEmail: email,
        isVerified: true,
        skipSecondaryEmail: false,
      });
    }
  };

  const handleSkip = () => {
    skipEmail();
    onNext({
      secondaryEmail: '',
      isVerified: false,
      skipSecondaryEmail: true,
    });
  };

  const handleTryAgain = () => {
    reset();
  };

  // Show verification form if we're in verification mode
  if (isVerifying) {
    return (
      <WizardStep
        title={t.verifyEmailTitle || 'Verify Your Email'}
        subtitle={t.verifyEmailSubtitle || `We've sent a verification code to ${email}`}
      >
        <div className="space-y-4">
          {/* Verification code input */}
          <div className="space-y-2">
            <Label htmlFor="verificationCode">
              {t.verificationCode || 'Verification Code'}
            </Label>
            <Input
              id="verificationCode"
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              placeholder={t.verificationCodePlaceholder || 'Enter the 6-digit code'}
              disabled={isSubmitting}
              maxLength={6}
              className="text-center text-lg tracking-widest"
            />
            <p className="text-sm text-gray-600">
              {t.verificationCodeHelp || 'Check your email for the verification code'}
            </p>
          </div>

          {/* Error display */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex flex-col space-y-3">
            <Button
              onClick={handleVerifyCode}
              disabled={!code.trim() || isSubmitting}
              className="w-full"
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  {t.verifying || 'Verifying...'}
                </div>
              ) : (
                t.verifyEmail || 'Verify Email'
              )}
            </Button>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleTryAgain}
                disabled={isSubmitting}
                className="flex-1"
              >
                {t.tryDifferentEmail || 'Try Different Email'}
              </Button>
              
              <Button
                type="button"
                variant="ghost"
                onClick={handleSkip}
                disabled={isSubmitting}
                className="flex-1"
              >
                {t.skipForNow || 'Skip for Now'}
              </Button>
            </div>
          </div>
        </div>
      </WizardStep>
    );
  }

  // Show email input form
  return (
    <WizardStep
      title={t.secondaryEmailTitle || 'Add Secondary Email (Optional)'}
      subtitle={t.secondaryEmailSubtitle || 'This helps us link your accounts if you have multiple products'}
    >
      <div className="space-y-4">
        {/* Email input */}
        <div className="space-y-2">
          <Label htmlFor="secondaryEmail">
            {t.secondaryEmail || 'Secondary Email Address'}
          </Label>
          <Input
            id="secondaryEmail"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={t.secondaryEmailPlaceholder || 'Enter your secondary email address'}
            disabled={isSubmitting}
          />
          <p className="text-sm text-gray-600">
            {t.secondaryEmailHelp || 'We\'ll send a verification code to confirm this email'}
          </p>
        </div>

        {/* Error display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
            <p className="text-sm text-gray-600 mt-2">
              💡 You can complete onboarding now and add a secondary email later from your account settings.
            </p>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex flex-col space-y-3">
          <Button
            onClick={handleAddEmail}
            disabled={!email.trim() || isSubmitting}
            className="w-full"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                {t.addingEmail || 'Adding Email...'}
              </div>
            ) : (
              t.addEmail || 'Add & Verify Email'
            )}
          </Button>

          <Button
            type="button"
            variant="ghost"
            onClick={handleSkip}
            disabled={isSubmitting}
            className="w-full"
          >
            {t.skipThisStep || 'Skip This Step'}
          </Button>
        </div>

        {/* Info box */}
        <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-blue-800">
                {t.whySecondaryEmailTitle || 'Why add a secondary email?'}
              </h4>
              <p className="text-sm text-blue-700 mt-1">
                {t.whySecondaryEmailText || 'If you\'ve purchased other products with a different email, this helps us connect your accounts and provide better support.'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </WizardStep>
  );
}