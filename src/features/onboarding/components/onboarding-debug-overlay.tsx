'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

export function OnboardingDebugOverlay() {
  const { user, isLoaded } = useUser();
  const [debugVisible, setDebugVisible] = useState(false);
  const [debugInfo, setDebugInfo] = useState({
    timestamp: new Date().toISOString(),
    userLoaded: false,
    hasUser: false,
    userId: null as string | null,
    onboardingComplete: null as boolean | null,
    metadata: null as any,
    currentPath: '',
    shouldRedirect: false
  });

  useEffect(() => {
    const updateDebugInfo = () => {
      setDebugInfo({
        timestamp: new Date().toISOString(),
        userLoaded: isLoaded,
        hasUser: !!user,
        userId: user?.id || null,
        onboardingComplete: user?.unsafeMetadata?.['onboardingComplete'] || null,
        metadata: user?.unsafeMetadata || null,
        currentPath: window.location.pathname,
        shouldRedirect: !!(user?.unsafeMetadata?.['onboardingComplete'])
      });
    };

    updateDebugInfo();
    const interval = setInterval(updateDebugInfo, 1000);

    return () => clearInterval(interval);
  }, [isLoaded, user]);

  // Auto-show in development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setDebugVisible(true);
    }
  }, []);

  if (!debugVisible) {
    return (
      <button
        onClick={() => setDebugVisible(true)}
        className="fixed bottom-4 right-4 bg-red-500 text-white px-3 py-1 rounded text-xs z-50"
      >
        Debug
      </button>
    );
  }

  return (
    <div className="fixed top-4 right-4 bg-black bg-opacity-90 text-white p-4 rounded-lg max-w-md text-xs z-50">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-bold text-yellow-400">Onboarding Debug</h3>
        <button
          onClick={() => setDebugVisible(false)}
          className="text-white hover:text-red-400"
        >
          ✕
        </button>
      </div>
      
      <div className="space-y-1">
        <div className={`flex justify-between ${debugInfo.userLoaded ? 'text-green-400' : 'text-red-400'}`}>
          <span>User Loaded:</span>
          <span>{debugInfo.userLoaded ? '✅' : '❌'}</span>
        </div>
        
        <div className={`flex justify-between ${debugInfo.hasUser ? 'text-green-400' : 'text-red-400'}`}>
          <span>Has User:</span>
          <span>{debugInfo.hasUser ? '✅' : '❌'}</span>
        </div>
        
        {debugInfo.userId && (
          <div className="flex justify-between text-blue-400">
            <span>User ID:</span>
            <span>{debugInfo.userId.substring(0, 8)}...</span>
          </div>
        )}
        
        <div className={`flex justify-between ${
          debugInfo.onboardingComplete === true ? 'text-green-400' : 
          debugInfo.onboardingComplete === false ? 'text-yellow-400' : 'text-gray-400'
        }`}>
          <span>Onboarding:</span>
          <span>{
            debugInfo.onboardingComplete === true ? '✅ Complete' :
            debugInfo.onboardingComplete === false ? '⏳ Incomplete' : '❓ Unknown'
          }</span>
        </div>
        
        {debugInfo.shouldRedirect && (
          <div className="text-red-400 font-bold">
            ⚠️ Should redirect to dashboard!
          </div>
        )}
        
        {debugInfo.onboardingComplete === false && (
          <div className="text-green-400 font-bold">
            ✅ Correctly showing onboarding wizard
          </div>
        )}
        
        <div className="text-xs text-gray-400 mt-2">
          Path: {debugInfo.currentPath}
        </div>
        
        <div className="text-xs text-gray-400">
          Last Update: {new Date(debugInfo.timestamp).toLocaleTimeString()}
        </div>
        
        {debugInfo.metadata && (
          <details className="mt-2">
            <summary className="text-yellow-400 cursor-pointer">Metadata</summary>
            <pre className="text-xs bg-gray-800 p-2 rounded mt-1 overflow-auto max-h-32">
              {JSON.stringify(debugInfo.metadata, null, 1)}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}
