/**
 * Utility functions for handling email operations in onboarding
 */

/**
 * Check if user can add secondary emails
 * Some users might have restrictions based on their account state
 */
export function canAddSecondaryEmail(user: any): boolean {
  // Basic checks
  if (!user) return false;
  
  // Check if user has verified their primary email
  const primaryEmail = user.emailAddresses?.find((email: any) => email.isPrimary);
  if (primaryEmail?.verification?.status !== 'verified') {
    return false;
  }
  
  // Check if user already has multiple emails
  if (user.emailAddresses?.length >= 3) {
    return false;
  }
  
  return true;
}

/**
 * Get user-friendly message for why secondary email cannot be added
 */
export function getSecondaryEmailRestrictionMessage(user: any): string | null {
  if (!user) return 'User not loaded';
  
  // During onboarding, be more lenient with email verification checks
  // If user is signed in and has emails, they're likely verified enough for secondary email
  if (!user.emailAddresses || user.emailAddresses.length === 0) {
    return 'No primary email found';
  }
  
  // Only restrict if user already has too many emails
  if (user.emailAddresses?.length >= 3) {
    return 'You can only have up to 3 email addresses';
  }
  
  // For onboarding flow, allow secondary email addition
  // The actual verification will be handled by Clerk's API
  return null;
}

/**
 * Check if email address is already in use by this user
 */
export function isEmailAlreadyAdded(user: any, email: string): boolean {
  if (!user?.emailAddresses) return false;
  
  return user.emailAddresses.some((emailAddr: any) => 
    emailAddr.emailAddress.toLowerCase() === email.toLowerCase()
  );
}
