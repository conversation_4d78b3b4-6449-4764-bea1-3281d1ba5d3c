// src/features/onboarding/utils/onboarding-guard.ts
// Following <PERSON>'s official recommendation: Use unsafeMetadata for onboarding flows
// "Unsafe metadata is the only metadata property that can be set during sign-up, 
// so a common use case is to use it in custom onboarding flows."
// Source: https://clerk.com/docs/users/metadata#unsafe-metadata

import { User } from '@clerk/nextjs/server';

export function needsOnboarding(user: User | null | undefined): boolean {
  if (!user) return false;
  // Check unsafeMetadata for onboarding completion flag
  return user.unsafeMetadata?.['onboardingComplete'] !== true;
}

export function isOnboardingCompleted(user: User | null | undefined): boolean {
  if (!user) return false;
  // Check unsafeMetadata for onboarding completion flag
  return user.unsafeMetadata?.['onboardingComplete'] === true;
}

export function getPreferredLanguage(user: User | null | undefined): string | null {
  if (!user) return null;
  // Get language from unsafeMetadata (user-modifiable preference)
  return (user.unsafeMetadata?.['language'] as string) || null;
}