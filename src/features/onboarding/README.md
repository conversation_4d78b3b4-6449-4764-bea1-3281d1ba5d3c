# Onboarding Feature

This feature handles the user onboarding flow after registration, following the official Clerk documentation pattern.

## Architecture

- **actions/**: Server actions for completing onboarding
- **components/**: React components for the onboarding form
- **utils/**: Utility functions for onboarding checks

## Flow

1. User signs up and is redirected to `/onboarding` (via `NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL`)
2. Middleware checks if user has `onboardingComplete: true` in their metadata
3. If not, user is redirected to onboarding page
4. User completes form with language, first name, and last name
5. Server action updates user metadata and redirects to dashboard

## Key Files

- `middleware.ts`: Handles onboarding redirects
- `types/globals.d.ts`: Custom JWT session claims
- `actions/onboarding.actions.ts`: Server action to complete onboarding
- `components/onboarding-form.tsx`: Main onboarding form

## Clerk Dashboard Setup

Add this to your session token claims in the Clerk Dashboard:

```json
{
  "metadata": "{{user.public_metadata}}"
}
```