/**
 * Onboarding type definitions
 * Following Clerk best practices with publicMetadata
 */

import type { SupportedLanguage } from '@/features/auth/services/language.service';

export type OnboardingStep = 'basic-info' | 'secondary-email' | 'completion';

export interface BasicInfoData {
  firstName: string;
  lastName: string;
  language: SupportedLanguage;
}

export interface SecondaryEmailData {
  secondaryEmail?: string;
  isVerified: boolean;
  skipSecondaryEmail: boolean;
}

export interface OnboardingData extends BasicInfoData, SecondaryEmailData {
  completedSteps: OnboardingStep[];
  currentStep: OnboardingStep;
  completedAt?: string;
}

export interface OnboardingWizardState {
  currentStep: OnboardingStep;
  completedSteps: OnboardingStep[];
  data: Partial<OnboardingData>;
  isSubmitting: boolean;
  error: string | null;
}

export interface StepValidation {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface WizardStepProps {
  data: Partial<OnboardingData>;
  onNext: (stepData: any) => void;
  onBack: () => void;
  isSubmitting: boolean;
  error: string | null;
}