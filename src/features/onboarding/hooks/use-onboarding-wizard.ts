'use client';

import { useState, useCallback, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { LanguageService } from '@/features/auth/services/language.service';
import type { 
  OnboardingStep, 
  OnboardingWizardState, 
  OnboardingData,
  BasicInfoData,
  SecondaryEmailData,
  StepValidation 
} from '../types/onboarding.types';

const STEPS: OnboardingStep[] = ['basic-info', 'secondary-email', 'completion'];

/**
 * Onboarding wizard state management hook
 * Handles step navigation, data collection, and validation
 */
export function useOnboardingWizard(_locale: string) {
  const { user, isLoaded: userLoaded } = useUser();
  const router = useRouter();

  // Add comprehensive loading detection
  const [debugInfo, setDebugInfo] = useState({
    userLoaded: false,
    hasUser: false,
    onboardingStatus: null as boolean | null,
    redirecting: false,
    lastCheck: new Date().toISOString()
  });

  // Check if user is already onboarded - but don't redirect if we're already on onboarding page
  useEffect(() => {
    if (userLoaded && user) {
      const onboardingComplete = user.unsafeMetadata?.['onboardingComplete'];
      
      setDebugInfo(prev => ({
        ...prev,
        userLoaded: true,
        hasUser: true,
        onboardingStatus: onboardingComplete as boolean,
        lastCheck: new Date().toISOString()
      }));

      // Only redirect to dashboard if onboarding is complete
      // For onboarding pages, we allow a brief delay to show completion screen
      const currentPath = window.location.pathname;
      const isOnOnboardingPage = currentPath.includes('/onboarding');
      
      if (onboardingComplete) {
        console.log('✅ User has completed onboarding');
        
        if (isOnOnboardingPage) {
          // User is on onboarding page and has completed - redirect after a brief delay
          const completedAt = user.unsafeMetadata?.['onboardingCompletedAt'] as string;
          if (completedAt) {
            const completedTime = new Date(completedAt).getTime();
            const now = new Date().getTime();
            const timeSinceCompletion = now - completedTime;
            
            // Allow 3 seconds to see completion screen, then redirect
            if (timeSinceCompletion > 3000) {
              console.log('🎯 Onboarding completed, redirecting to dashboard...');
              setDebugInfo(prev => ({ ...prev, redirecting: true }));
              
              const locale = currentPath.split('/')[1] || 'en';
              router.push(`/${locale}/dashboard`);
              return;
            } else {
              console.log('⏳ Recently completed onboarding, showing completion screen...');
            }
          } else {
            // No timestamp, redirect immediately
            console.log('🎯 Onboarding completed (no timestamp), redirecting to dashboard...');
            setDebugInfo(prev => ({ ...prev, redirecting: true }));
            
            const locale = currentPath.split('/')[1] || 'en';
            router.push(`/${locale}/dashboard`);
            return;
          }
        } else {
          // User completed onboarding but not on onboarding page - redirect immediately
          console.log('🎯 User completed onboarding and not on onboarding page, redirecting to dashboard...');
          setDebugInfo(prev => ({ ...prev, redirecting: true }));
          
          const locale = currentPath.split('/')[1] || 'en';
          router.push(`/${locale}/dashboard`);
          return;
        }
      } else {
        console.log('📝 User needs to complete onboarding - showing wizard');
      }
    } else if (userLoaded && !user) {
      console.log('❌ No user found, should redirect to login');
      setDebugInfo(prev => ({
        ...prev,
        userLoaded: true,
        hasUser: false,
        lastCheck: new Date().toISOString()
      }));
    }
  }, [userLoaded, user, router]);

  // Initialize state with user data if available
  const [state, setState] = useState<OnboardingWizardState>(() => {
    const initialData: Partial<OnboardingData> = {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      language: user ? LanguageService.getUserLanguage(user) : LanguageService.DEFAULT_LANGUAGE,
      secondaryEmail: '',
      isVerified: false,
      skipSecondaryEmail: false,
      completedSteps: [],
      currentStep: 'basic-info',
    };

    return {
      currentStep: 'basic-info',
      completedSteps: [],
      data: initialData,
      isSubmitting: false,
      error: null,
    };
  });

  // Step navigation helpers
  const getCurrentStepIndex = useCallback(() => {
    return STEPS.indexOf(state.currentStep);
  }, [state.currentStep]);

  const canGoNext = useCallback(() => {
    // Allow "next" on completion step (which triggers completion)
    // or when we're not on the last step
    return getCurrentStepIndex() < STEPS.length - 1 || state.currentStep === 'completion';
  }, [getCurrentStepIndex, state.currentStep]);

  const canGoBack = useCallback(() => {
    return getCurrentStepIndex() > 0;
  }, [getCurrentStepIndex]);

  // Validation functions
  const validateBasicInfo = useCallback((data: Partial<BasicInfoData>): StepValidation => {
    const errors: Record<string, string> = {};

    if (!data['firstName']?.trim()) {
      errors['firstName'] = 'First name is required';
    }
    if (!data['lastName']?.trim()) {
      errors['lastName'] = 'Last name is required';
    }
    if (!data['language']) {
      errors['language'] = 'Language selection is required';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }, []);

  const validateSecondaryEmail = useCallback((data: Partial<SecondaryEmailData>): StepValidation => {
    const errors: Record<string, string> = {};

    // If user provided email but didn't verify and didn't skip
    if (data['secondaryEmail'] && !data.isVerified && !data.skipSecondaryEmail) {
      errors['secondaryEmail'] = 'Please verify your email or skip this step';
    }

    // Basic email format validation if provided
    if (data['secondaryEmail'] && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data['secondaryEmail'])) {
      errors['secondaryEmail'] = 'Please enter a valid email address';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }, []);

  const validateCurrentStep = useCallback((): StepValidation => {
    switch (state.currentStep) {
      case 'basic-info':
        return validateBasicInfo(state.data);
      case 'secondary-email':
        return validateSecondaryEmail(state.data);
      case 'completion':
        return { isValid: true, errors: {} };
      default:
        return { isValid: false, errors: { general: 'Invalid step' } };
    }
  }, [state.currentStep, state.data, validateBasicInfo, validateSecondaryEmail]);

  // Step navigation actions
  const goToStep = useCallback((step: OnboardingStep) => {
    setState(prev => ({
      ...prev,
      currentStep: step,
      error: null,
    }));
  }, []);

  const goNext = useCallback((stepData?: any) => {
    // Update data with step-specific data first
    const updatedData = { ...state.data, ...stepData };
    
    // Validate using the updated data instead of old state data
    const validation = state.currentStep === 'basic-info' 
      ? validateBasicInfo(updatedData)
      : state.currentStep === 'secondary-email'
      ? validateSecondaryEmail(updatedData)
      : { isValid: true, errors: {} };
    
    if (!validation.isValid) {
      setState(prev => ({
        ...prev,
        error: Object.values(validation.errors)[0] || 'Please fix the errors before continuing',
      }));
      return false;
    }
    
    // Mark current step as completed
    const completedSteps = [...state.completedSteps];
    if (!completedSteps.includes(state.currentStep)) {
      completedSteps.push(state.currentStep);
    }

    if (canGoNext()) {
      const nextStepIndex = getCurrentStepIndex() + 1;
      const nextStep = STEPS[nextStepIndex];
      
      if (nextStep) {
        setState(prev => ({
          ...prev,
          currentStep: nextStep,
          completedSteps,
          data: updatedData,
          error: null,
        }));
      }
    }

    return true;
  }, [state, canGoNext, getCurrentStepIndex, validateBasicInfo, validateSecondaryEmail]);

  const goBack = useCallback(() => {
    if (canGoBack()) {
      const prevStepIndex = getCurrentStepIndex() - 1;
      const prevStep = STEPS[prevStepIndex];
      
      if (prevStep) {
        setState(prev => ({
          ...prev,
          currentStep: prevStep,
          error: null,
        }));
      }
    }
  }, [canGoBack, getCurrentStepIndex]);

  // Data update helpers
  const updateData = useCallback((newData: Partial<OnboardingData>) => {
    setState(prev => ({
      ...prev,
      data: { ...prev.data, ...newData },
      error: null,
    }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
    }));
  }, []);

  const setSubmitting = useCallback((isSubmitting: boolean) => {
    setState(prev => ({
      ...prev,
      isSubmitting,
    }));
  }, []);

  // Complete onboarding
  const completeOnboarding = useCallback(async () => {
    try {
      setSubmitting(true);
      setError(null);

      // Final validation
      const validation = validateCurrentStep();
      if (!validation.isValid) {
        setError(Object.values(validation.errors)[0] || 'Please complete all required fields');
        return false;
      }

      // Prepare complete onboarding data
      const onboardingData: OnboardingData = {
        firstName: state.data.firstName || '',
        lastName: state.data.lastName || '',
        language: state.data.language || LanguageService.DEFAULT_LANGUAGE,
        secondaryEmail: state.data.secondaryEmail || '',
        isVerified: state.data.isVerified || false,
        skipSecondaryEmail: state.data.skipSecondaryEmail || false,
        completedSteps: [...state.completedSteps, state.currentStep],
        currentStep: state.currentStep,
        completedAt: new Date().toISOString(),
      };

      // Import and call the server action
      const { completeOnboardingWizard } = await import('../actions/onboarding-wizard.actions');
      const result = await completeOnboardingWizard(onboardingData);

      if (result?.error) {
        setError(result.error);
        return false;
      }

      // Force session token refresh to ensure middleware sees updated metadata
      // This is critical because the session token needs to include the new onboarding status
      if (user) {
        // Force user data refresh to ensure we get the updated metadata
        await user.reload();
        
        // Wait a bit to ensure the session is fully refreshed
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log('✅ Onboarding completed successfully');
      
      // Don't redirect immediately - let the useEffect handle it with proper timing
      // This allows the user to see the completion screen briefly
      
      return true;
    } catch (error) {
      console.error('❌ Error completing onboarding:', error);
      setError('There was an error completing your onboarding. Please try again.');
      return false;
    } finally {
      setSubmitting(false);
    }
  }, [state, validateCurrentStep, user]);

  return {
    // State
    currentStep: state.currentStep,
    completedSteps: state.completedSteps,
    data: state.data,
    isSubmitting: state.isSubmitting,
    error: state.error,
    
    // Navigation
    canGoNext: canGoNext(),
    canGoBack: canGoBack(),
    currentStepIndex: getCurrentStepIndex(),
    totalSteps: STEPS.length,
    
    // Actions
    goToStep,
    goNext,
    goBack,
    updateData,
    setError,
    completeOnboarding,
    
    // Debug information for troubleshooting
    debugInfo,
    
    // Validation
    validateCurrentStep,
  };
}