'use client';

import { useState, useCallback } from 'react';
import { useUser, useReverification } from '@clerk/nextjs';
import { EmailAddressResource } from '@clerk/types';
import { isClerkRuntimeError, isReverificationCancelledError } from '@clerk/nextjs/errors';
import { getSecondaryEmailRestrictionMessage, isEmailAlreadyAdded } from '../utils/email-utils';
import { addSecondaryEmailAction, updateSecondaryEmailVerifiedAction } from '../actions/secondary-email.actions';

interface UseSecondaryEmailReturn {
  email: string;
  setEmail: (email: string) => void;
  code: string;
  setCode: (code: string) => void;
  isVerifying: boolean;
  isSubmitting: boolean;
  error: string | null;
  emailObj: EmailAddressResource | undefined;
  addEmail: () => Promise<boolean>;
  verifyCode: () => Promise<boolean>;
  skipEmail: () => void;
  reset: () => void;
}

/**
 * Hook for managing secondary email addition with proper server-side reverification
 * Follows Clerk's recommended pattern for server-side sensitive actions
 */
export function useSecondaryEmail(): UseSecondaryEmailReturn {
  const { user } = useUser();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailObj, setEmailObj] = useState<EmailAddressResource | undefined>();

  // Use reverification hook to handle server action that may require reverification
  // This is the correct pattern for server-side reverification according to Clerk docs
  const addEmailWithReverification = useReverification(addSecondaryEmailAction);

  const addEmail = useCallback(async (): Promise<boolean> => {
    if (!user) {
      setError('User not loaded');
      return false;
    }

    if (!email.trim()) {
      setError('Please enter an email address');
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    // Check if user can add secondary emails
    console.log('🔍 User object for secondary email check:', {
      hasUser: !!user,
      emailAddresses: user?.emailAddresses?.map(e => ({
        id: e.id,
        emailAddress: e.emailAddress,
        verificationStatus: e.verification?.status
      })),
      totalEmails: user?.emailAddresses?.length
    });

    const restrictionMessage = getSecondaryEmailRestrictionMessage(user);
    if (restrictionMessage) {
      console.log('❌ Secondary email restricted:', restrictionMessage);
      setError(restrictionMessage + ' You can skip this step for now.');
      return false;
    }

    // Check if email is already added
    if (isEmailAlreadyAdded(user, email)) {
      setError('This email address is already associated with your account.');
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('🔍 Adding secondary email via server action with reverification:', email);

      // Call server action through reverification wrapper
      // If reverification is required, the useReverification hook will handle the modal
      const result = await addEmailWithReverification(email);
      
      if (result?.success) {
        // Server successfully added the email
        console.log('✅ Secondary email added successfully via server action');

        // Reload user to get updated User object
        await user?.reload();

        // Find the email address that was just added
        const emailAddress = user?.emailAddresses.find((a) => a.emailAddress === email);
        
        if (!emailAddress) {
          throw new Error('Email address not found after creation');
        }

        setEmailObj(emailAddress);

        // Send verification code (this needs to be client-side)
        await emailAddress.prepareVerification({ strategy: 'email_code' });

        console.log('✅ Secondary email verification code sent');
        
        // Switch to verification mode
        setIsVerifying(true);
        return true;
      } else if (result && !result.success) {
        // Server returned an error
        throw new Error(result.error || 'Failed to add email address');
      } else {
        // Result is null/undefined - likely user cancelled reverification
        console.log('User cancelled reverification or operation was interrupted');
        setError('Email addition cancelled. You can skip this step for now.');
        return false;
      }

    } catch (err: any) {
      console.error('❌ Error adding secondary email:', err);
      
      // Handle specific Clerk errors
      if (isClerkRuntimeError(err)) {
        setError(err.message);
      } else if (isReverificationCancelledError(err)) {
        // User cancelled reverification modal - this is not an error to show
        console.log('User cancelled reverification');
        setError('Email addition cancelled. You can skip this step for now.');
      } else if (err?.message) {
        setError(err.message);
      } else {
        setError('Unable to add secondary email at this time. You can skip this step and add it later from your account settings.');
      }
      
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [email, user]);

  const verifyCode = useCallback(async (): Promise<boolean> => {
    if (!code.trim()) {
      setError('Please enter the verification code');
      return false;
    }

    if (!emailObj) {
      setError('No email address to verify');
      return false;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      console.log('🔍 Verifying email code:', code);

      // Verify the code
      const emailVerifyAttempt = await emailObj.attemptVerification({ code });

      if (emailVerifyAttempt?.verification.status === 'verified') {
        console.log('✅ Secondary email verified successfully');
        
        // Update metadata to reflect verification
        await updateSecondaryEmailVerifiedAction(emailObj.id);
        
        return true;
      } else {
        console.error('❌ Email verification failed:', emailVerifyAttempt);
        setError('Invalid verification code. Please try again.');
        return false;
      }

    } catch (err: any) {
      console.error('❌ Error verifying email code:', err);
      
      // Handle specific Clerk errors
      if (err?.errors?.[0]?.message) {
        setError(err.errors[0].message);
      } else if (err?.message) {
        setError(err.message);
      } else {
        setError('Failed to verify code. Please try again.');
      }
      
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [code, emailObj]);

  const skipEmail = useCallback(() => {
    console.log('🔍 User skipped secondary email');
    setEmail('');
    setCode('');
    setIsVerifying(false);
    setError(null);
    setEmailObj(undefined);
  }, []);

  const reset = useCallback(() => {
    setEmail('');
    setCode('');
    setIsVerifying(false);
    setIsSubmitting(false);
    setError(null);
    setEmailObj(undefined);
  }, []);

  return {
    email,
    setEmail,
    code,
    setCode,
    isVerifying,
    isSubmitting,
    error,
    emailObj,
    addEmail,
    verifyCode,
    skipEmail,
    reset,
  };
}