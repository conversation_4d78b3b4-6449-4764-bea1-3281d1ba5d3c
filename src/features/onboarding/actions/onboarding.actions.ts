'use server';

import { auth, clerkClient } from '@clerk/nextjs/server';
import { cookies } from 'next/headers';

export const completeOnboarding = async (formData: FormData) => {
  try {
    console.log('🔍 Onboarding: Starting auth() call...');
    
    // Get the authenticated user's ID from the request (following Clerk docs pattern)
    const { userId } = await auth();
    
    console.log('✅ Onboarding: Auth result:', { userId });
    
    // Check if user is authenticated
    if (!userId) {
      console.error('❌ No authenticated user found');
      return { error: 'No authenticated user. Please log in again.' };
    }

    console.log('📝 Form data received:', {
      language: formData.get('language'),
      firstName: formData.get('firstName'),
      lastName: formData.get('lastName')
    });

    const language = formData.get('language') as string;
    const firstName = formData.get('firstName') as string;
    const lastName = formData.get('lastName') as string;

    if (!language || !firstName || !lastName) {
      return { error: 'All fields are required' };
    }

    // For Next.js 13+ with App Router, await the client instantiation
    const client = await clerkClient();
    
    // First, get current user data to preserve existing unsafeMetadata
    const currentUser = await client.users.getUser(userId);
    
    // Update user basic info
    await client.users.updateUser(userId, {
      firstName,
      lastName,
    });

    // Following Clerk's official recommendation: Use unsafeMetadata for onboarding flows
    // "Unsafe metadata is the only metadata property that can be set during sign-up, 
    // so a common use case is to use it in custom onboarding flows."
    // Source: https://clerk.com/docs/users/metadata#unsafe-metadata
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        ...currentUser.unsafeMetadata, // Preserve existing unsafeMetadata
        onboardingComplete: true,
        onboardingCompletedAt: new Date().toISOString(),
        onboardingSteps: ['basic-info'], // Track completed steps
        language: language, // Single source of truth for language
        updatedAt: new Date().toISOString(),
      },
    });

    // CRITICAL: Reload user to ensure session claims are updated
    // This ensures middleware will see the updated onboarding completion status
    const updatedUser = await client.users.getUser(userId);
    console.log('✅ User metadata updated and reloaded:', {
      publicMetadata: updatedUser.publicMetadata,
      unsafeMetadata: updatedUser.unsafeMetadata
    });

    // Set language cookie
    const cookieStore = await cookies();
    cookieStore.set('locale', language, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });

    console.log('✅ Onboarding completed successfully for user:', userId);
    
    // IMPORTANT: Return success message to trigger client-side token refresh
    // The client component should call user.reload() or getToken({ skipCache: true })
    // after receiving this response to ensure the session token is updated
    return { 
      success: true, 
      message: 'Onboarding completed successfully',
      language,
      shouldReload: true // Signal that client should refresh token
    };
    
  } catch (error) {
    console.error('❌ Error completing onboarding:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Handle specific auth errors
    if (errorMessage.includes('token-not-active-yet') || 
        errorMessage.includes('nbf') || 
        errorMessage.includes('clock skew')) {
      console.error('⏰ Clock skew detected - system time may be incorrect');
      return { error: 'Authentication token issue. Your system clock may be incorrect. Please try refreshing the page and logging in again.' };
    }
    
    return { error: 'There was an error updating your information. Please try again.' };
  }
};