'use server';

import { auth, clerkClient, reverificationError } from '@clerk/nextjs/server';

/**
 * Server action to add secondary email with proper reverification handling
 * Follows Clerk's recommended pattern for server-side sensitive actions
 */
export async function addSecondaryEmailAction(email: string) {
  try {
    // Get authenticated user with reverification check
    const { userId, has } = await auth.protect();
    
    if (!userId) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    // Check if user has verified credentials within the past 10 minutes
    const shouldUserRevalidate = !has({ reverification: 'strict' });

    if (shouldUserRevalidate) {
      console.log('🔍 Server: User needs reverification for secondary email');
      // Return reverification error that client can handle
      return reverificationError('strict');
    }

    console.log('🔍 Server: Adding secondary email for user:', userId, 'email:', email);

    // Create email address using server-side API
    const client = await clerkClient();
    const emailAddress = await client.emailAddresses.createEmailAddress({
      userId,
      emailAddress: email,
      primary: false,
      verified: false  // Will need verification, but reverification is satisfied
    });

    console.log('✅ Server: Secondary email created:', emailAddress.id);

    // Update metadata to track secondary email addition
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        secondaryEmailAdded: true,
        secondaryEmailId: emailAddress.id,
        secondaryEmailAddress: emailAddress.emailAddress,
        secondaryEmailVerified: false, // Will be updated when verified
        secondaryEmailAddedAt: new Date().toISOString()
      }
    });

    console.log('✅ Server: Metadata updated for secondary email');

    return {
      success: true,
      emailAddressId: emailAddress.id,
      emailAddress: emailAddress.emailAddress
    };

  } catch (error: any) {
    console.error('❌ Server: Error adding secondary email:', error);
    
    // Handle specific errors
    if (error?.errors?.[0]?.code === 'form_identifier_exists') {
      return {
        success: false,
        error: 'This email address is already associated with another account.'
      };
    }
    
    if (error?.errors?.[0]?.code === 'form_identifier_not_allowed') {
      return {
        success: false,
        error: 'This email address cannot be added. Please try a different email.'
      };
    }

    return {
      success: false,
      error: error?.message || 'Failed to add secondary email'
    };
  }
}

/**
 * Server action to update metadata when secondary email is verified
 */
export async function updateSecondaryEmailVerifiedAction(_emailAddressId: string) {
  try {
    const { userId } = await auth.protect();
    
    if (!userId) {
      return {
        success: false,
        error: 'User not authenticated'
      };
    }

    console.log('🔍 Server: Updating secondary email verification status');

    const client = await clerkClient();
    
    // Update metadata to mark secondary email as verified
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        secondaryEmailVerified: true,
        secondaryEmailVerifiedAt: new Date().toISOString()
      }
    });

    console.log('✅ Server: Secondary email verification status updated');

    return {
      success: true
    };

  } catch (error: any) {
    console.error('❌ Server: Error updating secondary email verification:', error);
    
    return {
      success: false,
      error: error?.message || 'Failed to update verification status'
    };
  }
}
