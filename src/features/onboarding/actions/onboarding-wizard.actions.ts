'use server';

import { auth, clerkClient } from '@clerk/nextjs/server';
import { cookies } from 'next/headers';
import type { SupportedLanguage } from '@/features/auth/services/language.service';
import type { OnboardingData } from '../types/onboarding.types';

/**
 * Complete onboarding with wizard data
 * Following Clerk's official recommendation: Use unsafeMetadata for onboarding flows
 * "Unsafe metadata is the only metadata property that can be set during sign-up, 
 * so a common use case is to use it in custom onboarding flows."
 * Source: https://clerk.com/docs/users/metadata#unsafe-metadata
 */
export const completeOnboardingWizard = async (data: OnboardingData) => {
  try {
    console.log('🔍 Onboarding Wizard: Starting completion process...');
    
    const { userId } = await auth();
    
    if (!userId) {
      console.error('❌ No authenticated user found');
      return { error: 'No authenticated user. Please log in again.' };
    }

    console.log('📝 Onboarding data received:', {
      firstName: data.firstName,
      lastName: data.lastName,
      language: data.language,
      secondaryEmail: data.secondaryEmail ? '***@***.***' : 'none',
      isVerified: data.isVerified,
      skipSecondaryEmail: data.skipSecondaryEmail,
      completedSteps: data.completedSteps,
    });

    const client = await clerkClient();
    
    // Get current user data to preserve existing metadata
    const currentUser = await client.users.getUser(userId);
    
    // Update user basic info
    await client.users.updateUser(userId, {
      firstName: data.firstName,
      lastName: data.lastName,
    });

    // Following Clerk's official recommendation: Use unsafeMetadata for onboarding flows
    // "Unsafe metadata is the only metadata property that can be set during sign-up, 
    // so a common use case is to use it in custom onboarding flows."
    // Source: https://clerk.com/docs/users/metadata#unsafe-metadata
    
    const metadataUpdate = {
      unsafeMetadata: {
        ...currentUser.unsafeMetadata, // Preserve existing unsafeMetadata
        onboardingComplete: true,
        onboardingCompletedAt: new Date().toISOString(),
        onboardingSteps: data.completedSteps,
        secondaryEmailAdded: !!data.secondaryEmail,
        secondaryEmailVerified: data.isVerified,
        language: data.language, // Single source of truth for language
        updatedAt: new Date().toISOString(),
      },
    };
    
    await client.users.updateUserMetadata(userId, metadataUpdate);

    // Set language cookie
    const cookieStore = await cookies();
    cookieStore.set('locale', data.language, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });

    console.log('✅ Onboarding wizard completed successfully for user:', userId);
    
    // IMPORTANT: Return success message to trigger client-side token refresh
    // The client component should call user.reload() or getToken({ skipCache: true })
    // after receiving this response to ensure the session token is updated
    return { 
      success: true, 
      message: 'Onboarding completed successfully',
      language: data.language,
      shouldReload: true // Signal that client should refresh token
    };
    
  } catch (error) {
    console.error('❌ Error completing onboarding wizard:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // Handle specific auth errors
    if (errorMessage.includes('token-not-active-yet') || 
        errorMessage.includes('nbf') || 
        errorMessage.includes('clock skew')) {
      console.error('⏰ Clock skew detected - system time may be incorrect');
      return { error: 'Authentication token issue. Your system clock may be incorrect. Please try refreshing the page and logging in again.' };
    }
    
    return { error: 'There was an error completing your onboarding. Please try again.' };
  }
};

/**
 * Update onboarding step completion
 * Following Clerk's official recommendation: Use unsafeMetadata for onboarding flows
 */
export const updateOnboardingStep = async (
  step: string, 
  stepData: any, 
  language: SupportedLanguage
) => {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return { error: 'No authenticated user found' };
    }

    const client = await clerkClient();
    const currentUser = await client.users.getUser(userId);
    
    // Get existing onboarding steps from unsafeMetadata
    const existingSteps = (currentUser.unsafeMetadata?.['onboardingSteps'] as string[]) || [];
    const updatedSteps = Array.from(new Set([...existingSteps, step])); // Avoid duplicates

    // Following Clerk's official recommendation: Use unsafeMetadata for onboarding flows
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        ...currentUser.unsafeMetadata,
        onboardingSteps: updatedSteps,
        [`onboarding_${step}_data`]: stepData,
        onboardingLastUpdated: new Date().toISOString(),
        language, // Keep language updated for LanguageService and profile system
        updatedAt: new Date().toISOString(),
      },
    });

    // Update language cookie
    const cookieStore = await cookies();
    cookieStore.set('locale', language, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });

    console.log(`✅ Onboarding step '${step}' updated successfully`);
    
    return { success: true };
    
  } catch (error) {
    console.error(`❌ Error updating onboarding step '${step}':`, error);
    return { error: 'Failed to update onboarding progress' };
  }
};