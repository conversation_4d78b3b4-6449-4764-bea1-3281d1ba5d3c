// NOTE: This file is currently not in use. 
// We've switched to client-side user.update() approach for better reliability.
// The forms now use client-side updates with server-side cookie management.
// Keeping this file for reference in case server-side approach is needed in the future.

'use server';

import { auth, clerkClient } from '@clerk/nextjs/server';
import { cookies } from 'next/headers';

export interface UpdateProfileData {
  firstName: string;
  lastName: string;
  bio: string;
  language: string;
  customName: string;
}

export interface UpdateMetadataData {
  bio?: string;
  language?: string;
  [key: string]: any; // Allow any additional metadata fields
}

export interface UpdateProfileResult {
  success: boolean;
  error?: string;
}

export const updateProfile = async (data: UpdateProfileData): Promise<UpdateProfileResult> => {
  try {
    const authResult = await auth();
    const userId = authResult.userId;
    
    if (!userId) {
      return { success: false, error: 'User not authenticated. Please log in again.' };
    }

    const client = await clerk<PERSON>lient();
    
    // First, get current user data to preserve existing unsafeMetadata
    const currentUser = await client.users.getUser(userId);
    
    // Update user basic info
    await client.users.updateUser(userId, {
      firstName: data.firstName,
      lastName: data.lastName,
    });

    // Update unsafeMetadata (server-side approach using updateUserMetadata)
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        ...currentUser.unsafeMetadata, // Preserve existing metadata
        bio: data.bio,
        language: data.language,
        customName: data.customName,
        updatedAt: new Date().toISOString(),
      },
    });

    // Update language cookie if language changed
    const cookieStore = await cookies();
    cookieStore.set('locale', data.language, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });

    console.log('✅ Profile updated successfully for user:', userId);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating profile:', error);
    return { 
      success: false, 
      error: 'There was an error updating your profile. Please try again.' 
    };
  }
};

// Separate action for updating only metadata (bio, language, etc.)
export const updateMetadata = async (data: UpdateMetadataData): Promise<UpdateProfileResult> => {
  try {
    const authResult = await auth();
    const userId = authResult.userId;
    
    if (!userId) {
      return { success: false, error: 'User not authenticated. Please log in again.' };
    }

    const client = await clerkClient();
    
    // First, get current user data to preserve existing unsafeMetadata
    const currentUser = await client.users.getUser(userId);
    
    // Update unsafeMetadata with provided data
    await client.users.updateUserMetadata(userId, {
      unsafeMetadata: {
        ...currentUser.unsafeMetadata, // Preserve existing metadata
        ...data, // Spread the provided data
        updatedAt: new Date().toISOString(),
      },
    });

    // Update language cookie if language was changed
    if (data.language) {
      const cookieStore = await cookies();
      cookieStore.set('locale', data.language, {
        path: '/',
        maxAge: 60 * 60 * 24 * 365, // 1 year
      });
    }

    console.log('✅ Metadata updated successfully for user:', userId);
    
    return { success: true };
  } catch (error) {
    console.error('Error updating metadata:', error);
    return { 
      success: false, 
      error: 'There was an error updating your preferences. Please try again.' 
    };
  }
};
