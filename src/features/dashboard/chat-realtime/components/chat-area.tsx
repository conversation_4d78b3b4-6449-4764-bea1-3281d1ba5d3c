'use client';

import { useMemo, memo } from 'react';
import { useAutoScroll } from '@/hooks/use-auto-scroll';
import { Check, CheckCheck } from 'lucide-react';
import { cn } from '@/lib/utils';
import { UserAvatar } from './user-avatar';
import { AIMessage, AIMessageContent } from '@/components/ui/kibo-ui/ai/message';
import { AppleHelloEnglishEffect } from '@/components/ui/kibo-ui/apple-hello-effect';
import type { ChatAreaProps, FilteredMessage } from '../types/chat.types';

/**
 * Empty chat state component with friendly Apple Hello Effect
 * Shows different messages based on whether user is alone or waiting for others
 */
const EmptyChatState = memo(function EmptyChatState({ 
  selectedUserId, 
  activeUsers,
  currentUserId 
}: { 
  selectedUserId: string | null; 
  activeUsers: ChatAreaProps['activeUsers'];
  currentUserId: string;
}) {
  // Determine if user is alone (only counting non-current users)
  const otherActiveUsers = activeUsers.filter(user => user.email !== currentUserId);
  const isUserAlone = otherActiveUsers.length === 0;

  return (
    <div className="flex flex-col items-center justify-center py-8 sm:py-12 px-4 sm:px-6 text-center space-y-4 sm:space-y-6">
      {/* Apple Hello Effect Animation */}
      <div className="mb-3 sm:mb-4 scale-75 sm:scale-100">
        <AppleHelloEnglishEffect 
          speed={1.2}
          onAnimationComplete={() => {
            // Optional: Add any completion callback if needed
          }}
        />
      </div>
      
      {/* Dynamic messaging based on user context */}
      <div className="space-y-2 sm:space-y-3 max-w-xs sm:max-w-md">
        {selectedUserId ? (
          // When viewing specific user's messages
          <>
            <h3 className="text-base sm:text-lg font-semibold text-foreground">
              No messages yet
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground">
              No conversation history with this user. Start chatting to begin your conversation!
            </p>
          </>
        ) : isUserAlone ? (
          // When user is the only one in the chat
          <>
            <h3 className="text-base sm:text-lg font-semibold text-foreground">
              You're here first! 👋
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground">
              You're currently the only one in this chat room. Share the link with others to start chatting together!
            </p>
            <div className="text-xs sm:text-sm text-muted-foreground/80 mt-3 sm:mt-4 p-2 sm:p-3 bg-muted/50 rounded-lg">
              💡 <strong>Tip:</strong> Others will appear here automatically when they join the conversation.
            </div>
          </>
        ) : (
          // When others are present but no messages yet
          <>
            <h3 className="text-base sm:text-lg font-semibold text-foreground">
              Ready to chat! 🎉
            </h3>
            <p className="text-sm sm:text-base text-muted-foreground">
              {otherActiveUsers.length === 1 
                ? `You and 1 other person are here.` 
                : `You and ${otherActiveUsers.length} others are here.`
              } Start the conversation!
            </p>
            <div className="text-xs sm:text-sm text-muted-foreground/80">
              Everyone's waiting for the first message...
            </div>
          </>
        )}
      </div>
    </div>
  );
});

/**
 * Session start indicator component for fresh sessions
 */
const SessionStartIndicator = () => (
  <div className="flex justify-center py-4">
    <div className="px-4 py-2 bg-muted rounded-full text-sm text-muted-foreground">
      Session started - previous messages not shown
    </div>
  </div>
);

/**
 * Chat area component for displaying messages with kibo-ui AIMessage components
 * Adapted from ruixen-mono-chat with message filtering and automatic user/other alignment
 * Memoized for performance optimization
 */
export const ChatArea = memo(function ChatArea({
  messages,
  selectedUserId,
  currentUserId,
  activeUsers,
}: ChatAreaProps) {
  // Filter messages based on selected user
  const filteredMessages: FilteredMessage[] = useMemo(() => {
    console.log('🔍 Filtering messages:', {
      selectedUserId,
      currentUserId,
      totalMessages: messages.length,
      activeUsers: activeUsers.map(u => ({ id: u.id, email: u.email }))
    });

    if (!selectedUserId) {
      // Show all messages when no user is selected
      return messages.map(message => ({
        ...message,
        isVisible: true,
        isFromSelectedUser: false,
      }));
    }
    
    // Find the selected user to get their email
    const selectedUser = activeUsers.find(user => user.id === selectedUserId);
    const selectedUserEmail = selectedUser?.email;
    
    console.log('👤 Selected user details:', {
      selectedUserId,
      selectedUser,
      selectedUserEmail
    });
    
    if (!selectedUserEmail) {
      console.log('⚠️ Could not find selected user email, showing all messages');
      // If we can't find the selected user, show all messages
      return messages.map(message => ({
        ...message,
        isVisible: true,
        isFromSelectedUser: false,
      }));
    }
    
    // Show only messages from the selected user and current user (conversation view)
    const filtered = messages.map(message => ({
      ...message,
      isVisible: message.user.email === selectedUserEmail || message.user.email === currentUserId,
      isFromSelectedUser: message.user.email === selectedUserEmail,
    })).filter(message => message.isVisible);
    
    console.log('📝 Filtered messages:', {
      originalCount: messages.length,
      filteredCount: filtered.length,
      selectedUserEmail,
      currentUserId,
      messageEmails: messages.map(m => m.user.email)
    });
    
    return filtered;
  }, [messages, selectedUserId, currentUserId, activeUsers]);

  // Auto-scroll for chat messages
  const { scrollRef } = useAutoScroll({
    offset: 100,
    smooth: true,
    content: filteredMessages.length,
    enabled: true,
    scrollDelay: 50
  });

  return (
    <section className="flex-1 overflow-y-auto bg-background relative" ref={scrollRef}>
      <div className="min-h-full flex flex-col px-4 sm:px-6 py-4">
        {/* Session start indicator for fresh sessions */}
        <SessionStartIndicator />
        
        {filteredMessages.length === 0 ? (
          <div className="flex-1">
            <EmptyChatState 
              selectedUserId={selectedUserId}
              activeUsers={activeUsers}
              currentUserId={currentUserId}
            />
          </div>
        ) : (
          <div className="space-y-3 sm:space-y-4 pb-4">
            {filteredMessages.map((message, index) => {
              const prevMessage = index > 0 ? filteredMessages[index - 1] : null;
              const showAvatar = !prevMessage || prevMessage.user.email !== message.user.email;
              const isCurrentUser = message.user.email === currentUserId;
              
              // Determine message sender type for AIMessage alignment
              const messageFrom = isCurrentUser ? "user" : "other";

            return (
              <div
                key={message.id}
                className="animate-in fade-in slide-in-from-bottom-4 duration-300"
              >
                <AIMessage from={messageFrom}>
                  <AIMessageContent>
                    {/* User header (name and timestamp) - only show for grouped messages or first message */}
                    {showAvatar && (
                      <div className={cn(
                        "flex items-center gap-2 mb-2",
                        isCurrentUser ? "justify-end" : "justify-start"
                      )}>
                        <div className={cn(
                          "text-sm",
                          isCurrentUser ? "text-right" : "text-left"
                        )}>
                          <p className="font-semibold text-foreground">
                            {message.user.name || message.user.email}
                          </p>
                          <span className="text-xs text-muted-foreground">
                            {new Date(message.createdAt).toLocaleTimeString('en-US', {
                              hour: '2-digit',
                              minute: '2-digit',
                              hour12: true,
                            })}
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {/* Message content with responsive styling based on sender */}
                    <div className={cn(
                      "rounded-2xl px-4 py-3 max-w-[85%] sm:max-w-[80%] lg:max-w-[70%] xl:max-w-[65%]",
                      "shadow-sm border",
                      isCurrentUser 
                        ? "bg-primary text-primary-foreground ml-auto border-primary/20" 
                        : "bg-card text-card-foreground mr-auto border-border"
                    )}>
                      <p className="text-sm sm:text-base leading-relaxed whitespace-pre-wrap break-words">
                        {message.content}
                      </p>
                    </div>
                    
                    {/* Message footer with status - improved spacing */}
                    <div className={cn(
                      "flex items-center text-xs text-muted-foreground mt-2",
                      isCurrentUser ? "justify-end" : "justify-start"
                    )}>
                      {/* Message status indicators */}
                      <div className="flex items-center gap-1">
                        {message.status === "read" && (
                          <CheckCheck className="w-3 h-3 text-green-500" />
                        )}
                        {message.status === "delivered" && (
                          <Check className="w-3 h-3" />
                        )}
                        <span className="text-xs">
                          {new Date(message.createdAt).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true,
                          })}
                        </span>
                      </div>
                    </div>
                  </AIMessageContent>
                  
                  {/* Avatar - only show for grouped messages or first message */}
                  {/* Use existing UserAvatar component for DRY principle */}
                  {showAvatar && (
                    <UserAvatar
                      user={{
                        id: message.user.email,
                        email: message.user.email,
                        name: message.user.name,
                        avatar: message.user.avatar || undefined,
                        isOnline: true,
                        color: 'hsl(var(--chart-1))',
                        lastSeen: new Date(),
                      }}
                      size="md"
                      showOnlineStatus={false}
                      selected={false}
                    />
                  )}
                </AIMessage>
              </div>
            );
          })}
        </div>
      )}
      </div>
    </section>
  );
});