'use client';

import { UserAvatar } from './user-avatar';
import { cn } from '@/lib/utils';
import type { ActiveUsersSidebarProps } from '../types/chat.types';

/**
 * Active users sidebar component with responsive design
 * Adapted from ruixen-mono-chat participants list with realtime user management
 */
export function ActiveUsersSidebar({
  users,
  selectedUserId,
  onUserSelect,
  collapsed,
  isMobile = false,
  currentUserId,
}: ActiveUsersSidebarProps & { currentUserId?: string }) {
  return (
    <aside className={cn(
      'bg-muted border-r border-border overflow-y-auto flex flex-col',
      isMobile 
        ? 'w-full h-full' 
        : collapsed 
          ? 'w-16 p-2' 
          : 'w-full'
    )}>
      {!collapsed && (
        <div className={cn(
          'border-b border-border flex-shrink-0',
          isMobile ? 'p-4 pb-3' : 'p-4 pb-3'
        )}>
          <h3 className="text-sm font-medium text-foreground mb-2">
            Active Users
          </h3>
          <p className="text-xs text-muted-foreground">
            Connected Users: {users.length}
          </p>
        </div>
      )}

      <div className={cn(
        'flex-1 overflow-y-auto',
        isMobile ? 'p-4 pt-2' : collapsed ? 'p-2' : 'p-4 pt-2'
      )}>
        <div className="space-y-1">
          {users.length === 0 ? (
            !collapsed && (
              <p className="text-center text-sm text-muted-foreground py-8">
                No other users online
              </p>
            )
          ) : (
            users.map((user) => {
              const isSelected = selectedUserId === user.id;
              const isCurrentUser = currentUserId ? user.email === currentUserId : user.id === users[0]?.id;
              const canSelect = !isCurrentUser;

              return (
                <button
                  key={user.id}
                  onClick={() => canSelect && onUserSelect(user.id)}
                  disabled={!canSelect}
                  className={cn(
                    'flex items-center w-full rounded-lg transition-all duration-200',
                    collapsed 
                      ? 'p-2 justify-center' 
                      : 'gap-3 p-3',
                    canSelect && [
                      'hover:bg-accent hover:scale-[1.02] active:scale-[0.98]',
                      'focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2'
                    ],
                    isSelected && canSelect && 'bg-primary text-primary-foreground shadow-sm',
                    !canSelect && 'opacity-75 cursor-default',
                    isMobile && 'min-h-[44px]'
                  )}
                  title={collapsed ? `${user.name || user.email}${isCurrentUser ? ' (You)' : ''}` : undefined}
                >
                  <UserAvatar
                    user={user}
                    size={collapsed ? 'sm' : isMobile ? 'md' : 'md'}
                    showOnlineStatus={true}
                    selected={isSelected}
                  />
                  
                  {!collapsed && (
                    <div className="flex-1 text-left min-w-0">
                      <div className="font-medium truncate text-sm leading-tight">
                        {user.name || user.email}
                        {isCurrentUser && <span className="text-xs opacity-75 ml-1">(You)</span>}
                      </div>
                      <div className="flex items-center gap-1 mt-0.5">
                        <span className="text-xs text-muted-foreground">
                          {user.isOnline ? 'Online' : 'Offline'}
                        </span>
                      </div>
                    </div>
                  )}
                </button>
              );
            })
          )}
        </div>
      </div>

      {collapsed && users.length > 0 && (
        <div className="flex-shrink-0 p-2 border-t border-border">
          <div className="text-center">
            <div className="w-8 h-0.5 bg-border mx-auto mb-2"></div>
            <div className="text-xs text-muted-foreground font-medium">
              {users.length}
            </div>
          </div>
        </div>
      )}
    </aside>
  );
}