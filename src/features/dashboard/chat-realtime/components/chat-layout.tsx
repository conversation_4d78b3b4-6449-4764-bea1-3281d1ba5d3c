'use client';

import { useCallback, useState, useEffect, useMemo } from 'react';
import { useAuth } from '@/features/auth/hooks';
import { useUnifiedRealtime } from '@/features/realtime/hooks/use-unified-realtime';
import { useThrottleCallback } from '../hooks/use-throttle-callback';
import { ActiveUsersSidebar } from './active-users-sidebar';
import { ChatArea } from './chat-area';
import { MessageInput } from './message-input';
import {
  Cursor,
  CursorBody,
  CursorMessage,
  CursorName,
  CursorPointer,
} from '@/features/realtime/components/cursor-shad';
import { ClickVisualization } from '@/features/realtime/components/click-indicator';
import { Users, X } from 'lucide-react';
import type { ActiveUser } from '../types/chat.types';

/**
 * Loading component for chat layout - uses full available height
 */
function ChatLayoutLoading() {
  return (
    <div className="flex flex-col h-full">
      <div className="bg-card rounded-3xl shadow-lg flex flex-col border border-border relative flex-1 min-h-0">
        <div className="flex justify-between items-center border-b border-border pb-3 mb-6 px-6 pt-6">
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-foreground" />
            <div>
              <div className="h-6 w-32 bg-muted rounded animate-pulse" />
              <div className="h-4 w-48 bg-muted rounded animate-pulse mt-1" />
            </div>
          </div>
          <div className="h-6 w-20 bg-muted rounded animate-pulse" />
        </div>

        <div className="flex flex-1 overflow-hidden rounded-xl border border-border mx-6 mb-6">
          <div className="hidden md:block w-56 bg-muted border-r border-border p-4">
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center gap-3 p-3">
                  <div className="w-10 h-10 bg-background rounded-full animate-pulse" />
                  <div className="flex-1">
                    <div className="h-4 w-20 bg-background rounded animate-pulse" />
                    <div className="h-3 w-16 bg-background rounded animate-pulse mt-1" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex-1 flex flex-col bg-background">
            <div className="flex-1 flex items-center justify-center p-6">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Connecting to chat...</p>
              </div>
            </div>

            <div className="border-t border-border p-4">
              <div className="flex gap-2">
                <div className="flex-1 h-12 bg-muted rounded-full animate-pulse" />
                <div className="w-12 h-12 bg-muted rounded-full animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Main chat layout component that orchestrates the entire chat interface
 * Adapted from ruixen-mono-chat structure with realtime integration
 * Authentication is handled by dashboard layout, this component handles user-specific logic
 */
export function ChatLayout() {
  const { user } = useAuth();
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Use unified realtime hook for dashboard-chat room
  const roomName = 'dashboard-chat';
  const username = user?.fullName || user?.firstName || user?.primaryEmailAddress?.emailAddress || '';
  const userEmail = user?.primaryEmailAddress?.emailAddress || '';

  const {
    messages,
    cursors,
    clicks,
    isConnected,
    sendMessage,
    sendCursorPosition,
    sendClickEvent,
    userId,
  } = useUnifiedRealtime({
    roomName,
    username,
    enableChat: true,
    enableMouse: true,
    enableClicks: true,
  });

  // Enhanced connection error handling with retry mechanism
  const handleConnectionError = useCallback((error: string) => {
    setConnectionError(error);

    if (retryCount < 3) {
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
        setConnectionError('Retrying connection...');
        // The useUnifiedRealtime hook will automatically attempt to reconnect
      }, 2000 * Math.pow(2, retryCount)); // Exponential backoff: 2s, 4s, 8s
    } else {
      setConnectionError('Connection failed. Please refresh the page.');
    }
  }, [retryCount]);

  // Reset retry count on successful connection
  useEffect(() => {
    if (isConnected && retryCount > 0) {
      setRetryCount(0);
      setConnectionError(null);
    }
  }, [isConnected, retryCount]);

  // Reset user selection when leaving page (requirement 3.7)
  useEffect(() => {
    return () => {
      setSelectedUserId(null);
    };
  }, []);

  // Generate active users from cursors and current user (memoized for performance)
  const activeUsers: ActiveUser[] = useMemo(() => [
    // Current user
    {
      id: userId,
      email: user?.primaryEmailAddress?.emailAddress || '',
      name: user?.fullName || null,
      avatar: user?.imageUrl || null,
      isOnline: true,
      color: 'hsl(var(--chart-1))',
      lastSeen: new Date(),
    },
    // Other users from cursors
    ...Object.values(cursors).map((cursor) => ({
      id: cursor.user.id,
      email: cursor.user.email,
      name: cursor.user.name,
      avatar: null, // Other users don't have avatar URLs in cursor data
      isOnline: true,
      color: cursor.color,
      lastSeen: new Date(),
      cursor,
    })),
  ], [userId, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.imageUrl, cursors]);

  // Handle user selection
  const handleUserSelect = useCallback((userId: string | null) => {
    setSelectedUserId(prev => prev === userId ? null : userId);
    // Close mobile sidebar when user is selected
    setIsMobileSidebarOpen(false);
  }, []);

  // Handle mobile sidebar toggle
  const toggleMobileSidebar = useCallback(() => {
    setIsMobileSidebarOpen(prev => !prev);
  }, []);

  // Close mobile sidebar on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isMobileSidebarOpen) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isMobileSidebarOpen]);

  // Handle message sending with enhanced error handling
  const handleSendMessage = useCallback((content: string) => {
    sendMessage(content).catch((error) => {
      console.error('Failed to send message:', error);
      handleConnectionError('Failed to send message. Please try again.');
    });
    setConnectionError(null); // Clear any previous errors
  }, [sendMessage, handleConnectionError]);

  // Handle mouse movement for cursor tracking (throttled to 50ms for performance)
  const throttledSendCursor = useThrottleCallback(sendCursorPosition, 50);

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const x = e.clientX;
    const y = e.clientY;
    throttledSendCursor(x, y);
  }, [throttledSendCursor]);

  // Handle mouse clicks for click tracking
  const handleMouseClick = useCallback((e: React.MouseEvent) => {
    const x = e.clientX;
    const y = e.clientY;
    sendClickEvent(x, y);
  }, [sendClickEvent]);

  // Show loading state while user data is being fetched
  if (!user?.primaryEmailAddress?.emailAddress) {
    return <ChatLayoutLoading />;
  }

  // Main chat interface - uses available height with proper scrolling
  return (
    <div
      className="flex flex-col h-[calc(100vh-8rem)]"
      onMouseMove={handleMouseMove}
      onClick={handleMouseClick}
    >
      {/* Cursors - render outside main container for z-index positioning */}
      {Object.values(cursors).map((cursor) => {
        return (
          <Cursor
            key={cursor.user.id}
            className="fixed transition-transform ease-in-out z-50"
            style={{
              transitionDuration: '20ms',
              top: 0,
              left: 0,
              transform: `translate(${cursor.position.x}px, ${cursor.position.y}px)`,
              color: cursor.color,
            }}
          >
            <CursorPointer />
            <CursorBody>
              <CursorName>{cursor.user.email}</CursorName>
              {cursor.user.name && (
                <CursorMessage>{cursor.user.name}</CursorMessage>
              )}
            </CursorBody>
          </Cursor>
        );
      })}

      {/* Click visualization - render outside main container for z-index positioning */}
      <ClickVisualization clicks={clicks} />

      {/* Enhanced error display */}
      {connectionError && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg dark:bg-red-950 dark:border-red-800 mb-4 flex-shrink-0">
          <p className="text-red-700 dark:text-red-400 text-sm font-medium">
            {connectionError}
          </p>
          {retryCount > 0 && retryCount < 3 && (
            <p className="text-red-600 dark:text-red-500 text-xs mt-1">
              Retry attempt {retryCount}/3
            </p>
          )}
          {retryCount >= 3 && (
            <button
              onClick={() => window.location.reload()}
              className="mt-2 px-3 py-1 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-400 text-xs rounded transition"
            >
              Refresh Page
            </button>
          )}
        </div>
      )}

      {/* Chat Container - uses available height with proper scrolling */}
      <div className="bg-card rounded-3xl shadow-lg flex flex-col border border-border relative flex-1 overflow-hidden">
        {/* Mobile sidebar overlay */}
        {isMobileSidebarOpen && (
          <div className="absolute inset-0 z-50 md:hidden">
            <div
              className="absolute inset-0 bg-black/50 animate-in fade-in duration-300"
              onClick={toggleMobileSidebar}
            />
            <div className="absolute left-0 top-0 bottom-0 w-80 max-w-[85vw] bg-background border-r border-border animate-in slide-in-from-left duration-300 shadow-lg rounded-l-3xl">
              <div className="flex justify-end p-3 border-b border-border">
                <button
                  onClick={toggleMobileSidebar}
                  className="p-2 rounded-lg hover:bg-accent transition-colors"
                  aria-label="Close user list"
                >
                  <X className="w-5 h-5 text-foreground" />
                </button>
              </div>
              <ActiveUsersSidebar
                users={activeUsers}
                selectedUserId={selectedUserId}
                onUserSelect={handleUserSelect}
                collapsed={false}
                isMobile={true}
                currentUserId={userEmail}
              />
            </div>
          </div>
        )}

        {/* Header - original ruixen style with mobile menu button */}
        <header className="flex justify-between items-center border-b border-border pb-3 mb-6 px-6 pt-6">
          <div className="flex items-center gap-3">
            <button
              onClick={toggleMobileSidebar}
              className="md:hidden p-2 rounded-lg hover:bg-accent transition-colors"
              aria-label="Toggle user list"
            >
              <svg className="w-5 h-5 text-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <Users className="w-8 h-8 text-foreground" />
            <div>
              <h2 className="text-2xl font-semibold text-foreground">
                Dashboard Chat
              </h2>
              <p className="italic text-sm text-muted-foreground">
                Collaborate creatively, deliver clearly.
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className={`inline-flex items-center px-3 py-1.5 rounded-md text-sm font-medium transition-all ${isConnected
                ? 'bg-green-50 text-green-700 border border-green-200 dark:bg-green-950 dark:text-green-400 dark:border-green-800'
                : 'bg-red-50 text-red-700 border border-red-200 dark:bg-red-950 dark:text-red-400 dark:border-red-800'
              }`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </header>

        {/* Body - consistent spacing with other dashboard pages */}
        <main className="flex flex-1 rounded-xl border border-border mx-6 mb-6 overflow-hidden">
          {/* Participants List - hidden on mobile, shown on desktop */}
          <div className="hidden md:block w-56 lg:w-64 xl:w-72 flex-shrink-0">
            <ActiveUsersSidebar
              users={activeUsers}
              selectedUserId={selectedUserId}
              onUserSelect={handleUserSelect}
              collapsed={false}
              currentUserId={userEmail}
            />
          </div>

          {/* Messages - takes full width on mobile, shares with sidebar on desktop */}
          <section className="flex-1 bg-background flex flex-col min-w-0">
            <ChatArea
              messages={messages}
              selectedUserId={selectedUserId}
              currentUserId={userEmail || ''}
              activeUsers={activeUsers}
            />

            {/* Footer - message input spans full chat area */}
            <div className="border-t border-border p-4">
              <MessageInput
                onSendMessage={handleSendMessage}
                isConnected={isConnected}
                disabled={false}
              />
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}