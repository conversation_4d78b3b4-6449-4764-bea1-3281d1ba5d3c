'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
// Removed generateUserInitials import - using simple inline logic
import type { UserAvatarProps } from '../types/chat.types';

/**
 * Reusable user avatar component with online status indicator and responsive sizing
 * Follows DRY principle by providing consistent avatar display across the chat interface
 */
export function UserAvatar({
  user,
  size = 'md',
  showOnlineStatus = true,
  selected = false,
  onClick,
}: UserAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10 sm:w-12 sm:h-12',
    lg: 'w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16',
  };

  const indicatorSizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5 sm:w-3 sm:h-3',
    lg: 'w-3 h-3 sm:w-4 sm:h-4',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm sm:text-base',
    lg: 'text-base sm:text-lg',
  };

  // Generate initials using simple logic (KISS principle)
  const getInitials = () => {
    if (user.name) {
      const names = user.name.trim().split(' ');
      const first = names[0]?.[0] || '';
      const last = names.length > 1 ? names[names.length - 1]?.[0] || '' : '';
      return `${first}${last}`.toUpperCase().substring(0, 2);
    }
    if (user.email) {
      return user.email.substring(0, 2).toUpperCase();
    }
    return 'U';
  };
  
  const initials = getInitials();

  return (
    <div className="relative">
      <Avatar
        className={cn(
          'ring-1 transition-all duration-200 flex-shrink-0',
          sizeClasses[size],
          textSizeClasses[size],
          selected 
            ? 'ring-2 ring-primary shadow-lg' 
            : 'ring-border hover:ring-accent',
          onClick && 'cursor-pointer hover:scale-105 active:scale-95'
        )}
        onClick={onClick}
      >
        {user.avatar && <AvatarImage src={user.avatar} alt={user.name || user.email} />}
        <AvatarFallback className="font-semibold">{initials}</AvatarFallback>
      </Avatar>
      
      {/* Online status indicator - responsive sizing */}
      {showOnlineStatus && (
        <span
          className={cn(
            'absolute -bottom-0.5 -right-0.5 rounded-full ring-2 ring-background transition-all duration-200',
            indicatorSizeClasses[size],
            user.isOnline
              ? 'bg-green-500 shadow-green-500/25 shadow-lg'
              : 'bg-muted-foreground'
          )}
          title={user.isOnline ? 'Online' : 'Offline'}
        />
      )}
    </div>
  );
}