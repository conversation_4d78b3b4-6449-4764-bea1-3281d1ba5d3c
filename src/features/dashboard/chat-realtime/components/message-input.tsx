'use client';

import { useState, useCallback } from 'react';
import { Send } from 'lucide-react';
import { cn } from '@/lib/utils';
import type { MessageInputProps } from '../types/chat.types';

/**
 * Message input component for sending chat messages with responsive design
 * Optimized for mobile-first approach with proper touch targets
 */
export function MessageInput({
  onSendMessage,
  isConnected,
  disabled = false,
}: MessageInputProps) {
  const [content, setContent] = useState('');

  const canSend = content.trim().length > 0 && isConnected && !disabled;

  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canSend) return;

    onSendMessage(content.trim());
    setContent('');
  }, [content, canSend, onSendMessage]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setContent(value);
  }, []);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  }, [handleSubmit]);

  return (
    <div className="border-t border-border bg-background">
      <div className="flex items-center gap-2 sm:gap-3 p-3 sm:p-4">
        <form onSubmit={handleSubmit} className="flex-1 flex gap-2">
          <input
            type="text"
            value={content}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder="Type a message..."
            disabled={disabled || !isConnected}
            className={cn(
              "flex-1 px-4 sm:px-5 py-3 rounded-full border border-border min-h-[44px]",
              "bg-background text-foreground placeholder-muted-foreground",
              "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-all",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            style={{
              fontSize: '16px', // Prevents zoom on iOS Safari
            }}
            maxLength={1000}
          />

          {canSend && (
            <button
              type="submit"
              aria-label="Send message"
              className="flex-shrink-0 p-3 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all min-h-[44px] min-w-[44px] flex items-center justify-center animate-in fade-in slide-in-from-right-4 duration-300 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
              disabled={!canSend}
            >
              <Send className="w-5 h-5" />
            </button>
          )}
        </form>

        {!isConnected && (
          <div className="flex-shrink-0 text-xs text-destructive bg-destructive/10 px-2 py-1 rounded-md">
            Offline
          </div>
        )}
      </div>
    </div>
  );
}