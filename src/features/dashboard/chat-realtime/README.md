# Dashboard Chat Realtime Feature

A comprehensive realtime chat interface for the dashboard that combines existing realtime infrastructure with a user-centric chat experience.

## Features

- ✅ **Single Global Room**: All users connect to "dashboard-chat" room
- ✅ **User Selection**: Click users in sidebar to filter messages
- ✅ **Real-time Features**: Chat + mouse tracking using unified hook
- ✅ **Mobile Responsive**: Collapsed sidebar with avatar-only view
- ✅ **Message System**: Status indicators and real-time updates
- ✅ **Design System**: Full compliance with existing theme system
- ✅ **Fresh Sessions**: No message persistence for FOMO experience
- ✅ **Performance Optimized**: Throttled cursor updates, memoized components

## Usage

```tsx
import { ChatLayout } from '@/features/dashboard/chat-realtime';

export default function ChatPage() {
  return <ChatLayout />;
}
```

## Components

- **ChatLayout**: Main orchestrator component
- **ActiveUsersSidebar**: User list with selection
- **ChatArea**: Message display with filtering
- **MessageInput**: Message composition
- **UserAvatar**: Reusable avatar with status

## Architecture

- Uses `useUnifiedRealtime` hook for single-channel pattern
- Integrates with existing realtime infrastructure
- Follows DRY principles with reusable components
- Mobile-first responsive design
- Design system compliant styling

## Integration

The feature is integrated at `/dashboard/chat` and leverages:

- Dashboard layout for authentication
- Existing realtime hooks and components
- Design system colors and styling
- i18n translation system
- Auto-scroll functionality

## Performance

- Cursor updates throttled to 50ms
- Components memoized to prevent unnecessary re-renders
- Efficient state management with useMemo
- Proper cleanup of event listeners and connections