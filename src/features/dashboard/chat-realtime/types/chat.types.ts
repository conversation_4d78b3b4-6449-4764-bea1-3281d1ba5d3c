/**
 * TypeScript interfaces for the realtime chat dashboard feature
 * Compatible with the unified realtime hook interface
 */

export interface ChatMessage {
  id: string;
  content: string;
  user: {
    email: string;
    name?: string;
    avatar?: string | null;
  };
  createdAt: string;
  // Optional dashboard-specific properties
  userId?: string;
  roomName?: string;
  status?: "sent" | "delivered" | "read";
}

export interface ActiveUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string | null;
  isOnline: boolean;
  color: string;
  lastSeen: Date;
  cursor?: CursorPosition;
}

export interface CursorPosition {
  position: { x: number; y: number };
  user: {
    id: string;
    email: string;
    name?: string;
  };
  color: string;
  timestamp: number;
}

export interface ChatState {
  // Connection
  isConnected: boolean;
  connectionError?: string;
  
  // Users
  activeUsers: Record<string, ActiveUser>;
  selectedUserId: string | null;
  
  // Messages
  messages: ChatMessage[];
  filteredMessages: ChatMessage[];
  
  // UI State
  isMobile: boolean;
  sidebarCollapsed: boolean;
  isTyping: boolean;
}

// Component Props Interfaces
export interface ChatLayoutProps {
  children?: React.ReactNode;
}

export interface ActiveUsersSidebarProps {
  users: ActiveUser[];
  selectedUserId: string | null;
  onUserSelect: (userId: string | null) => void;
  collapsed: boolean;
  isMobile?: boolean; // For mobile overlay styling
}

export interface ChatAreaProps {
  messages: ChatMessage[];
  selectedUserId: string | null;
  currentUserId: string; // Current user's ID for message alignment
  activeUsers: ActiveUser[]; // For detecting if user is alone
}

export interface MessageInputProps {
  onSendMessage: (content: string) => void;
  isConnected: boolean;
  disabled?: boolean;
}

export interface UserAvatarProps {
  user: ActiveUser;
  size?: 'sm' | 'md' | 'lg';
  showOnlineStatus?: boolean;
  selected?: boolean;
  onClick?: () => void;
}

export interface FilteredMessage extends ChatMessage {
  isVisible: boolean;
  isFromSelectedUser: boolean;
}