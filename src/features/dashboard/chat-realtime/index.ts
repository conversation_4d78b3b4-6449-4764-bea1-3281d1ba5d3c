/**
 * Feature exports for dashboard chat-realtime
 */

// Types
export type {
  ChatMessage,
  ActiveUser,
  CursorPosition,
  ChatState,
  ChatLayoutProps,
  ActiveUsersSidebarProps,
  ChatAreaProps,
  MessageInputProps,
  UserAvatarProps,
  FilteredMessage,
} from './types/chat.types';

// Components
export { ChatLayout } from './components/chat-layout';
export { ActiveUsersSidebar } from './components/active-users-sidebar';
export { ChatArea } from './components/chat-area';
export { MessageInput } from './components/message-input';
export { UserAvatar } from './components/user-avatar';

// Hooks
export { useThrottleCallback } from './hooks/use-throttle-callback';