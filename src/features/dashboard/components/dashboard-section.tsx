/**
 * @fileoverview Reusable dashboard section component
 * Provides consistent layout and styling for dashboard pages
 * Follows DRY principle by centralizing common layout patterns
 */

'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface DashboardSectionProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  headerActions?: React.ReactNode;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  background?: 'default' | 'card' | 'muted';
  rounded?: boolean;
  shadow?: boolean;
  border?: boolean;
}

/**
 * Reusable dashboard section component that provides consistent layout
 * and styling across all dashboard pages
 */
export function DashboardSection({
  children,
  className,
  title,
  description,
  headerActions,
  padding = 'md',
  background = 'default',
  rounded = true,
  shadow = false,
  border = false,
}: DashboardSectionProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  const backgroundClasses = {
    default: 'bg-background',
    card: 'bg-card',
    muted: 'bg-muted',
  };

  return (
    <section
      className={cn(
        'w-full',
        backgroundClasses[background],
        paddingClasses[padding],
        rounded && 'rounded-lg',
        shadow && 'shadow-sm',
        border && 'border border-border',
        className
      )}
    >
      {(title || description || headerActions) && (
        <header className="mb-6">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              {title && (
                <h2 className="text-2xl font-semibold text-foreground">
                  {title}
                </h2>
              )}
              {description && (
                <p className="text-muted-foreground">
                  {description}
                </p>
              )}
            </div>
            {headerActions && (
              <div className="flex items-center gap-2">
                {headerActions}
              </div>
            )}
          </div>
        </header>
      )}
      
      <div className="w-full">
        {children}
      </div>
    </section>
  );
}

/**
 * Specialized dashboard content wrapper for main page content
 * Provides consistent spacing and responsive behavior
 */
export function DashboardContent({
  children,
  className,
  maxWidth = '7xl',
}: {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl';
}) {
  const maxWidthClasses = {
    none: '',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    '4xl': 'max-w-4xl',
    '5xl': 'max-w-5xl',
    '6xl': 'max-w-6xl',
    '7xl': 'max-w-7xl',
  };

  return (
    <div
      className={cn(
        'w-full mx-auto space-y-6',
        maxWidth !== 'none' && maxWidthClasses[maxWidth],
        className
      )}
    >
      {children}
    </div>
  );
}

/**
 * Dashboard grid component for consistent grid layouts
 */
export function DashboardGrid({
  children,
  className,
  cols = 1,
  gap = 6,
}: {
  children: React.ReactNode;
  className?: string;
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 2 | 4 | 6 | 8;
}) {
  const colsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
    12: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6',
  };

  const gapClasses = {
    2: 'gap-2',
    4: 'gap-4',
    6: 'gap-6',
    8: 'gap-8',
  };

  return (
    <div
      className={cn(
        'grid',
        colsClasses[cols],
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
}