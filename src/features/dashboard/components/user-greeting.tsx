'use client'

import { useAuth } from '@/features/auth/hooks/use-auth-simple'

/**
 * Example component showing how to greet user with first name
 * Clerk's user object provides: firstName, lastName, fullName
 */
export function UserGreeting() {
  const { user } = useAuth()
  
  if (!user) return null

  // Clerk provides these fields automatically:
  // user.firstName - First name from account page
  // user.lastName - Last name from account page  
  // user.fullName - Combined first + last name
  // user.primaryEmailAddress?.emailAddress - Email
  
  const getGreeting = () => {
    if (user.firstName) {
      return `Hello, ${user.firstName}!`
    }
    if (user.fullName) {
      return `Hello, ${user.fullName}!`
    }
    return 'Hello!'
  }

  return (
    <div className="text-lg font-medium">
      {getGreeting()}
    </div>
  )
}

/**
 * Utility function to get user's first name for greetings
 */
export function getFirstName(user: any): string {
  if (!user) return 'User'
  
  // Priority order: firstName -> extract from fullName -> fallback
  if (user.firstName) {
    return user.firstName
  }
  
  if (user.fullName) {
    // Extract first word from full name
    return user.fullName.split(' ')[0]
  }
  
  return 'User'
}

/**
 * Usage examples for greeting user
 */
export function GreetingExamples() {
  const { user } = useAuth()
  
  if (!user) return <div>Please sign in</div>

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Greeting Examples:</h2>
      
      {/* Method 1: Direct access */}
      <p>Method 1: Hello, {user.firstName || 'there'}!</p>
      
      {/* Method 2: Using utility function */}
      <p>Method 2: Welcome back, {getFirstName(user)}!</p>
      
      {/* Method 3: Full name fallback */}
      <p>Method 3: Hi {user.firstName || user.fullName || 'User'}!</p>
      
      <div className="mt-4 p-4 bg-gray-100 rounded">
        <h3 className="font-medium">Available Clerk user fields:</h3>
        <ul className="mt-2 space-y-1 text-sm">
          <li><strong>firstName:</strong> {user.firstName || 'Not set'}</li>
          <li><strong>lastName:</strong> {user.lastName || 'Not set'}</li>
          <li><strong>fullName:</strong> {user.fullName || 'Not set'}</li>
          <li><strong>email:</strong> {user.primaryEmailAddress?.emailAddress || 'Not set'}</li>
        </ul>
      </div>
    </div>
  )
}
