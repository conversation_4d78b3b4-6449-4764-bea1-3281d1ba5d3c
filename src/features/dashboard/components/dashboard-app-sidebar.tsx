"use client"

import * as React from "react"
import {
  BarChartIcon,
  MessageCircleIcon,
  FlaskConicalIcon,
  LayoutDashboardIcon,
  SettingsIcon,
  HelpCircleIcon,
} from "lucide-react"

import { NavMain } from '@/components/nav/nav-main'
import { NavSecondary } from '@/components/nav/nav-secondary'
import { DashboardUserMenu } from './dashboard-user-menu'
import { useI18n } from '@/hooks/use-i18n'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from '@/components/ui/sidebar'

export function DashboardAppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const { t } = useI18n();

  const data = {
    navMain: [
      {
        title: t('dashboard:sidebar.main.dashboard'),
        url: "/dashboard",
        icon: LayoutDashboardIcon,
        isActive: true,
      },
      {
        title: t('dashboard:sidebar.main.analytics'),
        url: "/dashboard/analytics",
        icon: BarChartIcon,
      },
    ],
    navAI: [
      {
        title: t('dashboard:sidebar.ai.createRecipe.title'),
        url: "/dashboard/create-recipe/health-concern",
        icon: FlaskConicalIcon,
        items: [
          {
            title: t('dashboard:sidebar.ai.createRecipe.new'),
            url: "/dashboard/create-recipe/health-concern",
          },
          {
            title: t('dashboard:sidebar.ai.createRecipe.myRecipes'),
            url: "/dashboard/recipes",
          },
        ],
      },
      {
        title: t('dashboard:sidebar.ai.chat'),
        url: "/dashboard/chat",
        icon: MessageCircleIcon,
      },
    ],
    navClouds: [],
    navSecondary: [
      {
        title: t('dashboard:sidebar.secondary.settings'),
        url: "/dashboard/settings",
        icon: SettingsIcon,
      },
      {
        title: t('dashboard:sidebar.secondary.help'),
        url: "/help",
        icon: HelpCircleIcon,
      },
    ],
    documents: [],
  }

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <a href="/dashboard">
                <FlaskConicalIcon className="h-5 w-5" />
                <span className="text-base font-semibold">
                  {t('dashboard:sidebar.brand.title')}
                </span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavMain items={data.navAI} title={t('dashboard:sidebar.ai.title')} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <DashboardUserMenu />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
