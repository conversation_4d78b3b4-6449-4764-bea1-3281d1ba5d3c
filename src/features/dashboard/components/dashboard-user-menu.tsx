
// src/features/dashboard/components/dashboard-user-menu.tsx
"use client";

import React, { useState, useEffect } from "react";
import { Settings, Headphones, UserCircle2, Globe } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/features/auth/hooks";
import { UserButton, ClerkLoading, ClerkLoaded, useUser } from '@clerk/nextjs';
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Save } from 'lucide-react';
import { type SupportedLanguage } from '@/features/auth/services/language.service';
import { UserButtonWithCustomPages } from '@/features/auth/components/user-button/user-button-with-custom-pages';
import { useI18n } from '@/hooks/use-i18n';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

export function DashboardUserMenu() {
  const { user, isLoading } = useAuth();
  const [isMounted, setIsMounted] = useState(false);

  // Fix hydration mismatch by ensuring client-side mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);
  
  // Create UserButton appearance configuration for sidebar
  const sidebarUserButtonAppearance = {
    ...getClerkAppearance(),
    elements: {
      ...getClerkAppearance().elements,
      userButtonAvatarBox: {
        width: '2rem',
        height: '2rem',
        borderRadius: 'var(--radius)',
        filter: 'grayscale(1)' // Match existing sidebar styling
      },
      userButtonPopoverCard: {
        backgroundColor: 'hsl(var(--sidebar-background))',
        borderColor: 'hsl(var(--sidebar-border))',
        boxShadow: 'var(--shadow-lg)',
        zIndex: 50
      },
      userButtonPopoverActionButton: {
        borderRadius: 'var(--radius)'
      }
    }
  };

  // Loading state or not mounted yet (prevent hydration mismatch)
  if (isLoading || !isMounted) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg">
            <ClerkLoading>
              <Skeleton className="h-8 w-8 rounded-lg" />
              <div className="grid flex-1 text-left text-sm leading-tight">
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-3 w-32" />
              </div>
            </ClerkLoading>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  // Not authenticated state
  if (!user) {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg">
            <Avatar className="h-8 w-8 rounded-lg grayscale">
              <AvatarFallback className="rounded-lg bg-muted text-muted-foreground">
                <UserCircle2 size={18} />
              </AvatarFallback>
            </Avatar>
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-medium text-muted-foreground">Not Logged In</span>
            </div>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    );
  }

  // Authenticated state with UserButton
  return (
    <>
      <SidebarMenu>
        <SidebarMenuItem>
          <ClerkLoaded>
            <div className="flex items-center gap-2 px-2 py-1.5">
              <UserButtonWithCustomPages
                appearance={sidebarUserButtonAppearance}
                context="dashboard"
              />
              <div className="grid flex-1 text-left text-sm leading-tight ml-2">
                <span className="truncate font-medium">
                  {user.fullName || user.firstName || user.primaryEmailAddress?.emailAddress?.split('@')[0] || 'User'}
                </span>
                <span className="truncate text-xs text-muted-foreground">
                  {user.primaryEmailAddress?.emailAddress || 'No email available'}
                </span>
              </div>
            </div>
          </ClerkLoaded>
        </SidebarMenuItem>
      </SidebarMenu>
    </>
  );
}

/**
 * Custom preferences page component - matches native Clerk styling
 */
export function PreferencesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Preferences</h2>
        <p className="text-sm text-muted-foreground">Manage your personal preferences and settings.</p>
      </div>

      <CustomMetadataFormNative />
    </div>
  )
}

/**
 * Custom language page component - matches native Clerk styling
 */
export function LanguagePage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Language & Region</h2>
        <p className="text-sm text-muted-foreground">Set your preferred language and regional settings.</p>
      </div>

      <LanguageSettingsFormNative />
    </div>
  )
}

/**
 * Native-styled metadata form without card wrapper
 */
export function CustomMetadataFormNative() {
  const { user } = useUser()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  const [formData, setFormData] = useState({
    bio: (user?.unsafeMetadata?.['bio'] as string) || '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setIsUpdating(true)
    try {
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          bio: formData.bio,
          updatedAt: new Date().toISOString(),
        },
      });

      toast({
        title: 'Preferences updated!',
        description: 'Your preferences have been saved successfully.',
      });
    } catch (error) {
      console.error('❌ Update error:', error)
      toast({
        title: 'Update failed',
        description: 'There was an error updating your preferences.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-4">Personal Information</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-sm font-medium">Bio</Label>
            <Textarea
              id="bio"
              placeholder="Tell us about yourself..."
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
              className="min-h-[100px] resize-none"
            />
            <div className="text-xs text-muted-foreground">
              {formData.bio.length}/180 characters
            </div>
          </div>

          <Button type="submit" disabled={isUpdating} className="w-full">
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </>
            )}
          </Button>
        </form>
      </div>
    </div>
  )
}

/**
 * Native-styled language form using unified language service
 */
export function LanguageSettingsFormNative() {
  const { user } = useUser()
  const { toast } = useToast()
  const { t } = useI18n()
  const [isUpdating, setIsUpdating] = useState(false)
  const [language, setLanguage] = useState(
    (user?.unsafeMetadata?.['language'] as string) || 'en'
  )

  const handleLanguageChange = async (newLanguage: string) => {
    if (!user) return

    setIsUpdating(true)
    try {
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          language: newLanguage,
          updatedAt: new Date().toISOString(),
        },
      });

      // Use the cookie action from the auth features
      const { setLanguageCookie } = await import('@/features/auth/actions/cookie.actions');
      const cookieResult = await setLanguageCookie(newLanguage as SupportedLanguage);
      if (!cookieResult.success) {
        console.warn('Failed to set language cookie:', cookieResult.error);
      }

      setLanguage(newLanguage)
      toast({
        title: t('dashboard:userProfile.messages.language.updated'),
        description: t('dashboard:userProfile.messages.language.changed', undefined, { language: getLanguageName(newLanguage) }),
      });
    } catch (error) {
      console.error('❌ Language update error:', error)
      toast({
        title: t('dashboard:userProfile.messages.language.updateFailed'),
        description: t('dashboard:userProfile.messages.language.updateError'),
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const getLanguageName = (code: string) => {
    return t(`dashboard:userProfile.forms.language.languages.${code}`, code)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('dashboard:userProfile.forms.language.title')}</h3>

        <div className="space-y-3">
          <Label className="text-sm font-medium">{t('dashboard:userProfile.forms.language.chooseLabel')}</Label>

          {/* Use original button-based approach that was working */}
          <div className="space-y-2">
            {[
              { code: 'en' },
              { code: 'pt' },
              { code: 'es' },
            ].map((lang) => (
              <Button
                key={lang.code}
                variant={language === lang.code ? 'default' : 'outline'}
                onClick={() => handleLanguageChange(lang.code as SupportedLanguage)}
                disabled={isUpdating}
                className="w-full justify-start"
              >
                {isUpdating && language === lang.code && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {t(`dashboard:userProfile.forms.language.languages.${lang.code}`)}
              </Button>
            ))}
          </div>

          {isUpdating && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              {t('dashboard:userProfile.forms.language.updating')}
            </div>
          )}

          <div className="text-sm text-muted-foreground">
            {t('dashboard:userProfile.forms.language.currentSelection', undefined, { language: getLanguageName(language) })}
          </div>
        </div>
      </div>
    </div>
  )
}
