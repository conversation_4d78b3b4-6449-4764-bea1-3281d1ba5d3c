'use client';

import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@/features/auth/hooks';
import { createClient } from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

const EVENT_MESSAGE_TYPE = 'chat_message';

export interface ChatMessage {
  id: string;
  content: string;
  user: {
    email: string;
    name?: string;
  };
  createdAt: string;
}

interface UseRealtimeChatProps {
  roomName: string;
  username: string;
}

export function useRealtimeChat({ roomName, username }: UseRealtimeChatProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);

  const supabase = createClient();

  useEffect(() => {
    if (!user?.primaryEmailAddress?.emailAddress) return;

    // Create single channel instance following official Supabase pattern
    const realtimeChannel = supabase.channel(roomName);

    // Set up event listeners before subscribing
    realtimeChannel
      .on('broadcast', { event: EVENT_MESSAGE_TYPE }, (payload) => {
        setMessages((current) => [...current, payload['payload'] as ChatMessage]);
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
          setChannel(realtimeChannel);
        } else if (status === 'CHANNEL_ERROR') {
          setIsConnected(false);
          console.error('Chat channel subscription error');
        } else if (status === 'TIMED_OUT') {
          setIsConnected(false);
          console.error('Chat channel subscription timed out');
        }
      });

    return () => {
      // Official cleanup method
      supabase.removeChannel(realtimeChannel);
      setChannel(null);
      setIsConnected(false);
    };
  }, [user?.primaryEmailAddress?.emailAddress, supabase, roomName, username]);

  const sendMessage = useCallback(
    async (content: string) => {
      const userEmail = user?.primaryEmailAddress?.emailAddress;
      if (!userEmail || !isConnected || !channel) return;

      const chatMessage: ChatMessage = {
        id: crypto.randomUUID(),
        content,
        user: {
          email: userEmail,
          name: user.fullName || user.firstName,
        },
        createdAt: new Date().toISOString(),
      };

      // Add to local state immediately for better UX
      setMessages((current) => [...current, chatMessage]);

      // Send using existing subscribed channel
      await channel.send({
        type: 'broadcast',
        event: EVENT_MESSAGE_TYPE,
        payload: chatMessage,
      });
    },
    [isConnected, channel, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName]
  );

  return {
    messages,
    isConnected,
    sendMessage,
  };
}