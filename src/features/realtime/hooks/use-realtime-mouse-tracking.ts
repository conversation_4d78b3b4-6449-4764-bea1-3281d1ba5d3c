'use client';

import { useCallback, useEffect, useRef, useState } from 'react';
import { useAuth } from '@/features/auth/hooks';
import { createClient } from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface CursorPosition {
  position: { x: number; y: number };
  user: {
    id: string;
    email: string;
    name?: string;
  };
  color: string;
  timestamp: number;
}

export interface ClickEvent {
  position: { x: number; y: number };
  user: {
    id: string;
    email: string;
    name?: string;
  };
  color: string;
  timestamp: number;
}

const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

const generateUserColor = (userId: string): string => {
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = ((hash << 5) - hash) + userId.charCodeAt(i);
  }
  return CHART_COLORS[Math.abs(hash) % CHART_COLORS.length] || CHART_COLORS[0];
};

// Throttle hook for performance optimization
const useThrottleCallback = <Params extends unknown[], Return>(
  callback: (...args: Params) => Return,
  delay: number
) => {
  const lastCall = useRef(0);
  const timeout = useRef<NodeJS.Timeout | null>(null);

  return useCallback(
    (...args: Params) => {
      const now = Date.now();
      const remainingTime = delay - (now - lastCall.current);

      if (remainingTime <= 0) {
        if (timeout.current) {
          clearTimeout(timeout.current);
          timeout.current = null;
        }
        lastCall.current = now;
        callback(...args);
      } else if (!timeout.current) {
        timeout.current = setTimeout(() => {
          lastCall.current = Date.now();
          timeout.current = null;
          callback(...args);
        }, remainingTime);
      }
    },
    [callback, delay]
  );
};

interface UseRealtimeMouseTrackingProps {
  roomName: string;
  throttleMs?: number;
  includeClicks?: boolean;
  maxClickHistory?: number;
}

export function useRealtimeMouseTracking({
  roomName,
  throttleMs = 50,
  includeClicks = false,
  maxClickHistory = 10,
}: UseRealtimeMouseTrackingProps) {
  const { user } = useAuth();
  const [cursors, setCursors] = useState<Record<string, CursorPosition>>({});
  const [clicks, setClicks] = useState<ClickEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [userId] = useState(crypto.randomUUID());
  const [color] = useState(() => generateUserColor(userId));

  const supabase = createClient();

  useEffect(() => {
    if (!user?.primaryEmailAddress?.emailAddress) return;

    // Create single channel instance with custom presence key (user email for uniqueness)
    const realtimeChannel = supabase.channel(roomName, {
      config: {
        presence: {
          key: user.primaryEmailAddress.emailAddress, // Use email as unique key to prevent duplicates
        },
      },
    });

    // Set up presence listeners for cursor tracking (auto-cleanup on disconnect)
    realtimeChannel
      .on('presence', { event: 'sync' }, () => {
        const newState = realtimeChannel.presenceState();
        const newCursors: Record<string, CursorPosition> = {};
        
        Object.entries(newState).forEach(([key, presences]) => {
          if (key !== user.primaryEmailAddress.emailAddress && presences.length > 0) {
            const presence = presences[0] as CursorPosition;
            newCursors[presence.user.id] = presence;
          }
        });
        
        setCursors(newCursors);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', key);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', key);
        // Cursors are automatically removed by sync event
      });

    // Add click listener if enabled (still use broadcast for ephemeral events)
    if (includeClicks) {
      realtimeChannel.on('broadcast', { event: 'click_event' }, (payload) => {
        const clickData = payload['payload'] as ClickEvent;
        if (clickData.user.id !== userId) {
          setClicks((prev) => [...prev, clickData].slice(-maxClickHistory));
        }
      });
    }

    realtimeChannel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        setIsConnected(true);
        setChannel(realtimeChannel);
      } else if (status === 'CHANNEL_ERROR') {
        setIsConnected(false);
        console.error('Mouse tracking channel subscription error');
      } else if (status === 'TIMED_OUT') {
        setIsConnected(false);
        console.error('Mouse tracking channel subscription timed out');
      }
    });

    return () => {
      // Untrack presence before removing channel
      if (realtimeChannel) {
        realtimeChannel.untrack();
      }
      supabase.removeChannel(realtimeChannel);
      setChannel(null);
      setIsConnected(false);
    };
  }, [user?.primaryEmailAddress?.emailAddress, userId, supabase, roomName, includeClicks, maxClickHistory]);

  const sendCursorPosition = useCallback(
    (x: number, y: number) => {
      if (!user?.primaryEmailAddress?.emailAddress || !isConnected || !channel) return;

      const cursorData: CursorPosition = {
        position: { x, y },
        user: {
          id: userId,
          email: user.primaryEmailAddress.emailAddress,
          name: user.fullName || user.firstName,
        },
        color,
        timestamp: Date.now(),
      };

      // Use presence.track() instead of broadcast for cursor position
      // This automatically handles cleanup when user disconnects/refreshes
      channel.track(cursorData);
    },
    [isConnected, channel, userId, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName, color]
  );

  const sendClickEvent = useCallback(
    (x: number, y: number) => {
      if (!user?.primaryEmailAddress?.emailAddress || !isConnected || !channel || !includeClicks) return;

      const clickData: ClickEvent = {
        position: { x, y },
        user: {
          id: userId,
          email: user.primaryEmailAddress.emailAddress,
          name: user.fullName || user.firstName,
        },
        color,
        timestamp: Date.now(),
      };

      channel.send({
        type: 'broadcast',
        event: 'click_event',
        payload: clickData,
      });
    },
    [isConnected, channel, userId, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName, color, includeClicks]
  );

  // Throttled cursor position for performance
  const throttledSendCursor = useThrottleCallback(sendCursorPosition, throttleMs);

  return {
    cursors,
    clicks,
    isConnected,
    sendCursorPosition: throttledSendCursor,
    sendClickEvent,
    userId,
    color,
  };
}