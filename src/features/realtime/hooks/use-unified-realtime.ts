'use client';

import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '@/features/auth/hooks';
import { createClient } from '@/lib/supabase/client';
import { RealtimeChannel } from '@supabase/supabase-js';

export interface ChatMessage {
  id: string;
  content: string;
  user: {
    email: string;
    name?: string;
  };
  createdAt: string;
}

export interface CursorPosition {
  position: { x: number; y: number };
  user: {
    id: string;
    email: string;
    name?: string;
  };
  color: string;
  timestamp: number;
}

export interface ClickEvent {
  position: { x: number; y: number };
  user: {
    id: string;
    email: string;
    name?: string;
  };
  color: string;
  timestamp: number;
  id: string;
}

const CHART_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

const generateUserColor = (userId: string): string => {
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = ((hash << 5) - hash) + userId.charCodeAt(i);
  }
  return CHART_COLORS[Math.abs(hash) % CHART_COLORS.length] || CHART_COLORS[0];
};

interface UseUnifiedRealtimeProps {
  roomName: string;
  username: string;
  enableChat?: boolean;
  enableMouse?: boolean;
  enableClicks?: boolean;
}

export function useUnifiedRealtime({
  roomName,
  username,
  enableChat = true,
  enableMouse = true,
  enableClicks = true,
}: UseUnifiedRealtimeProps) {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [cursors, setCursors] = useState<Record<string, CursorPosition>>({});
  const [clicks, setClicks] = useState<Record<string, ClickEvent>>({});
  const [isConnected, setIsConnected] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [userId] = useState(crypto.randomUUID());
  const [color] = useState(() => generateUserColor(userId));

  const supabase = createClient();

  useEffect(() => {
    if (!user?.primaryEmailAddress?.emailAddress) return;

    // Create single channel instance - this is the key to avoiding subscription conflicts
    const realtimeChannel = supabase.channel(roomName);

    // Set up event listeners before subscribing
    let channelWithListeners = realtimeChannel;

    if (enableChat) {
      channelWithListeners = channelWithListeners.on('broadcast', { event: 'chat_message' }, (payload) => {
        setMessages((current) => [...current, payload['payload'] as ChatMessage]);
      });
    }

    if (enableMouse) {
      channelWithListeners = channelWithListeners.on('broadcast', { event: 'cursor_move' }, (payload) => {
        const cursorData = payload['payload'] as CursorPosition;
        if (cursorData.user.id !== userId) {
          setCursors((prev) => ({
            ...prev,
            [cursorData.user.id]: cursorData,
          }));
        }
      });
    }

    if (enableClicks) {
      channelWithListeners = channelWithListeners.on('broadcast', { event: 'mouse_click' }, (payload) => {
        const clickData = payload['payload'] as ClickEvent;
        if (clickData.user.id !== userId) {
          setClicks((prev) => ({
            ...prev,
            [clickData.user.id]: clickData,
          }));
        }
      });
    }

    channelWithListeners.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        setIsConnected(true);
        setChannel(realtimeChannel);
      } else if (status === 'CHANNEL_ERROR') {
        setIsConnected(false);
        console.error('Channel subscription error');
      } else if (status === 'TIMED_OUT') {
        setIsConnected(false);
        console.error('Channel subscription timed out');
      }
    });

    return () => {
      // Official cleanup method
      supabase.removeChannel(realtimeChannel);
      setChannel(null);
      setIsConnected(false);
    };
  }, [user?.primaryEmailAddress?.emailAddress, userId, supabase, roomName, enableChat, enableMouse, enableClicks]);

  const sendMessage = useCallback(
    async (content: string) => {
      if (!user?.primaryEmailAddress?.emailAddress || !isConnected || !channel || !enableChat) return;

      const chatMessage: ChatMessage = {
        id: crypto.randomUUID(),
        content,
        user: {
          email: user.primaryEmailAddress.emailAddress,
          name: user.fullName || user.firstName,
        },
        createdAt: new Date().toISOString(),
      };

      // Add to local state immediately for better UX
      setMessages((current) => [...current, chatMessage]);

      // Send using existing subscribed channel
      await channel.send({
        type: 'broadcast',
        event: 'chat_message',
        payload: chatMessage,
      });
    },
    [isConnected, channel, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName, enableChat]
  );

  const sendCursorPosition = useCallback(
    (x: number, y: number) => {
      if (!user?.primaryEmailAddress?.emailAddress || !isConnected || !channel || !enableMouse) return;

      const cursorData: CursorPosition = {
        position: { x, y },
        user: {
          id: userId,
          email: user.primaryEmailAddress.emailAddress,
          name: user.fullName || user.firstName,
        },
        color,
        timestamp: Date.now(),
      };

      channel.send({
        type: 'broadcast',
        event: 'cursor_move',
        payload: cursorData,
      });
    },
    [isConnected, channel, userId, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName, color, enableMouse]
  );

  const sendClickEvent = useCallback(
    (x: number, y: number) => {
      if (!user?.primaryEmailAddress?.emailAddress || !isConnected || !channel || !enableClicks) return;

      const clickData: ClickEvent = {
        position: { x, y },
        user: {
          id: userId,
          email: user.primaryEmailAddress.emailAddress,
          name: user.fullName || user.firstName,
        },
        color,
        timestamp: Date.now(),
        id: crypto.randomUUID(),
      };

      // Add to local state for immediate feedback
      setClicks((prev) => ({
        ...prev,
        [userId]: clickData,
      }));

      channel.send({
        type: 'broadcast',
        event: 'mouse_click',
        payload: clickData,
      });
    },
    [isConnected, channel, userId, user?.primaryEmailAddress?.emailAddress, user?.fullName, user?.firstName, color, enableClicks]
  );

  return {
    messages,
    cursors,
    clicks,
    isConnected,
    sendMessage,
    sendCursorPosition,
    sendClickEvent,
    userId,
    color,
  };
}