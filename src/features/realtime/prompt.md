// demo.tsx

import RuixenCard04 from "@/components/ui/ruixen-mono-chat";

const DemoOne = () => {
  return <RuixenCard04 />;
};

export { DemoOne };

// ruixen-mono-chat.tsx

"use client";

import React, { useState } from "react";
import {
    SmilePlus,
    Send,
    MoreHorizontal,
    CheckCheck,
    Check,
    Users,
} from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface Message {
    id: string;
    content: string;
    sender: {
        name: string;
        avatar: string;
        isOnline: boolean;
    };
    timestamp: string;
    status: "sent" | "delivered" | "read";
    reactions?: Array<{
        emoji: string;
        count: number;
        reacted: boolean;
    }>;
}

interface RuixenCard04Props {
    chatName?: string;
    messages?: Message[];
}

export default function RuixenCard04({
    chatName = "Software Team",
    messages = [
        {
            id: "1",
            content: "Just pushed the latest design system updates ✨",
            sender: {
                name: "<PERSON>",
                avatar:
                    "https://github.com/shadcn.png",
                isOnline: true,
            },
            timestamp: "10:24 AM",
            status: "read",
            reactions: [
                { emoji: "🙌", count: 2, reacted: true },
                { emoji: "✨", count: 1, reacted: false },
            ],
        },
        {
            id: "2",
            content:
                "The new components look amazing! Great work on the animations.",
            sender: {
                name: "Sarah Kim",
                avatar:
                    "https://github.com/evilrabbit.png",
                isOnline: true,
            },
            timestamp: "10:26 AM",
            status: "delivered",
        },
    ],
}: RuixenCard04Props) {
    const [selectedSender, setSelectedSender] = useState<string | null>(null);

    // Get unique senders from messages
    const uniqueSenders = Array.from(
        new Map(
            messages.map((m) => [m.sender.name, m.sender])
        ).values()
    );

    // Filter messages by selected sender or show all
    const filteredMessages = selectedSender
        ? messages.filter((m) => m.sender.name === selectedSender)
        : messages;

    return (
        <div className="w-full max-w-5xl mx-auto p-6 bg-white dark:bg-black rounded-3xl shadow-lg flex flex-col h-[550px] border border-gray-300 dark:border-gray-700">
            {/* Header */}
            <header className="flex justify-between items-center border-b border-gray-300 dark:border-gray-700 pb-3 mb-6">
                <div className="flex items-center gap-3">
                    <Users className="w-8 h-8 text-black dark:text-white" />
                    <div>
                        <h2 className="text-2xl font-semibold text-black dark:text-white">
                            {chatName}
                        </h2>
                        <p className="italic text-sm text-gray-600 dark:text-gray-400">
                            Collaborate creatively, deliver clearly.
                        </p>
                    </div>
                </div>
                <button
                    aria-label="More options"
                    className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900 transition"
                >
                    <MoreHorizontal className="w-6 h-6 text-gray-600 dark:text-gray-400" />
                </button>
            </header>

            {/* Body */}
            <main className="flex flex-1 overflow-hidden rounded-xl border border-gray-300 dark:border-gray-700">
                {/* Participants List */}
                <aside className="w-56 bg-gray-50 dark:bg-gray-900 border-r border-gray-300 dark:border-gray-700 p-4 overflow-y-auto">
                    {uniqueSenders.map((sender) => {
                        const isSelected = selectedSender === sender.name;
                        return (
                            <button
                                key={sender.name}
                                onClick={() =>
                                    setSelectedSender(
                                        isSelected ? null : sender.name
                                    )
                                }
                                className={cn(
                                    "flex items-center gap-3 w-full p-3 mb-3 rounded-lg transition-colors",
                                    isSelected
                                        ? "bg-black dark:bg-white text-white dark:text-black"
                                        : "hover:bg-gray-200 dark:hover:bg-gray-800 text-gray-900 dark:text-gray-300"
                                )}
                            >
                                <div className="relative">
                                    <Image
                                        src={sender.avatar}
                                        alt={sender.name}
                                        width={40}
                                        height={40}
                                        className="rounded-full ring-1 ring-gray-400 dark:ring-gray-600"
                                    />
                                    <span
                                        className={cn(
                                            "absolute bottom-0 right-0 w-3 h-3 rounded-full ring-2 ring-white dark:ring-black",
                                            sender.isOnline
                                                ? "bg-green-500"
                                                : "bg-gray-400"
                                        )}
                                    />
                                </div>
                                <span className="text-left font-medium truncate">
                                    {sender.name}
                                </span>
                            </button>
                        );
                    })}
                </aside>

                {/* Messages */}
                <section className="flex-1 p-6 overflow-y-auto bg-white dark:bg-black">
                    {filteredMessages.length === 0 ? (
                        <p className="text-center text-gray-500 dark:text-gray-400">
                            No messages to display.
                        </p>
                    ) : (
                        filteredMessages.map((message) => (
                            <div
                                key={message.id}
                                className="mb-6 last:mb-0 group border-b border-gray-200 dark:border-gray-800 pb-4"
                            >
                                <div className="flex items-center gap-4 mb-2">
                                    <Image
                                        src={message.sender.avatar}
                                        alt={message.sender.name}
                                        width={40}
                                        height={40}
                                        className="rounded-full ring-1 ring-gray-400 dark:ring-gray-600"
                                    />
                                    <div>
                                        <p className="font-semibold text-black dark:text-white">
                                            {message.sender.name}
                                        </p>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            {message.timestamp}
                                        </span>
                                    </div>
                                </div>
                                <p className="text-gray-800 dark:text-gray-200 text-lg mb-1">
                                    {message.content}
                                </p>
                                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                    <div className="flex items-center gap-1">
                                        {message.status === "read" && (
                                            <CheckCheck className="w-5 h-5 text-green-500" />
                                        )}
                                        {message.status === "delivered" && (
                                            <Check className="w-5 h-5" />
                                        )}
                                        <span>{message.timestamp}</span>
                                    </div>
                                    <div className="flex gap-2">
                                        {message.reactions?.map((reaction) => (
                                            <button
                                                key={reaction.emoji}
                                                className={cn(
                                                    "px-2 py-1 rounded-md text-sm transition-colors",
                                                    reaction.reacted
                                                        ? "bg-gray-300 dark:bg-gray-700 text-black dark:text-white"
                                                        : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-400",
                                                    "hover:bg-gray-200 dark:hover:bg-gray-600"
                                                )}
                                            >
                                                {reaction.emoji} {reaction.count}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </section>
            </main>

            {/* Footer */}
            <footer className="mt-6 flex items-center gap-4 border-t border-gray-300 dark:border-gray-700 pt-4">
                <button
                    aria-label="Add emoji"
                    className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition"
                >
                    <SmilePlus className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                </button>
                <input
                    type="text"
                    placeholder="Write your message..."
                    className={cn(
                        "flex-1 px-5 py-3 rounded-full border border-gray-300 dark:border-gray-700",
                        "bg-white dark:bg-black text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400",
                        "focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white transition"
                    )}
                />
                <button
                    aria-label="Send message"
                    className="p-3 rounded-full bg-black dark:bg-white text-white dark:text-black hover:brightness-90 transition"
                >
                    <Send className="w-6 h-6" />
                </button>
            </footer>
        </div>
    );
}

You are given a task to integrate an existing React component in the codebase

The codebase should support:
- shadcn project structure  
- Tailwind CSS
- Typescript

If it doesn't, provide instructions on how to setup project via shadcn CLI, install Tailwind or Typescript.

Determine the default path for components and styles. 
If default path for components is not /components/ui, provide instructions on why it's important to create this folder
Copy-paste this component to /components/ui folder:
```tsx
ruixen-mono-chat.tsx
"use client";

import React, { useState } from "react";
import {
    SmilePlus,
    Send,
    MoreHorizontal,
    CheckCheck,
    Check,
    Users,
} from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface Message {
    id: string;
    content: string;
    sender: {
        name: string;
        avatar: string;
        isOnline: boolean;
    };
    timestamp: string;
    status: "sent" | "delivered" | "read";
    reactions?: Array<{
        emoji: string;
        count: number;
        reacted: boolean;
    }>;
}

interface RuixenCard04Props {
    chatName?: string;
    messages?: Message[];
}

export default function RuixenCard04({
    chatName = "Software Team",
    messages = [
        {
            id: "1",
            content: "Just pushed the latest design system updates ✨",
            sender: {
                name: "Alex Chen",
                avatar:
                    "https://github.com/shadcn.png",
                isOnline: true,
            },
            timestamp: "10:24 AM",
            status: "read",
            reactions: [
                { emoji: "🙌", count: 2, reacted: true },
                { emoji: "✨", count: 1, reacted: false },
            ],
        },
        {
            id: "2",
            content:
                "The new components look amazing! Great work on the animations.",
            sender: {
                name: "Sarah Kim",
                avatar:
                    "https://github.com/evilrabbit.png",
                isOnline: true,
            },
            timestamp: "10:26 AM",
            status: "delivered",
        },
    ],
}: RuixenCard04Props) {
    const [selectedSender, setSelectedSender] = useState<string | null>(null);

    // Get unique senders from messages
    const uniqueSenders = Array.from(
        new Map(
            messages.map((m) => [m.sender.name, m.sender])
        ).values()
    );

    // Filter messages by selected sender or show all
    const filteredMessages = selectedSender
        ? messages.filter((m) => m.sender.name === selectedSender)
        : messages;

    return (
        <div className="w-full max-w-5xl mx-auto p-6 bg-white dark:bg-black rounded-3xl shadow-lg flex flex-col h-[550px] border border-gray-300 dark:border-gray-700">
            {/* Header */}
            <header className="flex justify-between items-center border-b border-gray-300 dark:border-gray-700 pb-3 mb-6">
                <div className="flex items-center gap-3">
                    <Users className="w-8 h-8 text-black dark:text-white" />
                    <div>
                        <h2 className="text-2xl font-semibold text-black dark:text-white">
                            {chatName}
                        </h2>
                        <p className="italic text-sm text-gray-600 dark:text-gray-400">
                            Collaborate creatively, deliver clearly.
                        </p>
                    </div>
                </div>
                <button
                    aria-label="More options"
                    className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-900 transition"
                >
                    <MoreHorizontal className="w-6 h-6 text-gray-600 dark:text-gray-400" />
                </button>
            </header>

            {/* Body */}
            <main className="flex flex-1 overflow-hidden rounded-xl border border-gray-300 dark:border-gray-700">
                {/* Participants List */}
                <aside className="w-56 bg-gray-50 dark:bg-gray-900 border-r border-gray-300 dark:border-gray-700 p-4 overflow-y-auto">
                    {uniqueSenders.map((sender) => {
                        const isSelected = selectedSender === sender.name;
                        return (
                            <button
                                key={sender.name}
                                onClick={() =>
                                    setSelectedSender(
                                        isSelected ? null : sender.name
                                    )
                                }
                                className={cn(
                                    "flex items-center gap-3 w-full p-3 mb-3 rounded-lg transition-colors",
                                    isSelected
                                        ? "bg-black dark:bg-white text-white dark:text-black"
                                        : "hover:bg-gray-200 dark:hover:bg-gray-800 text-gray-900 dark:text-gray-300"
                                )}
                            >
                                <div className="relative">
                                    <Image
                                        src={sender.avatar}
                                        alt={sender.name}
                                        width={40}
                                        height={40}
                                        className="rounded-full ring-1 ring-gray-400 dark:ring-gray-600"
                                    />
                                    <span
                                        className={cn(
                                            "absolute bottom-0 right-0 w-3 h-3 rounded-full ring-2 ring-white dark:ring-black",
                                            sender.isOnline
                                                ? "bg-green-500"
                                                : "bg-gray-400"
                                        )}
                                    />
                                </div>
                                <span className="text-left font-medium truncate">
                                    {sender.name}
                                </span>
                            </button>
                        );
                    })}
                </aside>

                {/* Messages */}
                <section className="flex-1 p-6 overflow-y-auto bg-white dark:bg-black">
                    {filteredMessages.length === 0 ? (
                        <p className="text-center text-gray-500 dark:text-gray-400">
                            No messages to display.
                        </p>
                    ) : (
                        filteredMessages.map((message) => (
                            <div
                                key={message.id}
                                className="mb-6 last:mb-0 group border-b border-gray-200 dark:border-gray-800 pb-4"
                            >
                                <div className="flex items-center gap-4 mb-2">
                                    <Image
                                        src={message.sender.avatar}
                                        alt={message.sender.name}
                                        width={40}
                                        height={40}
                                        className="rounded-full ring-1 ring-gray-400 dark:ring-gray-600"
                                    />
                                    <div>
                                        <p className="font-semibold text-black dark:text-white">
                                            {message.sender.name}
                                        </p>
                                        <span className="text-xs text-gray-500 dark:text-gray-400">
                                            {message.timestamp}
                                        </span>
                                    </div>
                                </div>
                                <p className="text-gray-800 dark:text-gray-200 text-lg mb-1">
                                    {message.content}
                                </p>
                                <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
                                    <div className="flex items-center gap-1">
                                        {message.status === "read" && (
                                            <CheckCheck className="w-5 h-5 text-green-500" />
                                        )}
                                        {message.status === "delivered" && (
                                            <Check className="w-5 h-5" />
                                        )}
                                        <span>{message.timestamp}</span>
                                    </div>
                                    <div className="flex gap-2">
                                        {message.reactions?.map((reaction) => (
                                            <button
                                                key={reaction.emoji}
                                                className={cn(
                                                    "px-2 py-1 rounded-md text-sm transition-colors",
                                                    reaction.reacted
                                                        ? "bg-gray-300 dark:bg-gray-700 text-black dark:text-white"
                                                        : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-400",
                                                    "hover:bg-gray-200 dark:hover:bg-gray-600"
                                                )}
                                            >
                                                {reaction.emoji} {reaction.count}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </section>
            </main>

            {/* Footer */}
            <footer className="mt-6 flex items-center gap-4 border-t border-gray-300 dark:border-gray-700 pt-4">
                <button
                    aria-label="Add emoji"
                    className="p-3 rounded-full bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition"
                >
                    <SmilePlus className="w-6 h-6 text-gray-600 dark:text-gray-300" />
                </button>
                <input
                    type="text"
                    placeholder="Write your message..."
                    className={cn(
                        "flex-1 px-5 py-3 rounded-full border border-gray-300 dark:border-gray-700",
                        "bg-white dark:bg-black text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400",
                        "focus:outline-none focus:ring-2 focus:ring-black dark:focus:ring-white transition"
                    )}
                />
                <button
                    aria-label="Send message"
                    className="p-3 rounded-full bg-black dark:bg-white text-white dark:text-black hover:brightness-90 transition"
                >
                    <Send className="w-6 h-6" />
                </button>
            </footer>
        </div>
    );
}


demo.tsx
import RuixenCard04 from "@/components/ui/ruixen-mono-chat";

const DemoOne = () => {
  return <RuixenCard04 />;
};

export { DemoOne };

```

Install NPM dependencies:
```bash
next, lucide-react
```

Implementation Guidelines
 1. Analyze the component structure and identify all required dependencies
 2. Review the component's argumens and state
 3. Identify any required context providers or hooks and install them
 4. Questions to Ask
 - What data/props will be passed to this component?
 - Are there any specific state management requirements?
 - Are there any required assets (images, icons, etc.)?
 - What is the expected responsive behavior?
 - What is the best place to use this component in the app?

Steps to integrate
 0. Copy paste all the code above in the correct directories
 1. Install external dependencies
 2. Fill image assets with Unsplash stock images you know exist
 3. Use lucide-react icons for svgs or logos if component requires them


Please use the following technologies in your implementation: next.js 15+, tailwhind v3, shadcnui

For context, here is the current Tailwind configuration being used: import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/features/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
  	extend: {
  		backgroundImage: {
  			'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
  			'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
			sidebar: {
				DEFAULT: 'hsl(var(--sidebar))',
				foreground: 'hsl(var(--sidebar-foreground))',
				primary: 'hsl(var(--sidebar-primary))',
				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
				accent: 'hsl(var(--sidebar-accent))',
				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
				border: 'hsl(var(--sidebar-border))',
				ring: 'hsl(var(--sidebar-ring))'
			},
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			nexus: {
  				green: 'hsl(158, 64%, 52%)',
  				'dark': '#111111',
  				'gray': {
  					'light': 'hsl(0, 0%, 20%)',
  					'DEFAULT': 'hsl(0, 0%, 15%)',
  					'dark': 'hsl(0, 0%, 10%)',
  					'text': 'hsl(0, 0%, 60%)'
  				}
  			}
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			}
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out'
  		}
  	}
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;


For context, here are the global CSS styles being used: @tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbars for terminal-like components */
@layer utilities {
  .scrollbar-hide {
    /* Firefox */
    scrollbar-width: none;
    /* Safari and Chrome */
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* 3D Flip Card Utilities for Final Recipes */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Timeline Navigation Utilities */
  .timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    bottom: 1.5rem;
    width: 3px;
    background-color: hsl(var(--border));
  }

  .timeline-item .dot {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    background-color: hsl(var(--background));
    border: 3px solid hsl(var(--border));
    transition: all 0.3s ease;
  }

  .timeline-item.active .dot {
    border-color: hsl(var(--primary));
    background-color: hsl(var(--primary));
  }

  /* Active timeline item highlight */
  .timeline-item.active-box > div[class*='bg-'] {
    box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.22);
    background-color: hsl(var(--primary) / 0.05) !important;
    border: 1.5px solid hsl(var(--primary) / 0.3);
    transition: box-shadow 0.18s, border 0.18s, background 0.18s;
  }

  /* Droplet animation for recipe visualizer */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
  }

  .droplet {
    width: 8px;
    border-radius: 50%;
    animation: float 2s ease-in-out infinite;
  }
}

body {
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

@layer base {
:root {
  --background: 204.0000 12.1951% 91.9608%;
  --foreground: 0 0% 20%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 20%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 20%;
  --primary: 13.2143 73.0435% 54.9020%;
  --primary-foreground: 0 0% 100%;
  --secondary: 220.0000 14.2857% 95.8824%;
  --secondary-foreground: 215 13.7931% 34.1176%;
  --muted: 210 20.0000% 98.0392%;
  --muted-foreground: 220 8.9362% 46.0784%;
  --accent: 207.6923 46.4286% 89.0196%;
  --accent-foreground: 224.4444 64.2857% 32.9412%;
  --destructive: 0 84.2365% 60.1961%;
  --destructive-foreground: 0 0% 100%;
  --border: 210 9.3750% 87.4510%;
  --input: 220 15.7895% 96.2745%;
  --ring: 13.2143 73.0435% 54.9020%;
  --chart-1: 210 37.5000% 65.4902%;
  --chart-2: 12.9032 73.2283% 75.0980%;
  --chart-3: 213.1579 29.9213% 50.1961%;
  --chart-4: 216.9231 35.7798% 42.7451%;
  --chart-5: 221.0127 43.6464% 35.4902%;
  --sidebar: 216 7.9365% 87.6471%;
  --sidebar-foreground: 0 0% 20%;
  --sidebar-primary: 13.2143 73.0435% 54.9020%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 207.6923 46.4286% 89.0196%;
  --sidebar-accent-foreground: 224.4444 64.2857% 32.9412%;
  --sidebar-border: 220 13.0435% 90.9804%;
  --sidebar-ring: 13.2143 73.0435% 54.9020%;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  --background: 219.1304 29.1139% 15.4902%;
  --foreground: 0 0% 89.8039%;
  --card: 223.6364 20.7547% 20.7843%;
  --card-foreground: 0 0% 89.8039%;
  --popover: 223.3333 19.1489% 18.4314%;
  --popover-foreground: 0 0% 89.8039%;
  --primary: 13.2143 73.0435% 54.9020%;
  --primary-foreground: 0 0% 100%;
  --secondary: 222 19.2308% 20.3922%;
  --secondary-foreground: 0 0% 89.8039%;
  --muted: 222 19.2308% 20.3922%;
  --muted-foreground: 0 0% 63.9216%;
  --accent: 223.6364 34.3750% 25.0980%;
  --accent-foreground: 213.3333 96.9231% 87.2549%;
  --destructive: 0 84.2365% 60.1961%;
  --destructive-foreground: 0 0% 100%;
  --border: 224.3478 15.8621% 28.4314%;
  --input: 224.3478 15.8621% 28.4314%;
  --ring: 13.2143 73.0435% 54.9020%;
  --chart-1: 210 37.5000% 65.4902%;
  --chart-2: 11.7241 63.5036% 73.1373%;
  --chart-3: 213.1579 29.9213% 50.1961%;
  --chart-4: 216.9231 35.7798% 42.7451%;
  --chart-5: 221.0127 43.6464% 35.4902%;
  --sidebar: 222.8571 20.0000% 20.5882%;
  --sidebar-foreground: 0 0% 89.8039%;
  --sidebar-primary: 13.2143 73.0435% 54.9020%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 223.6364 34.3750% 25.0980%;
  --sidebar-accent-foreground: 213.3333 96.9231% 87.2549%;
  --sidebar-border: 224.3478 15.8621% 28.4314%;
  --sidebar-ring: 13.2143 73.0435% 54.9020%;
  --font-sans: Inter, sans-serif;
  --font-serif: Source Serif 4, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: 0.75rem;
  --shadow-2xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 1px 3px 0px hsl(0 0% 0% / 0.25);
}
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-sidebar text-foreground;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
  main {
    flex-grow: 1;
  }
}
  /* Final Recipe UI Styles - using theme variables only */
  
  /* Flip card animation styles */
  .flip-card {
    perspective: 1000px;
  }

  .flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    transition: transform 0.8s;
    transform-style: preserve-3d;
  }

  .flip-card.is-flipped .flip-card-inner {
    transform: rotateY(180deg);
  }

  .flip-card-front,
  .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 1rem;
    overflow: hidden;
  }

  .flip-card-back {
    transform: rotateY(180deg);
  }

  /* Utility classes for backface visibility */
  .backface-hidden {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Droplet animation */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
  }

  .droplet {
    width: 8px;
    border-radius: 50%;
    animation: float 2s ease-in-out infinite;
  }

  /* Timeline styles */
  .timeline {
    position: relative;
  }

  .timeline-item .dot {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    transform: translateX(-50%);
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 9999px;
    background-color: hsl(var(--background));
    border: 3px solid hsl(var(--border));
    transition: all 0.3s ease;
  }

  .timeline-item.active .dot {
    border-color: hsl(var(--primary));
    background-color: hsl(var(--primary));
  }

  /* Active timeline highlighting */
  .timeline-item.active-box > div[class^='bg-'] {
    box-shadow: 0 2px 8px 0 hsl(var(--primary) / 0.07), 0 0 0 2px hsl(var(--primary) / 0.13);
    background-color: hsl(var(--primary) / 0.05) !important;
    border: 1.5px solid hsl(var(--primary) / 0.3);
    transition: box-shadow 0.18s, border 0.18s, background 0.18s;
  }

  /* Protocol card styles */
  .protocol-card {
    background: hsl(var(--card));
    border-radius: 20px;
    box-shadow: 0 8px 24px hsl(var(--foreground) / 0.12);
    overflow: hidden;
    max-width: 550px;
    width: 100%;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid hsl(var(--border));
  }

  .protocol-header {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    color: hsl(var(--primary-foreground));
    padding: 24px 24px 60px;
    position: relative;
  }

  .time-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background: hsl(var(--primary-foreground) / 0.2);
    color: hsl(var(--primary-foreground));
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 10;
  }

  .protocol-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 8px;
  }

  .protocol-subtitle {
    font-size: 1rem;
    opacity: 0.9;
  }

  .recipe-visual {
    background: hsl(var(--card));
    margin: -40px 24px 20px;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 16px hsl(var(--foreground) / 0.08);
    position: relative;
    z-index: 5;
  }

  .droplet-visualizer {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 4px;
    height: 40px;
    padding-bottom: 16px;
    margin-bottom: 8px;
  }

  .ingredient-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
  }

  .ingredient-badge {
    flex-shrink: 0;
    text-align: center;
    font-size: 0.8rem;
    font-weight: 600;
    background-color: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
    border-radius: 8px;
    width: 60px;
    padding: 8px 4px;
  }

  .ingredient-details .name {
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .ingredient-details .botanical {
    font-size: 0.8rem;
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }

  .collapsible-strip {
    list-style: none;
    cursor: pointer;
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
    color: hsl(var(--primary-foreground));
    padding: 16px 24px;
    transition: background 0.3s ease;
  }

  .collapsible-strip:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.8), hsl(var(--primary)));
  }

  .collapsible-strip .title {
    font-weight: 600;
  }

  .collapsible-strip .subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
  }

  .collapsible-content {
    background-color: hsl(var(--muted) / 0.3);
    padding: 20px 24px;
  }

  .collapsible-strip::-webkit-details-marker {
    display: none;
  }

  .collapsible-strip .arrow {
    transition: transform 0.3s ease;
  }

  details[open] .collapsible-strip .arrow {
    transform: rotate(90deg);
  }

Additional important context to consider: Important files to maintain DRY concept in place when developing chat interfaces
src\hooks\use-auto-scroll.ts 
src\features\realtime

# Development Constraints

## Prohibited
- No test files, performance optimizations, monitoring, or caching
- No hardcoded colors, styles, or user-facing text
- No unnecessary complexity or redundant functionality

## Required
- Use existing themed colors from component files
- Use translations from `src\lib\i18n\messages[locale]\create-recipe.json`
- Maintain 100% build success (`npm run build`)
- Follow DRY, YAGNI, KISS principles

## Goal
Implement only explicitly listed tasks. Check existing functionality before adding new code to prevent legacy bloat.

User Additional Context:
Creat a new page for realtime chat.
Remember: Do not change the component's code unless it's required to integrate or the user asks you to.
IMPORTANT: Create all mentioned files in full, without abbreviations. Do not use placeholders like "insert the rest of the code here" – output every line of code exactly as it is, so it can be copied and pasted directly into the project.


Can I have the chat interface more adapted/merged with this example here:
'use client';
import { AIMessage, AIMessageAvatar, AIMessageContent } from '@/components/ui/kibo-ui/ai/message';
const messages: {
from: 'user' | 'assistant';
content: string;
avatar: string;
name: string;
}[] = [
{
from: 'user',
content: 'Hello, how are you?',
avatar: 'https://github.com/haydenbleasel.png',
name: 'Hayden Bleasel',
},
{
from: 'assistant',
content: 'I am fine, thank you!',
avatar: 'https://github.com/openai.png',
name: 'OpenAI',
},
{
from: 'user',
content: 'What is the weather in Tokyo?',
avatar: 'https://github.com/haydenbleasel.png',
name: 'Hayden Bleasel',
},
{
from: 'assistant',
content: 'The weather in Tokyo is sunny.',
avatar: 'https://github.com/openai.png',
name: 'OpenAI',
},
];
const Example = () => (
<>
{messages.map(({ content, ...message }, index) => (
<AIMessage from={message.from} key={index}>
<AIMessageContent>{content}</AIMessageContent>
<AIMessageAvatar name={message.name} src={message.avatar} />
</AIMessage>
))}
</>
);

export default Example;
'use client';
import { AIMessage, AIMessageAvatar, AIMessageContent } from '@/components/ui/kibo-ui/ai/message';
const messages: {
from: 'user' | 'assistant';
content: string;
avatar: string;
name: string;
}[] = [
{
from: 'user',
content: 'Hello, how are you?',
avatar: 'https://github.com/haydenbleasel.png',
name: 'Hayden Bleasel',
},
{
from: 'assistant',
content: 'I am fine, thank you!',
avatar: 'https://github.com/openai.png',
name: 'OpenAI',
},
{
from: 'user',
content: 'What is the weather in Tokyo?',
avatar: 'https://github.com/haydenbleasel.png',
name: 'Hayden Bleasel',
},
{
from: 'assistant',
content: 'The weather in Tokyo is sunny.',
avatar: 'https://github.com/openai.png',
name: 'OpenAI',
},
];
const Example = () => (
<>
{messages.map(({ content, ...message }, index) => (
<AIMessage from={message.from} key={index}>
<AIMessageContent>{content}</AIMessageContent>
<AIMessageAvatar name={message.name} src={message.avatar} />
</AIMessage>
))}
</>
);
export default Example;

Features
Displays messages from both the user and other online users with distinct styling.
Includes avatar images for message senders with fallback initials.
Shows the sender's name through avatar fallbacks.
Automatically aligns user and other user messages on opposite sides.
Uses different background colors for user and other users.

Notes
Always render the AIMessageContent first, then the AIMessageAvatar. The AIMessage component is a wrapper that determines the alignment of the message.

update the AIMessage components to remove the redundant AIMessageAvatar since we're using the existing UserAvatar componen