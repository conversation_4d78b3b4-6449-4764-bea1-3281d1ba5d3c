import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import type { ClickEvent } from '../hooks/use-unified-realtime';

interface ClickIndicatorProps {
    click: ClickEvent;
    onComplete?: () => void;
}

export function ClickIndicator({ click, onComplete }: ClickIndicatorProps) {
    const [isVisible, setIsVisible] = useState(true);

    useEffect(() => {
        const timer = setTimeout(() => {
            setIsVisible(false);
            onComplete?.();
        }, 1000); // Show for 1 second

        return () => clearTimeout(timer);
    }, [onComplete]);

    if (!isVisible) return null;

    return (
        <motion.div
            className="fixed pointer-events-none z-50"
            style={{
                left: click.position.x,
                top: click.position.y,
                color: click.color,
            }}
            initial={{ scale: 0, opacity: 1 }}
            animate={{ scale: [0, 1.2, 1], opacity: [1, 1, 0] }}
            exit={{ scale: 0, opacity: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
        >
            {/* Ripple effect */}
            <div className="absolute inset-0 -translate-x-1/2 -translate-y-1/2">
                <div
                    className="w-8 h-8 rounded-full border-2 border-current opacity-60"
                    style={{ borderColor: 'currentColor' }}
                />
                <motion.div
                    className="absolute inset-0 w-8 h-8 rounded-full border border-current"
                    style={{ borderColor: 'currentColor' }}
                    initial={{ scale: 1, opacity: 0.8 }}
                    animate={{ scale: 3, opacity: 0 }}
                    transition={{ duration: 1, ease: "easeOut" }}
                />
            </div>

            {/* User info tooltip */}
            <div
                className="absolute -top-8 left-1/2 -translate-x-1/2 px-2 py-1 rounded text-xs whitespace-nowrap"
                style={{
                    backgroundColor: 'hsl(var(--background))',
                    border: `1px solid ${click.color}`,
                    color: 'hsl(var(--foreground))',
                }}
            >
                {click.user.name || click.user.email}
            </div>
        </motion.div>
    );
}

interface ClickVisualizationProps {
    clicks: Record<string, ClickEvent>;
}

export function ClickVisualization({ clicks }: ClickVisualizationProps) {
    const [visibleClicks, setVisibleClicks] = useState<Record<string, ClickEvent>>({});

    useEffect(() => {
        // Ensure clicks is always a valid object
        if (clicks && typeof clicks === 'object' && !Array.isArray(clicks)) {
            setVisibleClicks(clicks);
        }
    }, [clicks]);

    const handleClickComplete = (userId: string) => {
        setVisibleClicks(prev => {
            const updated = { ...prev };
            delete updated[userId];
            return updated;
        });
    };

    // Safely get click values
    const clickValues = visibleClicks && typeof visibleClicks === 'object' 
        ? Object.values(visibleClicks) 
        : [];

    return (
        <AnimatePresence>
            {clickValues.map((click) => (
                <ClickIndicator
                    key={click.user.id}
                    click={click}
                    onComplete={() => handleClickComplete(click.user.id)}
                />
            ))}
        </AnimatePresence>
    );
}