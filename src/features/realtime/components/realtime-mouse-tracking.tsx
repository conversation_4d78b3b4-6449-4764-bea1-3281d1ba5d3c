'use client';

import { useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Cursor } from './cursor';
import { useRealtimeMouseTracking } from '../hooks/use-realtime-mouse-tracking';

interface RealtimeMouseTrackingProps {
  roomName: string;
  throttleMs?: number;
  includeClicks?: boolean;
  maxClickHistory?: number;
  className?: string;
  children?: React.ReactNode;
}

export const RealtimeMouseTracking = ({
  roomName,
  throttleMs = 50,
  includeClicks = false,
  maxClickHistory = 10,
  className,
  children,
}: RealtimeMouseTrackingProps) => {
  const {
    cursors,
    clicks,
    isConnected,
    sendCursorPosition,
    sendClickEvent,
  } = useRealtimeMouseTracking({
    roomName,
    throttleMs,
    includeClicks,
    maxClickHistory,
  });

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    const x = e.clientX;
    const y = e.clientY;
    sendCursorPosition(x, y);
  }, [sendCursorPosition]);

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (!includeClicks) return;
    const x = e.clientX;
    const y = e.clientY;
    sendClickEvent(x, y);
  }, [sendClickEvent, includeClicks]);

  return (
    <div
      className={cn('relative', className)}
      onMouseMove={handleMouseMove}
      onClick={handleClick}
    >
      {/* Cursors with smooth transitions */}
      {Object.values(cursors).map((cursor) => (
        <Cursor
          key={cursor.user.id}
          className="fixed transition-transform ease-in-out z-50"
          style={{
            transitionDuration: '20ms',
            top: 0,
            left: 0,
            transform: `translate(${cursor.position.x}px, ${cursor.position.y}px)`,
          }}
          color={cursor.color}
          name={`${cursor.user.email} ${cursor.user.name ? `(${cursor.user.name})` : ''}`}
        />
      ))}

      {/* Click Events */}
      {includeClicks && clicks.map((click) => (
        <div
          key={`${click.user.id}-${click.timestamp}`}
          className="fixed pointer-events-none z-40 animate-ping"
          style={{
            left: click.position.x,
            top: click.position.y,
            transform: 'translate(-50%, -50%)',
            animationDuration: '1s',
            animationIterationCount: '3',
          }}
        >
          <div
            className="w-8 h-8 rounded-full border-4 border-background shadow-lg opacity-75"
            style={{ backgroundColor: click.color }}
          />
        </div>
      ))}

      {children}
    </div>
  );
};

// Export connection status and stats hook for UI components
export const useMouseTrackingStats = (roomName: string, includeClicks = false) => {
  const { cursors, clicks, isConnected } = useRealtimeMouseTracking({
    roomName,
    includeClicks,
  });

  return {
    connectedUsers: Object.keys(cursors).length,
    recentClicks: clicks.length,
    isConnected,
  };
};