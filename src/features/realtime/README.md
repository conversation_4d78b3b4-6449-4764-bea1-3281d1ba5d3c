# Realtime Features Documentation

This directory contains production-ready realtime components and hooks built with **official Supabase patterns** to prevent subscription conflicts and ensure reliable performance.

## 📁 Structure

```
src/features/realtime/
├── components/
│   ├── cursor.tsx                    # Reusable cursor component with MousePointer2
│   ├── chat-message.tsx             # Chat message item component
│   ├── realtime-chat.tsx            # Chat component with auto-scroll
│   └── realtime-mouse-tracking.tsx  # Mouse tracking component
├── hooks/
│   ├── use-realtime-chat.ts         # Chat hook (single room)
│   ├── use-realtime-mouse-tracking.ts # Mouse tracking hook (single room)
│   └── use-unified-realtime.ts      # Unified hook (mouse + chat, single room)
├── docs/
│   ├── PATTERNS.md                  # Official Supabase patterns
│   ├── TROUBLESHOOTING.md           # Known issues and fixes
│   └── CONSTRAINTS.md               # Critical constraints
└── README.md                        # This file
```

## 🚀 Quick Start

### Single Feature Usage

```tsx
// Chat only
import { RealtimeChat } from '@/features/realtime';
<RealtimeChat roomName="my-room" username="<EMAIL>" />

// Mouse tracking only
import { RealtimeMouseTracking } from '@/features/realtime';
<RealtimeMouseTracking roomName="my-room" includeClicks={true} />
```

### Multiple Features (Same Room)

```tsx
// ✅ CORRECT: Use unified hook for same room
import { useUnifiedRealtime } from '@/features/realtime';

const { messages, cursors, sendMessage, sendCursorPosition } = useUnifiedRealtime({
  roomName: 'my-room',
  username: '<EMAIL>',
  enableChat: true,
  enableMouse: true,
});
```

## ⚠️ Critical Rules

### 🔴 NEVER DO THIS:
```tsx
// ❌ WRONG: Multiple components for same room = subscription conflict
<RealtimeMouseTracking roomName="same-room">
  <RealtimeChat roomName="same-room" />  {/* ERROR! */}
</RealtimeMouseTracking>
```

### ✅ ALWAYS DO THIS:
```tsx
// ✅ CORRECT: Different rooms OR unified hook
<RealtimeMouseTracking roomName="mouse-room" />
<RealtimeChat roomName="chat-room" />

// OR for same room:
const unified = useUnifiedRealtime({ roomName: 'same-room' });
```

## 🎯 Key Features

- **Official Supabase Patterns**: Prevents "tried to subscribe multiple times" errors
- **Design System Compliant**: Uses CSS variables, no hardcoded colors
- **Smart Auto-Scroll**: Chat scrolls intelligently based on user position
- **TypeScript Safe**: Full type safety throughout
- **Performance Optimized**: 50ms throttling, proper cleanup
- **Production Ready**: Handles errors, timeouts, and edge cases

## 📚 Documentation

- **[PATTERNS.md](./docs/PATTERNS.md)** - Official Supabase patterns and best practices
- **[TROUBLESHOOTING.md](./docs/TROUBLESHOOTING.md)** - Common issues and solutions
- **[CONSTRAINTS.md](./docs/CONSTRAINTS.md)** - Critical constraints and limitations
- **[ARCHITECTURE.md](./docs/ARCHITECTURE.md)** - System architecture and design decisions

## 🔧 Development

All components follow **DRY, YAGNI, and KISS** principles:
- **DRY**: Reusable components and hooks
- **YAGNI**: Only implement what's needed
- **KISS**: Simple, clear implementations

## 🚨 Emergency Fixes

If you encounter the **"tried to subscribe multiple times"** error:

1. Check if multiple components use the same `roomName`
2. Use `useUnifiedRealtime` hook instead
3. See [TROUBLESHOOTING.md](./docs/TROUBLESHOOTING.md) for detailed fixes

---

**Last Updated**: Based on official Supabase documentation and production testing
**Status**: ✅ Production Ready