
// src/features/homepage/components/hero-header/hero-header.tsx
"use client";

import React, { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useMotionValueEvent, AnimatePresence, type Variants } from 'framer-motion';
import Link from 'next/link';
import NavLink from './nav-link';
import DropdownMenu from './dropdown-menu';
import MobileMenu from './mobile-menu';
import { MenuIcon, CloseIcon } from './icons';
import { NAV_ITEMS_DESKTOP, NAV_ITEMS_MOBILE, LOGO_TEXT } from '../../constants';
import type { NavItem as NavItemType } from '../../types';
import { useAuth } from '@/features/auth/hooks';
import { UserButton, ClerkLoading, ClerkLoaded, SignedIn, SignedOut } from '@clerk/nextjs';
import { UserButtonWithCustomPages } from '@/features/auth/components/user-button/user-button-with-custom-pages';
import { Button } from '@/components/ui/button';
import { PassForgeLogo } from '@/components/icons';
import { Skeleton } from '@/components/ui/skeleton';
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';
import { useServerI18n } from '../../layout/homepage-layout';

/**
 * Renders the main header for the homepage.
 * It includes the application logo, desktop navigation links,
 * a mobile menu toggle, and authentication-related action buttons.
 * Authentication state is managed via the `useAuth` hook.
 *
 * @returns {JSX.Element} The homepage header component.
 */
const HeroHeader: React.FC = () => {
  // Prevent hydration mismatch
  const [isMounted, setIsMounted] = useState(false);
  
  // Get i18n context with fallback
  let t: (key: string, fallback?: string) => string;
  
  try {
    const context = useServerI18n();
    t = context.t;
  } catch {
    // Fallback function when no i18n context is available
    t = (key: string, fallback?: string) => fallback || key;
  }
  const [isScrolled, setIsScrolled] = useState(false);
  const { scrollY } = useScroll();
  useMotionValueEvent(scrollY, "change", (latest) => {
    setIsScrolled(latest > 10);
  });

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const headerRef = useRef<HTMLElement>(null);
  const dropdownTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const { isAuthenticated } = useAuth();

  const handleDropdownEnter = (label: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current);
    }
    setOpenDropdown(label);
  };

  const handleDropdownLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setOpenDropdown(null);
    }, 100);
  };

  const closeDropdown = () => {
    setOpenDropdown(null);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(prev => !prev);
  };

  // Prevent hydration mismatch by ensuring component is mounted
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const headerVariants: Variants = {
    top: {
      backgroundColor: 'hsl(var(--background) / 0.8)',
      borderBottomColor: 'hsl(var(--border) / 0.5)',
      boxShadow: 'none',
    },
    scrolled: {
      backgroundColor: 'hsl(var(--background) / 0.95)',
      borderBottomColor: 'hsl(var(--border) / 0.7)',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    }
  };

  // Keep it simple - use basic appearance config only
  const userButtonAppearance = getClerkAppearance();

  return (
    <motion.header
      ref={headerRef}
      variants={headerVariants}
      initial="top"
      animate={isScrolled ? 'scrolled' : 'top'}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="px-6 w-full md:px-10 lg:px-16 fixed top-0 left-0 right-0 z-30 backdrop-blur-md border-b"
    >
      <div className="container mx-auto px-0 sm:px-0 lg:px-0">
        <nav className="flex justify-between items-center max-w-screen-xl mx-auto h-[70px]">
          <Link href="/" className="flex items-center flex-shrink-0 group">
            <PassForgeLogo className="h-8 w-8 text-primary group-hover:text-primary transition-colors" />
            <span className="text-xl font-bold ml-2 text-foreground group-hover:text-primary transition-colors">
              {LOGO_TEXT}
            </span>
          </Link>

          <div className="hidden md:flex items-center justify-center flex-grow space-x-6 lg:space-x-8 px-4">
            {NAV_ITEMS_DESKTOP.map((item: NavItemType) => {
              const translatedLabel = t(item.label, item.label);
              const translatedChildren = item.children?.map(child => ({
                ...child,
                label: t(child.label, child.label)
              }));
              
              return (
                <div
                  key={item.label}
                  className="relative"
                  onMouseEnter={item.children ? () => handleDropdownEnter(translatedLabel) : undefined}
                  onMouseLeave={item.children ? handleDropdownLeave : undefined}
                >
                  <NavLink
                    href={item.href}
                    label={translatedLabel}
                    hasDropdown={!!item.children}
                    isOpen={openDropdown === translatedLabel}
                    onClick={item.children ? (e) => { e.preventDefault(); handleDropdownEnter(translatedLabel); } : closeDropdown}
                  />
                  {translatedChildren && (
                    <DropdownMenu
                      items={translatedChildren}
                      isOpen={openDropdown === translatedLabel}
                      onClose={closeDropdown}
                    />
                  )}
                </div>
              );
            })}
          </div>

          <div className="hidden md:flex items-center flex-shrink-0 space-x-2 sm:space-x-4 lg:space-x-6">
            {!isMounted ? (
              <Skeleton className="h-8 w-8 rounded-full" />
            ) : (
              <>
                <ClerkLoading>
                  <motion.div
                    key="skeleton"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center space-x-2 sm:space-x-4 lg:space-x-6"
                  >
                    <Skeleton className="h-8 w-8 rounded-full" />
                  </motion.div>
                </ClerkLoading>
                <ClerkLoaded>
                  <AnimatePresence>
                <SignedIn key="signed-in">
                  <motion.div
                    key="authenticated-content"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex items-center space-x-2 sm:space-x-4 lg:space-x-6"
                  >
                    {/* DRY: Use centralized UserButton component */}
                    <UserButtonWithCustomPages
                      appearance={userButtonAppearance}
                      context="homepage"
                    />
                    <Button variant="default" asChild size="sm">
                      <Link href="/dashboard">{t('nav.dashboard', 'Dashboard')}</Link>
                    </Button>
                  </motion.div>
                </SignedIn>
                <SignedOut key="signed-out">
                  <motion.div
                    key="unauthenticated-content"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="flex items-center space-x-2 sm:space-x-4 lg:space-x-6"
                  >
                    <Button variant="ghost" asChild size="sm">
                      <Link href="/login">{t('nav.signIn', 'Sign In')}</Link>
                    </Button>
                    <Button variant="default" asChild size="sm">
                      <Link href="/register">{t('nav.getStarted', 'Get Started')}</Link>
                    </Button>
                  </motion.div>
                </SignedOut>
              </AnimatePresence>
            </ClerkLoaded>
            </>
            )}
          </div>

          <div className="md:hidden flex items-center">
            <motion.button
              onClick={toggleMobileMenu}
              className="text-muted-foreground hover:text-foreground z-50 p-2 -mr-2"
              aria-label="Toggle menu"
              whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}
            >
              {isMobileMenuOpen ? <CloseIcon /> : <MenuIcon />}
            </motion.button>
          </div>
        </nav>
      </div>
      <MobileMenu
        isOpen={isMobileMenuOpen}
        items={NAV_ITEMS_MOBILE.map(item => ({
          ...item,
          label: t(item.label, item.label),
          children: item.children?.map(child => ({
            ...child,
            label: t(child.label, child.label)
          }))
        }))}
        onClose={toggleMobileMenu}
        isSessionLoading={!isMounted}
        isAuthenticated={isMounted && isAuthenticated}
      />
    </motion.header>
  );
};

export default HeroHeader;
