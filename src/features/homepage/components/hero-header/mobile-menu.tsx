// src/features/homepage/components/hero-header/mobile-menu.tsx
"use client";

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import type { NavItem } from '../../types';
import NavLink from './nav-link';
import DropdownItem from './dropdown-item'; 
import { ChevronDownIcon as ChevronDownIconImported } from './icons'; 
import { UserButton, ClerkLoading, ClerkLoaded, SignedIn, SignedOut } from '@clerk/nextjs';
import { UserButtonWithCustomPages } from '@/features/auth/components/user-button/user-button-with-custom-pages';
import { Button, Separator } from '@/components/ui';
import { Loader2 } from 'lucide-react';
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';
import { useServerI18n } from '../../layout/homepage-layout';

interface MobileMenuProps {
  isOpen: boolean;
  items: NavItem[]; 
  onClose: () => void;
  isSessionLoading: boolean; // Prop for session loading state (now includes mounted check)
  isAuthenticated: boolean; // Prop for basic authentication state (now includes mounted check)
}

/**
 * Renders the mobile navigation menu.
 * Authentication state (isSessionLoading, isAuthenticated) is passed as props.
 *
 * @param {MobileMenuProps} props - The component's props.
 * @returns {JSX.Element | null} The mobile menu component or null if not open.
 */
const MobileMenu: React.FC<MobileMenuProps> = ({ 
  isOpen, 
  items, 
  onClose,
  isSessionLoading: _isSessionLoading, // Passed from parent but not used - Clerk handles loading
  isAuthenticated: _isAuthenticated, // Passed from parent but not used - Clerk handles auth state
}) => {
  // Get i18n context with fallback
  let t: (key: string, fallback?: string) => string;
  
  try {
    const context = useServerI18n();
    t = context.t;
  } catch {
    // Fallback function when no i18n context is available
    t = (key: string, fallback?: string) => fallback || key;
  }
  // KISS: Keep mobile appearance simple too
  const mobileUserButtonAppearance = getClerkAppearance();

  const mobileMenuVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.2, ease: "easeOut" } },
    exit: { opacity: 0, y: -20, transition: { duration: 0.15, ease: "easeIn" } }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key={`mobile-menu-${isOpen}`}
          variants={mobileMenuVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="md:hidden absolute top-full left-0 right-0 bg-background/95 backdrop-blur-sm shadow-lg py-4 border-t border-border/50 z-40 max-h-[calc(100vh-70px)] overflow-y-auto"
        >
          <div className="flex flex-col items-center space-y-1 px-6 pb-4">
            {items.map((item, index) => (
              item.children ? (
                 <details key={`${item.label}-${index}`} className="group w-full text-center">
                    <summary className="flex items-center justify-center py-2 font-medium cursor-pointer text-muted-foreground hover:text-foreground transition-colors duration-200">
                      {item.label}
                      <ChevronDownIconImported className="h-4 w-4 ml-1 transition-transform duration-200 group-open:rotate-180" />
                    </summary>
                    <div className="pl-4 pt-1 pb-2 space-y-1">
                      {item.children.map((child, childIndex) => (
                        <DropdownItem key={`${child.label}-${childIndex}`} {...child} onClick={onClose} />
                      ))}
                    </div>
                  </details>
              ) : (
                <NavLink
                  key={`${item.label}-${index}`}
                  href={item.href}
                  label={item.label}
                  onClick={() => { if (item.href && item.href.startsWith('#')) onClose(); else onClose(); }}
                  className="w-full text-center py-2"
                  isButton={item.isButton}
                  isPrimary={item.isPrimary}
                />
              )
            ))}

            <Separator className="my-3 w-full" />

            <ClerkLoading>
              <Loader2 className="h-6 w-6 animate-spin text-primary my-2" />
            </ClerkLoading>
            <ClerkLoaded>
              <SignedIn>
                <div className="flex justify-center my-2">
                  {/* DRY: Use centralized UserButton component */}
                  <UserButtonWithCustomPages
                    appearance={mobileUserButtonAppearance}
                    context="mobile"
                  />
                </div>
                <Button variant="default" asChild size="sm" className="w-full my-1" onClick={onClose}>
                  <Link href="/dashboard">{t('nav.dashboard', 'Dashboard')}</Link>
                </Button>
              </SignedIn>
              <SignedOut>
                <Button variant="ghost" asChild size="sm" className="w-full my-1" onClick={onClose}>
                  <Link href="/login">{t('nav.login', 'Login')}</Link>
                </Button>
                <Button variant="default" asChild size="sm" className="w-full my-1" onClick={onClose}>
                  <Link href="/register">{t('nav.register', 'Register')}</Link>
                </Button>
              </SignedOut>
            </ClerkLoaded>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MobileMenu;
