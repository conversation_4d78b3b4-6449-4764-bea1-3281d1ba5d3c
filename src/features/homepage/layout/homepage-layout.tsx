/**
 * Unified homepage layout component
 * Supports both localized (with i18n) and non-localized usage
 * Provides server-side translations when available while maintaining all existing optimizations
 */

'use client';

import React, { useState, useEffect } from 'react';
import { LoadingProvider } from '@/features/ui/providers/loading-provider';
import { GoogleOneTap } from '@clerk/nextjs';
import HeroHeader from '../components/hero-header/hero-header';
import { HeroContent } from '../components/hero-content/hero-content';
import HeroCanvasBackground from '../components/hero-canvas-background/hero-canvas-background';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';

interface HomepageLayoutProps {
  locale?: SupportedLocale;
  translations?: Record<string, Record<string, any>>;
}

/**
 * I18n Context for providing server-side translations to client components
 */
const I18nContext = React.createContext<{
  locale: SupportedLocale | undefined;
  translations: Record<string, Record<string, any>> | undefined;
  t: (key: string, fallback?: string) => string;
} | null>(null);

/**
 * Hook to use i18n context in client components
 */
export const useServerI18n = () => {
  const context = React.useContext(I18nContext);
  if (!context) {
    throw new Error('useServerI18n must be used within HomepageLayoutWithI18n');
  }
  return context;
};

/**
 * Unified homepage layout component
 * Supports both localized (with i18n) and non-localized usage
 * Maintains all existing optimizations while adding translation capabilities when needed
 */
export const HomepageLayout: React.FC<HomepageLayoutProps> = ({
  locale,
  translations
}) => {
  // Prevent hydration mismatch for Clerk components
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);
  // Create translation function for client components
  const t = React.useCallback((key: string, fallback?: string): string => {
    // If no translations provided, return fallback or key (for root page)
    if (!translations) {
      return fallback || key;
    }

    // Handle namespace:key format
    let namespace = 'homepage';
    let translationKey = key;

    if (key.includes(':')) {
      const [ns, ...keyParts] = key.split(':');
      namespace = ns || 'homepage';
      translationKey = keyParts.join(':');
    }

    // Get nested value using dot notation
    const getNestedValue = (obj: any, path: string): string | undefined => {
      return path.split('.').reduce((current, k) => current?.[k], obj);
    };

    const namespaceTranslations = translations[namespace];
    if (!namespaceTranslations) {
      return fallback || key;
    }

    const translation = getNestedValue(namespaceTranslations, translationKey);
    return translation || fallback || key;
  }, [translations]);

  const contextValue = React.useMemo(() => ({
    locale,
    translations,
    t
  }), [locale, translations, t]);

  return (
    <I18nContext.Provider value={contextValue}>
      <LoadingProvider>
        {/* Only render GoogleOneTap after hydration to prevent mismatch */}
        {isMounted && (
          <GoogleOneTap
            signInForceRedirectUrl={locale ? `/${locale}/dashboard` : '/dashboard'}
            signUpForceRedirectUrl={locale ? `/${locale}/onboarding` : '/onboarding'}
          />
        )}
        <section className="relative bg-background text-muted-foreground min-h-screen flex flex-col overflow-x-hidden pt-[70px]">
          {/* Canvas Background - z-0 */}
          <HeroCanvasBackground color="var(--primary)" />

          {/* Original Gradient Overlay - z-1 */}
          <div
            className="absolute inset-0 z-1 pointer-events-none"
            style={{
              background:
                'linear-gradient(to bottom, transparent 0%, hsl(var(--background)) 90%), radial-gradient(ellipse at center, transparent 40%, hsl(var(--background)) 95%)',
            }}
          />

          {/* Hero Header */}
          <HeroHeader />

          {/* Hero Content */}
          <main className="relative z-10 flex-grow flex flex-col items-center justify-center text-center px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
            <HeroContent />
          </main>
        </section>
      </LoadingProvider>
    </I18nContext.Provider>
  );
};

// Backward compatibility exports
export const HomepageLayoutWithI18n = HomepageLayout;
