
import type { NavItem } from '../types'; 

// Navigation items with i18n keys - labels will be translated at runtime
export const NAV_ITEMS_DESKTOP: NavItem[] = [
  { label: 'nav.product', href: '#product' },
  { label: 'nav.customers', href: '#customers' },
  {
    label: 'nav.channels',
    href: '#channels', 
    children: [
      { label: 'nav.channels.slack', href: '#slack' },
      { label: 'nav.channels.teams', href: '#ms-teams' },
      { label: 'nav.channels.discord', href: '#discord' },
      { label: 'nav.channels.email', href: '#email' },
      { label: 'nav.channels.webChat', href: '#web-chat' },
    ],
  },
  {
    label: 'nav.resources',
    href: '#resources', 
    children: [
      { label: 'nav.resources.blog', href: '#blog', icon: undefined },
      { label: 'nav.resources.guides', href: '#guides' },
      { label: 'nav.resources.helpCenter', href: '#help-center' },
      { label: 'nav.resources.apiReference', href: '#api-reference' },
    ],
  },
  { label: 'nav.docs', href: '#docs' },
  { label: 'nav.pricing', href: '#pricing' },
];

export const NAV_ITEMS_MOBILE: NavItem[] = [
  { label: 'nav.product', href: '#product' },
  { label: 'nav.customers', href: '#customers' },
  {
    label: 'nav.channels',
    href: '#channels', 
    children: [
      { label: 'nav.channels.slack', href: '#slack' },
      { label: 'nav.channels.teams', href: '#ms-teams' },
      { label: 'nav.channels.discord', href: '#discord' },
      { label: 'nav.channels.email', href: '#email' },
      { label: 'nav.channels.webChat', href: '#web-chat' },
    ],
  },
  {
    label: 'nav.resources',
    href: '#resources', 
    children: [
      { label: 'nav.resources.blog', href: '#blog' },
      { label: 'nav.resources.guides', href: '#guides' },
      { label: 'nav.resources.helpCenter', href: '#help-center' },
    ],
  },
  { label: 'nav.docs', href: '#docs' },
  { label: 'nav.pricing', href: '#pricing' },
];

export const LOGO_TEXT = "Nexus";
