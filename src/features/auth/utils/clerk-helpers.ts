/**
 * Helper functions for working with Clerk user metadata
 * Following KISS principle - simple functions for common user data operations
 */

interface ClerkUser {
  id: string;
  firstName?: string | null;
  lastName?: string | null;
  fullName?: string | null;
  primaryEmailAddress?: {
    emailAddress?: string;
  } | null;
  imageUrl?: string;
  unsafeMetadata?: {
    bio?: string;
    language?: string;
    customName?: string;
    [key: string]: any;
  };
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Get user's preferred language from metadata (single source of truth: unsafeMetadata.language)
 */
export function getUserLanguage(user: ClerkUser | null | undefined): string {
  return user?.unsafeMetadata?.language || 'en';
}

/**
 * Get user's bio from metadata
 */
export function getUserBio(user: ClerkUser | null | undefined): string {
  return user?.unsafeMetadata?.bio || '';
}

/**
 * Get user's custom display name from metadata
 */
export function getUserCustomName(user: ClerkUser | null | undefined): string | null {
  return user?.unsafeMetadata?.customName || null;
}

/**
 * Get user's preferred display name (custom name, full name, or first name)
 */
export function getUserPreferredName(user: ClerkUser | null | undefined): string {
  if (!user) return 'User';
  
  // Try custom name first
  const customName = getUserCustomName(user);
  if (customName) return customName;
  
  // Try full name
  if (user.fullName) return user.fullName;
  
  // Try first name
  if (user.firstName) return user.firstName;
  
  // Fallback to email username
  const email = user.primaryEmailAddress?.emailAddress;
  if (email) {
    return email.split('@')[0];
  }
  
  return 'User';
}

/**
 * Get user's initials for avatar
 */
export function getUserInitials(user: ClerkUser | null | undefined): string {
  if (!user) return 'UN';
  
  // Try first and last name
  if (user.firstName && user.lastName) {
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  }
  
  // Try full name
  if (user.fullName) {
    const names = user.fullName.trim().split(' ');
    const first = names[0]?.[0] || '';
    const last = names.length > 1 ? names[names.length - 1]?.[0] || '' : '';
    return `${first}${last}`.toUpperCase().substring(0, 2);
  }
  
  // Try first name only
  if (user.firstName) {
    return user.firstName.substring(0, 2).toUpperCase();
  }
  
  // Fallback to email
  const email = user.primaryEmailAddress?.emailAddress;
  if (email) {
    return email.substring(0, 2).toUpperCase();
  }
  
  return 'UN';
}
