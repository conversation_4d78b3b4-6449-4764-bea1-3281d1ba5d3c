"use client";

import { SignUp } from "@clerk/nextjs";

/**
 * Simplified register form using Clerk's built-in SignUp component
 * Replaces 337 lines of custom code with Clerk's optimized UI
 */
export function RegisterForm() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignUp 
        routing="hash"
        signInUrl="/login"
        fallbackRedirectUrl="/dashboard"
        forceRedirectUrl="/dashboard"
        appearance={{
          elements: {
            formButtonPrimary: "bg-primary text-primary-foreground hover:bg-primary/90",
            card: "shadow-lg",
          }
        }}
      />
    </div>
  );
}

export default RegisterForm;
