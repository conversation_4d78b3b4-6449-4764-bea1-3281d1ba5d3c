'use client';

import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface OnboardingGuardProps {
  children: React.ReactNode;
  locale: string;
  fallbackComponent?: React.ReactNode;
}

/**
 * Onboarding Guard Component
 * Ensures users have completed onboarding before accessing protected content
 * This provides client-side verification in addition to middleware protection
 */
export function OnboardingGuard({ 
  children, 
  locale, 
  fallbackComponent 
}: OnboardingGuardProps) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!isLoaded) {
      // Still loading user data
      return;
    }

    if (!user) {
      // User not authenticated - redirect to login
      console.log('[OnboardingGuard] User not authenticated, redirecting to login');
      router.push(`/${locale}/login`);
      return;
    }

    // Check onboarding completion using unsafeMetadata (following Clerk recommendation)
    // "Unsafe metadata is the only metadata property that can be set during sign-up, 
    // so a common use case is to use it in custom onboarding flows."
    // Source: https://clerk.com/docs/users/metadata#unsafe-metadata
    const onboardingComplete = user.unsafeMetadata?.['onboardingComplete'] as boolean;
    
    if (!onboardingComplete) {
      console.log('[OnboardingGuard] User has not completed onboarding, redirecting');
      router.push(`/${locale}/onboarding`);
      return;
    }

    // User is authenticated and has completed onboarding
    console.log('[OnboardingGuard] User verified, allowing access');
    setIsChecking(false);
  }, [user, isLoaded, router, locale]);

  // Show loading state while checking
  if (!isLoaded || isChecking) {
    return fallbackComponent || <OnboardingGuardLoading />;
  }

  // Show loading if user is not available (shouldn't happen due to useEffect)
  if (!user) {
    return fallbackComponent || <OnboardingGuardLoading />;
  }

  // Check onboarding completion one more time before rendering
  const onboardingComplete = user.unsafeMetadata?.['onboardingComplete'] as boolean;
  if (!onboardingComplete) {
    return fallbackComponent || <OnboardingGuardLoading />;
  }

  // User is verified, render protected content
  return <>{children}</>;
}

/**
 * Default loading component for onboarding guard
 */
function OnboardingGuardLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-gray-900">
            Verifying your account...
          </h2>
          <p className="text-gray-600">
            Please wait while we check your setup status.
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * Hook for checking onboarding status
 * Useful for conditional rendering based on onboarding completion
 */
export function useOnboardingStatus() {
  const { user, isLoaded } = useUser();

  const isOnboardingComplete = user?.unsafeMetadata?.['onboardingComplete'] as boolean;
  
  return {
    isLoaded,
    isOnboardingComplete: isOnboardingComplete || false,
    user,
  };
}