'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';

interface ConditionalOnboardingGuardProps {
  children: React.ReactNode;
  locale: string;
  fallbackComponent?: React.ReactNode;
}

/**
 * Conditional Onboarding Guard Component
 * Only redirects to onboarding if user is NOT already on the onboarding page
 * This prevents infinite redirect loops
 */
export function ConditionalOnboardingGuard({ 
  children, 
  locale, 
  fallbackComponent 
}: ConditionalOnboardingGuardProps) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    if (!isLoaded) {
      // Still loading user data
      return;
    }

    if (!user) {
      // User not authenticated - redirect to login
      console.log('[ConditionalOnboardingGuard] User not authenticated, redirecting to login');
      router.push(`/${locale}/login`);
      return;
    }

    // Check if we're on the onboarding page - if so, don't redirect
    const isOnOnboardingPage = pathname.includes('/onboarding');
    
    if (isOnOnboardingPage) {
      console.log('[ConditionalOnboardingGuard] On onboarding page, allowing access');
      setIsChecking(false);
      return;
    }

    // Check onboarding completion for non-onboarding pages
    const onboardingComplete = user.unsafeMetadata?.['onboardingComplete'] as boolean;
    
    if (!onboardingComplete) {
      console.log('[ConditionalOnboardingGuard] User has not completed onboarding, redirecting from:', pathname);
      router.push(`/${locale}/onboarding`);
      return;
    }

    // User is authenticated and has completed onboarding
    console.log('[ConditionalOnboardingGuard] User verified, allowing access');
    setIsChecking(false);
  }, [user, isLoaded, router, locale, pathname]);

  // Show loading state while checking
  if (!isLoaded || isChecking) {
    return fallbackComponent || <ConditionalOnboardingGuardLoading />;
  }

  // Show loading if user is not available (shouldn't happen due to useEffect)
  if (!user) {
    return fallbackComponent || <ConditionalOnboardingGuardLoading />;
  }

  // If we're on onboarding page, always render (let the page handle its own logic)
  const isOnOnboardingPage = pathname.includes('/onboarding');
  if (isOnOnboardingPage) {
    return <>{children}</>;
  }

  // Check onboarding completion one more time before rendering non-onboarding pages
  const onboardingComplete = user.unsafeMetadata?.['onboardingComplete'] as boolean;
  if (!onboardingComplete) {
    return fallbackComponent || <ConditionalOnboardingGuardLoading />;
  }

  // User is verified, render protected content
  return <>{children}</>;
}

/**
 * Default loading component for conditional onboarding guard
 */
function ConditionalOnboardingGuardLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-gray-900">
            Verifying your account...
          </h2>
          <p className="text-gray-600">
            Please wait while we check your setup status.
          </p>
        </div>
      </div>
    </div>
  );
}
