'use client'

import React from 'react'
import { UserButton } from '@clerk/nextjs'
import { Settings, Globe } from 'lucide-react'
import { type Appearance } from '@clerk/types'
import {
  CustomMetadataFormNative,
  LanguageSettingsFormNative
} from '@/features/dashboard/components/dashboard-user-menu'
import { useI18n } from '@/hooks/use-i18n'

type UserButtonContext = 'homepage' | 'mobile' | 'dashboard'

interface UserButtonWithCustomPagesProps {
  appearance?: Appearance
  context: UserButtonContext
  className?: string
  showCustomPages?: boolean
}

/**
 * DRY UserButton component with custom pages
 * Single source of truth for all UserButton configurations
 * Context-aware rendering based on where it's used
 */
export function UserButtonWithCustomPages({
  appearance,
  context,
  className,
  showCustomPages = true
}: UserButtonWithCustomPagesProps) {
  const { t } = useI18n()

  // All contexts now render identically - no context-specific differences
  return (
    <div className={className}>
      <UserButton appearance={appearance}>
        {/* Same menu items for all contexts */}
        <UserButton.MenuItems>
          <UserButton.Action label="manageAccount" />
          <UserButton.Action label="signOut" />
        </UserButton.MenuItems>

        {/* Same custom pages for all contexts - now with translations */}
        {showCustomPages && (
          <UserButton.UserProfilePage
            label={t('dashboard:userProfile.pages.preferences.label')}
            labelIcon={<Settings className="w-4 h-4" />}
            url="preferences"
          >
            <PreferencesPageContent />
          </UserButton.UserProfilePage>
        )}

        {showCustomPages && (
          <UserButton.UserProfilePage
            label={t('dashboard:userProfile.pages.language.label')}
            labelIcon={<Globe className="w-4 h-4" />}
            url="language"
          >
            <LanguagePageContent />
          </UserButton.UserProfilePage>
        )}
      </UserButton>
    </div>
  )
}

/**
 * Preferences page content - same for all contexts with translations
 */
function PreferencesPageContent() {
  const { t } = useI18n()

  // All contexts now get the full dashboard functionality with translations
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">{t('dashboard:userProfile.pages.preferences.title')}</h2>
        <p className="text-sm text-muted-foreground">{t('dashboard:userProfile.pages.preferences.description')}</p>
      </div>
      <CustomMetadataFormNative />
    </div>
  )
}

/**
 * Language page content - same for all contexts with translations
 */
function LanguagePageContent() {
  const { t } = useI18n()

  // All contexts now get the full dashboard functionality with translations
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">{t('dashboard:userProfile.pages.language.title')}</h2>
        <p className="text-sm text-muted-foreground">{t('dashboard:userProfile.pages.language.description')}</p>
      </div>
      <LanguageSettingsFormNative />
    </div>
  )
}
