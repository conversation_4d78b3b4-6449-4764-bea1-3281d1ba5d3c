'use client';

import { useUser } from '@clerk/nextjs';
import { useEffect } from 'react';

/**
 * Debug component to see what Clerk user object contains
 * This will help us understand the data structure for Google OAuth users
 */
export function ClerkUserDebugger() {
  const { user, isLoaded } = useUser();

  useEffect(() => {
    if (isLoaded && user) {
      console.log('🔍 CLERK USER DEBUG:', {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        fullName: user.fullName,
        emailAddresses: user.emailAddresses,
        primaryEmailAddress: user.primaryEmailAddress,
        externalAccounts: user.externalAccounts,
        publicMetadata: user.publicMetadata,
        privateMetadata: user.privateMetadata,
        unsafeMetadata: user.unsafeMetadata,
        imageUrl: user.imageUrl,
        hasImage: user.hasImage,
        username: user.username,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        fullUser: user
      });
    }
  }, [user, isLoaded]);

  if (!isLoaded) {
    return <div>Loading user debug info...</div>;
  }

  if (!user) {
    return <div>No user found</div>;
  }

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="font-bold mb-2">🔍 Clerk User Debug Info:</h3>
      <div className="space-y-2 text-sm">
        <div><strong>ID:</strong> {user.id}</div>
        <div><strong>Full Name:</strong> {user.fullName || 'null'}</div>
        <div><strong>First Name:</strong> {user.firstName || 'null'}</div>
        <div><strong>Last Name:</strong> {user.lastName || 'null'}</div>
        <div><strong>Primary Email:</strong> {user.primaryEmailAddress?.emailAddress || 'null'}</div>
        <div><strong>Username:</strong> {user.username || 'null'}</div>
        <div><strong>Image URL:</strong> {user.imageUrl || 'null'}</div>
        <div><strong>Has Image:</strong> {user.hasImage ? 'true' : 'false'}</div>
        <div><strong>External Accounts:</strong> {user.externalAccounts?.length || 0}</div>
        {user.externalAccounts?.map((account, i) => (
          <div key={i} className="ml-4">
            <strong>Account {i + 1}:</strong> {account.provider} - {account.emailAddress}
          </div>
        ))}
      </div>
    </div>
  );
}
