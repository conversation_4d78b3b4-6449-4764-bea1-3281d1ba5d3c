"use client";

import { SignIn } from "@clerk/nextjs";

/**
 * Simplified login form using Clerk's built-in SignIn component
 * Replaces 326 lines of custom code with ~10 lines using Clerk's optimized UI
 */
export function LoginForm() {
  return (
    <div className="flex items-center justify-center min-h-screen">
      <SignIn 
        routing="hash"
        signUpUrl="/register"
        fallbackRedirectUrl="/dashboard"
        forceRedirectUrl="/dashboard"
        appearance={{
          elements: {
            formButtonPrimary: "bg-primary text-primary-foreground hover:bg-primary/90",
            card: "shadow-lg",
          }
        }}
      />
    </div>
  );
}

export default LoginForm;
