'use client';

import { useUser } from '@clerk/nextjs';
import {
  createContext,
  useContext,
  useEffect,
  useState,
  type ReactNode,
} from 'react';
import * as Sentry from '@sentry/nextjs';

interface ClerkAuthSessionContextType {
  user: any | null;
  isLoading: boolean;
  error: Error | null;
}

const ClerkAuthSessionContext = createContext<ClerkAuthSessionContextType | undefined>(
  undefined,
);

export const ClerkAuthSessionProvider = ({
  children,
  preloadedUser = null
}: {
  children: ReactNode;
  preloadedUser?: any | null;
}) => {
  const { user: clerkUser, isLoaded, isSignedIn } = useUser();
  const [user, setUser] = useState<any | null>(preloadedUser);
  const [isLoading, setIsLoading] = useState<boolean>(!preloadedUser && !isLoaded);
  const [error, setError] = useState<Error | null>(null);
  
  // Conditional logging for development only
  const shouldLog = process.env.NODE_ENV === 'development' && 
                    process.env.NEXT_PUBLIC_DEBUG_AUTH === 'true';
  
  useEffect(() => {
    if (isLoaded) {
      setIsLoading(false);
      
      if (isSignedIn && clerkUser) {
        setUser(clerkUser);
        setError(null);
        
        if (shouldLog) {
          console.log('[ClerkAuthSessionProvider] User signed in:', clerkUser.id);
        }
        
        // Set user context with masked email for privacy
        Sentry.setUser({ 
          id: clerkUser.id, 
          email: clerkUser.emailAddresses?.[0]?.emailAddress ? 
            `${clerkUser.emailAddresses[0].emailAddress.substring(0, 3)}***` : undefined 
        });
        
        Sentry.captureMessage('User signed in via Clerk', {
          level: 'info',
          tags: { component: 'ClerkAuthSessionProvider' },
          extra: { 
            userId: `${clerkUser.id.substring(0, 6)}...`,
            operation: 'userStateChange'
          }
        });
      } else {
        setUser(null);
        setError(null);
        
        if (shouldLog) {
          console.log('[ClerkAuthSessionProvider] User signed out or not authenticated');
        }
        
        Sentry.setUser(null);
        Sentry.captureMessage('User signed out via Clerk', {
          level: 'info',
          tags: { component: 'ClerkAuthSessionProvider' },
          extra: { operation: 'userStateChange' }
        });
      }
    }
  }, [isLoaded, isSignedIn, clerkUser, shouldLog]);
  
  // Handle loading state when Clerk is not yet loaded
  useEffect(() => {
    if (!isLoaded && !preloadedUser) {
      setIsLoading(true);
    }
  }, [isLoaded, preloadedUser]);
  
  // Handle errors from Clerk
  useEffect(() => {
    try {
      // If Clerk fails to load after a reasonable time, set an error
      if (!isLoaded) {
        const timeout = setTimeout(() => {
          if (!isLoaded) {
            const timeoutError = new Error('Clerk failed to load within expected time');
            setError(timeoutError);
            setIsLoading(false);
            
            if (shouldLog) {
              console.error('[ClerkAuthSessionProvider] Clerk load timeout');
            }
            
            Sentry.captureException(timeoutError, {
              tags: { component: 'ClerkAuthSessionProvider', type: 'loadTimeout' },
              extra: { 
                operation: 'clerkLoadTimeout',
                message: 'Clerk failed to load within expected time'
              }
            });
          }
        }, 10000); // 10 second timeout
        
        return () => clearTimeout(timeout);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error(String(err));
      
      if (shouldLog) {
        console.error('[ClerkAuthSessionProvider] Critical error:', error.message);
      }
      setError(error);
      setIsLoading(false);
      
      Sentry.captureException(error, {
        tags: { component: 'ClerkAuthSessionProvider', type: 'criticalError' },
        extra: { 
          operation: 'clerkError',
          message: 'Critical error in Clerk auth session provider'
        }
      });
    }
  }, [isLoaded, shouldLog]);
  
  return (
    <ClerkAuthSessionContext.Provider value={{ user, isLoading, error }}>
      {children}
    </ClerkAuthSessionContext.Provider>
  );
};

export const useClerkAuthSession = (): ClerkAuthSessionContextType => {
  const context = useContext(ClerkAuthSessionContext);
  if (context === undefined) {
    throw new Error('useClerkAuthSession must be used within a ClerkAuthSessionProvider');
  }
  return context;
};