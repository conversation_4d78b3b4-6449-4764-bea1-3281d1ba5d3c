'use server';

import { currentUser } from '@clerk/nextjs/server';
import { getServerLogger } from '@/lib/logger';
import { cache } from 'react';

const logger = getServerLogger('ClerkAuthService');

/**
 * Cached version of user profile retrieval to prevent redundant calls
 * Uses React's cache() to deduplicate calls within the same request
 */
const getCachedUserProfile = cache(async () => {
  try {
    const user = await currentUser();
    
    if (user) {
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      logger.info('Clerk user profile retrieved successfully', {
        userId: maskedUserId,
        operation: 'getCachedUserProfile'
      });
      
      return {
        success: true,
        user,
        error: null
      };
    } else {
      logger.info('No Clerk user session found', {
        operation: 'getCachedUserProfile'
      });
      
      return {
        success: false,
        user: null,
        error: null
      };
    }
  } catch (error) {
    // Handle static generation errors gracefully
    if (error instanceof Error && (error.message.includes('Dynamic server usage') || error.message.includes('cookies'))) {
      logger.info('Static generation detected in Clerk auth service', {
        operation: 'getCachedUserProfile'
      });
      return {
        success: false,
        user: null,
        error
      };
    }

    logger.error('Error retrieving Clerk user profile', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      operation: 'getCachedUserProfile'
    });
    
    return {
      success: false,
      user: null,
      error: error instanceof Error ? error : new Error(String(error))
    };
  }
});

/**
 * Get the current authenticated user profile from Clerk
 * Optimized with request-level caching to prevent redundant calls
 * 
 * @returns {Promise<{success: boolean, user: any | null, error: Error | null}>}
 */
export async function getCurrentUserProfile() {
  return getCachedUserProfile();
}

/**
 * Check if user is authenticated
 * @returns {Promise<boolean>}
 */
export async function isAuthenticated(): Promise<boolean> {
  try {
    const user = await currentUser();
    return !!user;
  } catch (error) {
    // Handle static generation errors gracefully
    if (error instanceof Error && (error.message.includes('Dynamic server usage') || error.message.includes('cookies'))) {
      logger.info('Static generation detected in authentication check', {
        operation: 'isAuthenticated'
      });
      return false;
    }

    logger.error('Error checking authentication status', {
      error: error instanceof Error ? error.message : String(error),
      operation: 'isAuthenticated'
    });
    return false;
  }
}

/**
 * Get user authentication status with detailed information
 * @returns {Promise<{isAuthenticated: boolean, user: any | null, error: Error | null}>}
 */
export async function getAuthenticationStatus() {
  try {
    const user = await currentUser();
    
    if (user) {
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      logger.info('Authentication status check - user authenticated', {
        userId: maskedUserId,
        operation: 'getAuthenticationStatus'
      });
      
      return {
        isAuthenticated: true,
        user,
        error: null
      };
    } else {
      logger.info('Authentication status check - no user session', {
        operation: 'getAuthenticationStatus'
      });
      
      return {
        isAuthenticated: false,
        user: null,
        error: null
      };
    }
  } catch (error) {
    // Handle static generation errors gracefully
    if (error instanceof Error && (error.message.includes('Dynamic server usage') || error.message.includes('cookies'))) {
      logger.info('Static generation detected in authentication status check', {
        operation: 'getAuthenticationStatus'
      });
      return {
        isAuthenticated: false,
        user: null,
        error
      };
    }

    logger.error('Error checking authentication status', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      operation: 'getAuthenticationStatus'
    });
    
    return {
      isAuthenticated: false,
      user: null,
      error: error instanceof Error ? error : new Error(String(error))
    };
  }
}

/**
 * TypeScript interfaces for Clerk auth service
 */
export interface ClerkAuthResult {
  success: boolean;
  user: any | null;
  error: Error | null;
}

export interface ClerkAuthStatus {
  isAuthenticated: boolean;
  user: any | null;
  error: Error | null;
}