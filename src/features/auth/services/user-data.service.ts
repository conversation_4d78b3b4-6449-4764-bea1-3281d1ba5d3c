'use client';

import { UserResource } from '@clerk/types';

export interface UserDataUpdate {
  firstName?: string;
  lastName?: string;
  language?: string;
  bio?: string;
  onboardingComplete?: boolean;
  onboardingCompletedAt?: string;
  onboardingSteps?: string[];
  secondaryEmailAdded?: boolean;
  secondaryEmailVerified?: boolean;
  [key: string]: any;
}

export interface UserDataServiceResponse {
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Centralized User Data Service
 * 
 * Provides consistent patterns for updating user data in Clerk
 * Handles both client-side and server-side scenarios
 * Ensures proper token refresh and data synchronization
 */
export class UserDataService {
  
  /**
   * Client-side user data update
   * Use this when updating from client components (forms, profile pages, etc.)
   * 
   * @param user - Clerk UserResource from useUser()
   * @param updates - Data to update
   * @param options - Additional options
   */
  static async updateUserDataClient(
    user: UserResource,
    updates: UserDataUpdate,
    options: {
      updateBasicInfo?: boolean;
      forceReload?: boolean;
      additionalMetadata?: Record<string, any>;
    } = {}
  ): Promise<UserDataServiceResponse> {
    try {
      if (!user) {
        throw new Error('User not found');
      }

      console.log('🔍 UserDataService (Client): Updating user data...', Object.keys(updates));

      // Update basic user info if requested
      if (options.updateBasicInfo && (updates.firstName || updates.lastName)) {
        await user.update({
          firstName: updates.firstName,
          lastName: updates.lastName,
        });
        console.log('✅ UserDataService (Client): Basic info updated');
      }

      // Prepare metadata update
      const metadataUpdate = {
        ...user.unsafeMetadata,
        ...updates,
        ...options.additionalMetadata,
        updatedAt: new Date().toISOString(),
      };

      // Remove basic info from metadata (shouldn't be in metadata)
      delete metadataUpdate.firstName;
      delete metadataUpdate.lastName;

      // Update metadata
      await user.update({
        unsafeMetadata: metadataUpdate,
      });

      console.log('✅ UserDataService (Client): Metadata updated successfully');

      // Force reload if requested (important for critical updates)
      if (options.forceReload) {
        await user.reload();
        console.log('✅ UserDataService (Client): User data reloaded');
      }

      return { success: true, data: metadataUpdate };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ UserDataService (Client): Failed to update user data:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Server-side user data update
   * Use this when updating from server actions, API routes, or middleware
   * 
   * Note: After server-side updates, client must call refreshUserDataClient()
   * 
   * @param userId - User ID from auth()
   * @param updates - Data to update
   * @param options - Additional options
   */
  static async updateUserDataServer(
    userId: string,
    updates: UserDataUpdate,
    options: {
      updateBasicInfo?: boolean;
      additionalMetadata?: Record<string, any>;
    } = {}
  ): Promise<UserDataServiceResponse> {
    try {
      const { clerkClient } = await import('@clerk/nextjs/server');
      const client = await clerkClient();

      console.log('🔍 UserDataService (Server): Updating user data...', Object.keys(updates));

      // Get current user to preserve existing metadata
      const currentUser = await client.users.getUser(userId);

      // Update basic user info if requested
      if (options.updateBasicInfo && (updates.firstName || updates.lastName)) {
        await client.users.updateUser(userId, {
          firstName: updates.firstName,
          lastName: updates.lastName,
        });
        console.log('✅ UserDataService (Server): Basic info updated');
      }

      // Prepare metadata update
      const metadataUpdate = {
        ...currentUser.unsafeMetadata,
        ...updates,
        ...options.additionalMetadata,
        updatedAt: new Date().toISOString(),
      };

      // Remove basic info from metadata (shouldn't be in metadata)
      delete metadataUpdate.firstName;
      delete metadataUpdate.lastName;

      // Update metadata
      await client.users.updateUserMetadata(userId, {
        unsafeMetadata: metadataUpdate,
      });

      console.log('✅ UserDataService (Server): Metadata updated successfully');

      return { success: true, data: metadataUpdate };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ UserDataService (Server): Failed to update user data:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Refresh user data on client after server-side updates
   * 
   * CRITICAL: Call this after any server-side user data updates
   * to ensure the client has the latest data
   * 
   * @param user - Clerk UserResource from useUser()
   * @param waitTime - Time to wait for server propagation (ms)
   */
  static async refreshUserDataClient(
    user: UserResource,
    waitTime: number = 1000
  ): Promise<UserDataServiceResponse> {
    try {
      if (!user) {
        throw new Error('User not found');
      }

      console.log('🔄 UserDataService: Refreshing client user data...');

      // Force user data refresh
      await user.reload();

      // Wait for server propagation
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }

      console.log('✅ UserDataService: Client user data refreshed');

      return { success: true };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('❌ UserDataService: Failed to refresh user data:', errorMessage);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * Get user language preference
   * Centralized way to extract language from user metadata
   */
  static getUserLanguage(user: any): string {
    return user?.unsafeMetadata?.language || 'en';
  }

  /**
   * Check if user has completed onboarding
   * Centralized way to check onboarding status
   */
  static hasCompletedOnboarding(user: any): boolean {
    return user?.unsafeMetadata?.onboardingComplete === true;
  }

  // Removed getUserDisplayName - using Clerk's built-in functionality
}
