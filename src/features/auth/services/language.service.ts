'use client'

import { User } from '@clerk/nextjs/server'
import { setLanguageCookie } from '@/features/auth/actions/cookie.actions'

export type SupportedLanguage = 'en' | 'pt' | 'es'

export interface LanguageOption {
  value: SupportedLanguage
  label: string
  flag: string
}

/**
 * Unified language service - Single source of truth for language handling
 * Uses unsafeMetadata.language as the canonical storage location
 */
export class LanguageService {
  // Standardized language options (en, pt, es only)
  static readonly LANGUAGE_OPTIONS: LanguageOption[] = [
    { value: 'en', label: 'English', flag: '🇺🇸' },
    { value: 'pt', label: 'Português', flag: '🇵🇹' },
    { value: 'es', label: 'Español', flag: '🇪🇸' },
  ]

  static readonly DEFAULT_LANGUAGE: SupportedLanguage = 'en'

  /**
   * Get user's language preference from unsafeMetadata.language
   */
  static getUserLanguage(user: any): SupportedLanguage {
    const language = user?.unsafeMetadata?.language as string
    return this.isValidLanguage(language) ? language : this.DEFAULT_LANGUAGE
  }

  /**
   * Check if a language code is supported
   */
  static isValidLanguage(language: string): language is SupportedLanguage {
    return ['en', 'pt', 'es'].includes(language)
  }

  /**
   * Get language option by code
   */
  static getLanguageOption(code: string): LanguageOption | undefined {
    return this.LANGUAGE_OPTIONS.find(option => option.value === code)
  }

  /**
   * Get language display name
   */
  static getLanguageName(code: string): string {
    const option = this.getLanguageOption(code)
    return option?.label || code
  }

  /**
   * Update user language preference and sync cookie
   * Used by both onboarding and profile forms
   */
  static async updateUserLanguage(
    user: any,
    language: SupportedLanguage,
    additionalMetadata: Record<string, any> = {}
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (!user) {
        throw new Error('User not found')
      }

      if (!this.isValidLanguage(language)) {
        throw new Error(`Invalid language: ${language}`)
      }

      console.log(`🔍 LanguageService: Updating language to ${language}`)

      // Update user with language in unsafeMetadata (single source of truth)
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          language, // Single source of truth
          updatedAt: new Date().toISOString(),
          ...additionalMetadata,
        },
      })

      console.log('✅ LanguageService: User metadata updated successfully')

      // Sync cookie using existing action
      const cookieResult = await setLanguageCookie(language)
      if (!cookieResult.success) {
        console.warn('⚠️ LanguageService: Failed to set language cookie:', cookieResult.error)
        // Don't fail the entire operation since the main update succeeded
      } else {
        console.log('✅ LanguageService: Language cookie synced successfully')
      }

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error('❌ LanguageService: Failed to update language:', errorMessage)
      return { success: false, error: errorMessage }
    }
  }
}