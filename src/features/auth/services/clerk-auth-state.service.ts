'use server';

import { currentUser } from '@clerk/nextjs/server';
import { getServerLogger } from '@/lib/logger';
import { cache } from 'react';

const logger = getServerLogger('ClerkAuthStateService');

export type ClerkAuthStateResult = {
  user: any | null;
  error?: Error;
};

/**
 * Cached version of Clerk auth state retrieval to prevent redundant calls
 * Uses React's cache() to deduplicate calls within the same request
 * Enhanced with clock skew resilience
 */
const getCachedClerkAuthState = cache(async (): Promise<ClerkAuthStateResult> => {
  try {
    const user = await currentUser();
    
    // Log successful auth state retrieval with masked user ID (only once per request)
    if (user?.id) {
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      logger.info('Clerk auth state retrieved successfully', {
        userId: maskedUserId,
        operation: 'getServerClerkAuthState'
      });
    } else {
      logger.info('Clerk auth state retrieved - no user session', {
        operation: 'getServerClerkAuthState'
      });
    }
    
    return { user };
  } catch (err) {
    // Handle static generation errors gracefully
    if (err instanceof Error && (err.message.includes('Dynamic server usage') || err.message.includes('cookies'))) {
      // This is expected during static generation - don't log as error
      logger.info('Static generation detected in Clerk auth state service', {
        operation: 'getServerClerkAuthState'
      });
      return { user: null, error: err };
    }

    // Handle clock skew errors gracefully without logging as critical
    if (err instanceof Error && (
      err.message.includes('token-not-active-yet') || 
      err.message.includes('nbf') || 
      err.message.includes('clock skew')
    )) {
      logger.info('Clock skew detected in auth state service - returning null user', {
        operation: 'getServerClerkAuthState',
        error: err.message
      });
      return { user: null, error: err };
    }

    // Log other errors as critical
    logger.error('Critical error in Clerk auth state service', {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      operation: 'getServerClerkAuthState'
    });
    return { user: null, error: err instanceof Error ? err : new Error(String(err)) };
  }
});

/**
 * Single source of truth for server-side Clerk authentication state
 * Used by layout, middleware, and page components
 * 
 * Optimized with request-level caching to prevent redundant calls during streaming:
 * - Uses React cache() to deduplicate auth calls within the same request
 * - Reduces API load during component re-renders
 * - Maintains logging for the first call only
 * 
 * Note: All warn/error level logs are automatically sent to Sentry
 * via the SentryWinstonTransport configured in winston.config.ts
 */
export async function getServerClerkAuthState(): Promise<ClerkAuthStateResult> {
  return getCachedClerkAuthState();
}