import type { Appearance } from '@clerk/types';

/**
 * Clerk appearance configuration that matches the application's theme
 * Uses CSS custom properties to automatically adapt to light/dark themes
 */
export function getClerkAppearance(): Appearance {
  return {
    variables: {
      // Primary colors - using CSS variables for theme adaptation
      colorPrimary: 'hsl(var(--primary))',
      colorPrimaryForeground: 'hsl(var(--primary-foreground))',

      // Background colors - will adapt to light/dark automatically
      colorBackground: 'hsl(var(--background))',
      colorForeground: 'hsl(var(--foreground))',

      // Card and surface colors
      colorMuted: 'hsl(var(--muted))',
      colorMutedForeground: 'hsl(var(--muted-foreground))',

      // Input colors
      colorInput: 'hsl(var(--input))',
      colorInputForeground: 'hsl(var(--foreground))',

      // Border and neutral colors
      colorBorder: 'hsl(var(--border))',
      colorNeutral: 'hsl(var(--border))',

      // State colors
      colorDanger: 'hsl(var(--destructive))',
      colorSuccess: 'hsl(var(--chart-1))',
      colorWarning: 'hsl(var(--chart-2))',

      // Focus and interaction
      colorRing: 'hsl(var(--ring))',

      // Typography - using CSS variables
      fontFamily: 'var(--font-sans)',
      fontSize: '0.875rem',
      fontWeight: {
        normal: 400,
        medium: 500,
        semibold: 600,
        bold: 700
      },

      // Layout
      borderRadius: 'var(--radius)',
      spacing: '1rem'
    },

    // Component-specific styling using CSS variables for theme adaptation
    elements: {
      // Main container
      card: {
        backgroundColor: 'hsl(var(--card))',
        borderColor: 'hsl(var(--border))',
        boxShadow: 'var(--shadow-lg)'
      },

      // Header styling
      headerTitle: {
        color: 'hsl(var(--foreground))',
        fontFamily: 'var(--font-sans)'
      },

      headerSubtitle: {
        color: 'hsl(var(--muted-foreground))',
        fontFamily: 'var(--font-sans)'
      },

      // Form elements
      formButtonPrimary: {
        backgroundColor: 'hsl(var(--primary))',
        color: 'hsl(var(--primary-foreground))',
        borderRadius: 'var(--radius)',
        fontFamily: 'var(--font-sans)',
        fontWeight: '500',
        '&:hover': {
          backgroundColor: 'hsl(var(--primary) / 0.9)'
        }
      },

      // OAuth/Social buttons styling
      socialButtonsBlockButton: {
        backgroundColor: 'hsl(var(--card))',
        borderColor: 'hsl(var(--border))',
        color: 'hsl(var(--foreground))',
        borderRadius: 'var(--radius)',
        fontFamily: 'var(--font-sans)',
        fontWeight: '500',
        border: '1px solid hsl(var(--border))',
        boxShadow: 'var(--shadow-sm)',
        '&:hover': {
          backgroundColor: 'hsl(var(--accent))',
          borderColor: 'hsl(var(--border))',
          boxShadow: 'var(--shadow-md)'
        },
        '&:focus': {
          borderColor: 'hsl(var(--ring))',
          boxShadow: '0 0 0 2px hsl(var(--ring) / 0.2)'
        }
      },

      socialButtonsBlockButtonText: {
        color: 'hsl(var(--foreground))',
        fontFamily: 'var(--font-sans)',
        fontWeight: '500'
      },

      // Alternative OAuth button styling
      formButtonSecondary: {
        backgroundColor: 'hsl(var(--card))',
        borderColor: 'hsl(var(--border))',
        color: 'hsl(var(--foreground))',
        borderRadius: 'var(--radius)',
        fontFamily: 'var(--font-sans)',
        fontWeight: '500',
        border: '1px solid hsl(var(--border))',
        boxShadow: 'var(--shadow-sm)',
        '&:hover': {
          backgroundColor: 'hsl(var(--accent))',
          borderColor: 'hsl(var(--border))',
          boxShadow: 'var(--shadow-md)'
        }
      },

      formFieldInput: {
        backgroundColor: 'hsl(var(--input))',
        borderColor: 'hsl(var(--border))',
        color: 'hsl(var(--foreground))',
        borderRadius: 'var(--radius)',
        fontFamily: 'var(--font-sans)',
        '&:focus': {
          borderColor: 'hsl(var(--ring))',
          boxShadow: '0 0 0 2px hsl(var(--ring) / 0.2)'
        }
      },

      // Navigation and tabs
      navbar: {
        backgroundColor: 'hsl(var(--card))',
        borderColor: 'hsl(var(--border))'
      },

      navbarButton: {
        color: 'hsl(var(--muted-foreground))',
        fontFamily: 'var(--font-sans)',
        '&:hover': {
          color: 'hsl(var(--foreground))',
          backgroundColor: 'hsl(var(--accent))'
        },
        '&[data-active]': {
          color: 'hsl(var(--primary))',
          backgroundColor: 'hsl(var(--primary) / 0.1)'
        }
      },

      // Profile page specific
      profileSectionPrimaryButton: {
        backgroundColor: 'hsl(var(--primary))',
        color: 'hsl(var(--primary-foreground))',
        borderRadius: 'var(--radius)',
        fontFamily: 'var(--font-sans)',
        '&:hover': {
          backgroundColor: 'hsl(var(--primary) / 0.9)'
        }
      },

      // Modal and overlay - allow default backdrop to show
      modalBackdrop: {
        backgroundColor: 'hsl(var(--background) / 0.8)',
        backdropFilter: 'blur(4px)'
      },

      // Allow Clerk's default close button to show
      modalCloseButton: {
        color: 'hsl(var(--muted-foreground))',
        '&:hover': {
          color: 'hsl(var(--foreground))'
        }
      },

      // Footer
      footer: {
        backgroundColor: 'hsl(var(--card))',
        borderColor: 'hsl(var(--border))'
      },

      // Social buttons container
      socialButtonsBlockButtonArrow: {
        display: 'none'
      },

      // Divider styling
      dividerLine: {
        backgroundColor: 'hsl(var(--border))'
      },

      dividerText: {
        color: 'hsl(var(--muted-foreground))',
        fontFamily: 'var(--font-sans)'
      },

      // Alternative button styling for better visibility
      button: {
        '&[data-variant="outline"]': {
          backgroundColor: 'hsl(var(--background))',
          borderColor: 'hsl(var(--border))',
          color: 'hsl(var(--foreground))',
          border: '1px solid hsl(var(--border))',
          boxShadow: 'var(--shadow-sm)',
          '&:hover': {
            backgroundColor: 'hsl(var(--accent))',
            borderColor: 'hsl(var(--border))'
          }
        }
      }
    }
  };
}

