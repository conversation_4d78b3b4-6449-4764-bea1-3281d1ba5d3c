import { ptBR, esES } from '@clerk/localizations';

/**
 * Get appropriate Clerk localization based on locale
 * Follows KISS principle - simple mapping without over-engineering
 */
export function getClerkLocalization(locale: string) {
  switch (locale) {
    case 'pt':
      return ptBR;
    case 'es':
      return esES;
    case 'en':
    default:
      return undefined; // English is Clerk's default
  }
}
