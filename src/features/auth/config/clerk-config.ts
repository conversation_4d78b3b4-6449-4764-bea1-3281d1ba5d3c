/**
 * Dynamic Clerk configuration based on locale
 * Handles environment variables that need to be locale-aware
 */

import { type Locale } from '../../i18n-config';

export const getClerkUrls = (locale: Locale = 'en') => {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || '';
  
  return {
    signInUrl: `/${locale}/login`,
    signUpUrl: `/${locale}/register`,
    signInFallbackRedirectUrl: `/${locale}/dashboard`,
    signUpFallbackRedirectUrl: `/${locale}/onboarding`,
  };
};

/**
 * Get the default locale from environment or fallback to 'en'
 */
export const getDefaultLocale = (): Locale => {
  // Check if there's a preferred locale in environment
  const envLocale = process.env['NEXT_PUBLIC_DEFAULT_LOCALE'] as Locale;
  return envLocale && ['en', 'pt', 'es'].includes(envLocale) ? envLocale : 'en';
};

/**
 * Update Clerk environment variables at runtime
 * This is useful for development and testing
 */
export const updateClerkEnvironmentUrls = (locale: Locale = 'en') => {
  if (typeof window !== 'undefined') {
    // Client-side: Can't modify process.env, but can provide URLs for components
    return getClerkUrls(locale);
  }
  
  // Server-side: Log current configuration
  const urls = getClerkUrls(locale);
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Clerk Config] Using URLs for locale '${locale}':`, urls);
  }
  
  return urls;
};
