'use server';

import { currentUser, auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { getServerLogger } from '@/lib/logger';

const logger = getServerLogger('ClerkAuthActions');

/**
 * Represents the state returned by authentication server actions.
 * @property {boolean} success - Indicates if the action was successful.
 * @property {string | null} message - A message describing the result of the action.
 * @property {Record<string, string> | null} [errorFields] - Optional. A record of field-specific error messages.
 * @property {string} [redirectTo] - Optional. The URL to redirect to on successful authentication.
 * @property {any} [user] - Optional. The user object returned on successful authentication.
 */
interface AuthActionState {
  success: boolean;
  message: string | null;
  errorFields?: Record<string, string> | null;
  redirectTo?: string;
  user?: any;
}

/**
 * Server Action to get the current authenticated user from Clerk
 * @returns {Promise<AuthActionState>} The current user state
 */
export async function getCurrentUser(): Promise<AuthActionState> {
  try {
    const user = await currentUser();
    
    if (user) {
      const maskedUserId = `${user.id.substring(0, 6)}...`;
      logger.info('Current user retrieved successfully', {
        userId: maskedUserId,
        operation: 'getCurrentUser'
      });
      
      return {
        success: true,
        message: 'User retrieved successfully',
        user
      };
    } else {
      logger.info('No current user session found', {
        operation: 'getCurrentUser'
      });
      
      return {
        success: false,
        message: 'No user session found',
        user: null
      };
    }
  } catch (error) {
    logger.error('Error retrieving current user', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      operation: 'getCurrentUser'
    });
    
    return {
      success: false,
      message: 'Failed to retrieve user',
      user: null
    };
  }
}

/**
 * Server Action to get the current authentication session from Clerk
 * @returns {Promise<AuthActionState>} The current auth session state
 */
export async function getAuthSession(): Promise<AuthActionState> {
  try {
    const { userId, sessionId } = await auth();
    
    if (userId && sessionId) {
      const maskedUserId = `${userId.substring(0, 6)}...`;
      const maskedSessionId = `${sessionId.substring(0, 6)}...`;
      
      logger.info('Auth session retrieved successfully', {
        userId: maskedUserId,
        sessionId: maskedSessionId,
        operation: 'getAuthSession'
      });
      
      return {
        success: true,
        message: 'Session retrieved successfully',
        user: { userId, sessionId }
      };
    } else {
      logger.info('No active auth session found', {
        operation: 'getAuthSession'
      });
      
      return {
        success: false,
        message: 'No active session found',
        user: null
      };
    }
  } catch (error) {
    logger.error('Error retrieving auth session', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      operation: 'getAuthSession'
    });
    
    return {
      success: false,
      message: 'Failed to retrieve session',
      user: null
    };
  }
}

/**
 * Server Action to sign out the current user
 * Redirects to login page after successful sign out
 */
export async function signOutAction(): Promise<void> {
  try {
    logger.info('Sign out initiated', {
      operation: 'signOutAction'
    });
    
    // Clerk handles sign out through client-side hooks
    // Server actions redirect to sign-in page
    redirect('/login');
  } catch (error) {
    logger.error('Error during sign out', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      operation: 'signOutAction'
    });
    
    // Still redirect to login even if there's an error
    redirect('/login');
  }
}