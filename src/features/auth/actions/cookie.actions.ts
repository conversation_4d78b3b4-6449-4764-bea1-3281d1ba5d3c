'use server';

import { cookies } from 'next/headers';

export const setLanguageCookie = async (language: string) => {
  try {
    const cookieStore = await cookies();
    cookieStore.set('locale', language, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
    });
    return { success: true };
  } catch (error) {
    console.error('Error setting language cookie:', error);
    return { success: false, error: 'Failed to set language cookie' };
  }
};
