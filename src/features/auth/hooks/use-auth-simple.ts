'use client';

import { useUser } from '@clerk/nextjs';

interface AuthState {
  user: any | null;
  profile: any | null | undefined;
  authUser: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  profileError: Error | null;
  retry: () => void;
  clearErrors: () => void;
}

/**
 * Simplified Clerk authentication hook
 * Replaces the over-engineered 252-line version with KISS principles
 * Uses Clerk user metadata as profile data (no external profile service needed)
 */
export function useAuth(): AuthState {
  const { user: clerkUser, isLoaded } = useUser();

  const isAuthenticated = !!clerkUser && isLoaded;
  const isLoading = !isLoaded;

  // Use Clerk user metadata as profile data
  const profile = clerkUser?.unsafeMetadata || null;

  // Create combined user object
  const authUser = clerkUser ? { ...clerkUser, ...profile } : null;

  return {
    user: clerkUser,
    profile,
    authUser,
    isAuthenticated,
    isLoading,
    error: null, // <PERSON> handles errors internally
    profileError: null, // No external profile service, no profile errors
    retry: () => {}, // No external queries to retry
    clearErrors: () => {} // Not needed with this simple approach
  };
}
