import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';

/**
 * Server-side onboarding guard
 * Checks onboarding completion on the server and redirects if needed
 * Use this in layouts and server components for immediate protection
 * 
 * Session token structure: { "metadata": { "public": "{{user.public_metadata}}", "unsafe": "{{user.unsafe_metadata}}" } }
 */
export async function checkOnboardingCompletion(locale: string) {
  try {
    const { userId, sessionClaims } = await auth();

    // If user is not authenticated, let auth routes handle it
    if (!userId) {
      console.log('[ServerOnboardingGuard] No authenticated user');
      return { isAuthenticated: false, isOnboardingComplete: false };
    }

    // Check onboarding completion from nested unsafe metadata
    const onboardingComplete = (sessionClaims as any)?.metadata?.unsafe?.onboardingComplete;

    if (!onboardingComplete) {
      console.log('[ServerOnboardingGuard] User has not completed onboarding, redirecting');
      console.log('[ServerOnboardingGuard] Session claims analysis:', {
        hasMetadata: !!sessionClaims?.metadata,
        hasUnsafeMetadata: !!(sessionClaims as any)?.metadata?.unsafe,
        onboardingComplete,
        structure: 'sessionClaims.metadata.unsafe.onboardingComplete'
      });
      redirect(`/${locale}/onboarding`);
    }

    console.log('[ServerOnboardingGuard] User verified and onboarding complete');
    return { isAuthenticated: true, isOnboardingComplete: true };

  } catch (error) {
    console.error('[ServerOnboardingGuard] Error checking onboarding status:', error);
    
    // On error, redirect to onboarding to be safe
    redirect(`/${locale}/onboarding`);
  }
}

/**
 * Server-side onboarding status check without redirect
 * Use this when you need to check status but handle the redirect yourself
 * Session token structure: { "metadata": { "public": "{{user.public_metadata}}", "unsafe": "{{user.unsafe_metadata}}" } }
 */
export async function getOnboardingStatus() {
  try {
    const { userId, sessionClaims } = await auth();

    if (!userId) {
      return { isAuthenticated: false, isOnboardingComplete: false };
    }

    // Check onboarding completion from nested unsafe metadata
    const onboardingComplete = (sessionClaims as any)?.metadata?.unsafe?.onboardingComplete;

    return { 
      isAuthenticated: true, 
      isOnboardingComplete: !!onboardingComplete 
    };

  } catch (error) {
    console.error('[ServerOnboardingGuard] Error getting onboarding status:', error);
    return { isAuthenticated: false, isOnboardingComplete: false };
  }
}