/**
 * Enhanced middleware for static rendering optimization
 * Handles ALL locale detection and redirects while keeping layouts static
 * Following Next.js App Router best practices for i18n
 */

import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

import { match as matchLocale } from "@formatjs/intl-localematcher";
import Negotiator from "negotiator";

// Locale configuration
const i18n = {
  locales: ["en", "pt", "es"],
  defaultLocale: "en",
} as const;

type SupportedLocale = typeof i18n.locales[number];

// Define all routes that should be publicly accessible
// Clerk will automatically protect all other routes
const isPublicRoute = createRouteMatcher([
  // Root and locale paths
  '/',
  '/(en|pt|es)',

  // Authentication routes
  '/(en|pt|es)/login(.*)',
  '/(en|pt|es)/register(.*)',
  '/(en|pt|es)/auth/(.*)',
  '/(en|pt|es)/sign-in(.*)',
  '/(en|pt|es)/sign-up(.*)',

  // Public pages (none currently needed - Clerk handles auth flows)

  // API routes that should be public
  '/api/webhooks/(.*)',

  // Static assets and Next.js internals (handled by matcher config)
]);

/**
 * Enhanced locale detection with user preference support
 * Detects locale from Accept-Language header, cookies, and user preferences
 * This is the ONLY place where dynamic locale detection happens
 */
function detectLocale(request: NextRequest): SupportedLocale {
  // 1. Check for explicit locale cookie (user preference)
  const cookieLocale = request.cookies.get('preferred-locale')?.value;
  if (cookieLocale && i18n.locales.includes(cookieLocale as SupportedLocale)) {
    return cookieLocale as SupportedLocale;
  }

  // 2. Check legacy cookie name for backward compatibility
  const legacyCookieLocale = request.cookies.get('locale')?.value;
  if (legacyCookieLocale && i18n.locales.includes(legacyCookieLocale as SupportedLocale)) {
    return legacyCookieLocale as SupportedLocale;
  }

  // 3. Use Accept-Language header with negotiator
  const negotiatorHeaders: Record<string, string> = {};
  request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

  const languages = new Negotiator({ headers: negotiatorHeaders }).languages(
    i18n.locales as readonly string[]
  );

  const detectedLocale = matchLocale(languages, i18n.locales, i18n.defaultLocale);
  return detectedLocale as SupportedLocale;
}

export default clerkMiddleware(async (auth, req: NextRequest) => {
  const pathname = req.nextUrl.pathname;

  // FIRST: Handle locale redirection (before auth checks)
  // This is the key to static generation - redirect happens in middleware only
  if (pathname === '/') {
    const locale = detectLocale(req);
    return NextResponse.redirect(new URL(`/${locale}`, req.url));
  }

  // Handle missing locale in paths (but not root and not API routes)
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // Don't redirect API routes - they should remain without locale prefixes
  const isApiRoute = pathname.startsWith('/api/');

  if (pathnameIsMissingLocale && !isApiRoute) {
    const locale = detectLocale(req);
    const sanitizedPathname = pathname.startsWith("/") ? pathname.substring(1) : pathname;
    const targetPath = sanitizedPathname || "";
    return NextResponse.redirect(new URL(`/${locale}/${targetPath}`, req.url));
  }

  // SECOND: Apply Clerk's authentication protection
  // Get auth state for protected routes
  const { userId, sessionClaims, redirectToSignIn } = await auth();

  // If the route is not public and user is not authenticated, redirect to sign-in
  if (!isPublicRoute(req) && !userId) {
    return redirectToSignIn({ returnBackUrl: req.url });
  }

  // Catch users who do not have `onboardingComplete: true` in their unsafeMetadata
  // Redirect them to the /onboarding route to complete onboarding
  if (userId && !(sessionClaims as any)?.metadata?.unsafe?.onboardingComplete) {
    // Allow access to onboarding page itself
    if (pathname.includes('/onboarding')) {
      return NextResponse.next();
    }

    const locale = pathname.split('/')[1] || i18n.defaultLocale;
    const onboardingUrl = new URL(`/${locale}/onboarding`, req.url);
    return NextResponse.redirect(onboardingUrl);
  }

  // CRITICAL: Do NOT set x-locale header here!
  // This would tempt future developers to use headers() in layouts,
  // which would break static generation. The locale is available
  // statically from the URL params in layouts and pages.

  // Allow all other requests to proceed
  return NextResponse.next();
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
