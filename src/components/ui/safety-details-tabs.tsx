import * as React from 'react';
import { cn } from '@/lib/utils';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Shield, Droplets, Sun, Baby, AlertTriangle } from 'lucide-react';
import { EnrichedEssentialOil } from '@/features/create-recipe/types/recipe.types';

interface SafetyDetailsTabsProps {
  oil: EnrichedEssentialOil;
  className?: string;
}

/**
 * Tabbed interface for detailed safety information
 * Organizes safety data into General, Pregnancy, and Children categories
 */
export function SafetyDetailsTabs({ oil, className }: SafetyDetailsTabsProps) {
  if (!oil.safety) return null;

  return (
    <div className={cn('w-full', className)}>
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="pregnancy">Pregnancy</TabsTrigger>
          <TabsTrigger value="children">Children</TabsTrigger>
        </TabsList>

        {/* General Tab */}
        <TabsContent value="general" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Internal Use */}
            {oil.safety.internal_use && (
              <div className="space-y-1">
                <div className="font-medium text-xs text-foreground flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  Internal Use
                </div>
                <div className="text-xs text-muted-foreground">
                  {oil.safety.internal_use.name || 'Unknown'}
                </div>
                {oil.safety.internal_use.description && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {oil.safety.internal_use.description}
                  </div>
                )}
              </div>
            )}

            {/* Dilution */}
            {oil.safety.dilution && (
              <div className="space-y-1">
                <div className="font-medium text-xs text-foreground flex items-center gap-1">
                  <Droplets className="h-3 w-3" />
                  Dilution
                </div>
                <div className="text-xs text-muted-foreground">
                  {oil.safety.dilution.name || 'Unknown'}
                  {(oil.safety.dilution.percentage_min || oil.safety.dilution.percentage_max) && (
                    <div className="mt-1">
                      <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full mb-1 relative overflow-hidden">
                        {/* Range bar showing min to max */}
                        <div 
                          className="h-2 bg-green-500 dark:bg-green-400 rounded-full transition-all duration-300 absolute"
                          style={{ 
                            left: `${(oil.safety.dilution.percentage_min || 0) * 100}%`,
                            width: `${((oil.safety.dilution.percentage_max || oil.safety.dilution.percentage_min || 0) - (oil.safety.dilution.percentage_min || 0)) * 100}%`
                          }}
                        />
                      </div>
                      <span className="text-xs">
                        {oil.safety.dilution.percentage_min ? `${(oil.safety.dilution.percentage_min * 100).toFixed(1)}` : ''}
                        {oil.safety.dilution.percentage_min && oil.safety.dilution.percentage_max ? '-' : ''}
                        {oil.safety.dilution.percentage_max ? `${(oil.safety.dilution.percentage_max * 100).toFixed(1)}` : ''}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Phototoxicity */}
            {oil.safety.phototoxicity && (
              <div className="space-y-1">
                <div className="font-medium text-xs text-foreground flex items-center gap-1">
                  <Sun className="h-3 w-3" />
                  Sun Exposure
                </div>
                <div className="text-xs text-muted-foreground">
                  {oil.safety.phototoxicity.status || 'Unknown'}
                </div>
                {oil.safety.phototoxicity.description && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {oil.safety.phototoxicity.description}
                  </div>
                )}
              </div>
            )}
          </div>
        </TabsContent>

        {/* Pregnancy Tab */}
        <TabsContent value="pregnancy" className="mt-4">
          <div className="space-y-3">
            {oil.safety.pregnancy_nursing && oil.safety.pregnancy_nursing.length > 0 ? (
              oil.safety.pregnancy_nursing.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className={cn(
                      'inline-flex items-center gap-1 text-xs font-medium',
                      getSafetyStatusColor(item.name || undefined)
                    )}>
                      {getSafetyIcon(item.name || undefined)}
                      {item.name || 'Unknown Status'}
                    </span>
                  </div>
                  {item.status_description && (
                    <div className="text-xs text-muted-foreground">
                      {item.status_description}
                    </div>
                  )}
                  {item.usage_guidance && (
                    <div className="text-xs text-muted-foreground font-medium p-2 bg-muted/50 rounded-md border border-muted">
                      {item.usage_guidance}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-xs text-muted-foreground">
                No pregnancy safety information available.
              </div>
            )}
          </div>
        </TabsContent>

        {/* Children Tab */}
        <TabsContent value="children" className="mt-4">
          <div className="space-y-3">
            {oil.safety.child_safety && oil.safety.child_safety.length > 0 ? (
              oil.safety.child_safety.map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className={cn(
                      'inline-flex items-center gap-1 text-xs font-medium',
                      getSafetyStatusColor(item.safety_notes || undefined)
                    )}>
                      {getSafetyIcon(item.safety_notes || undefined)}
                      {item.age_range || 'Age range not specified'}
                    </span>
                  </div>
                  {item.safety_notes && (
                    <div className="text-xs text-muted-foreground">
                      {item.safety_notes}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="text-xs text-muted-foreground">
                No child safety information available.
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Helper functions
function getSafetyStatusColor(status?: string): string {
  if (!status) return 'text-muted-foreground';
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('safe') || lowerStatus.includes('approved')) return 'text-green-600';
  if (lowerStatus.includes('avoid') || lowerStatus.includes('not recommended')) return 'text-red-600';
  if (lowerStatus.includes('caution') || lowerStatus.includes('with care')) return 'text-amber-600';
  return 'text-muted-foreground';
}

function getSafetyIcon(status?: string) {
  if (!status) return '❓';
  const lowerStatus = status.toLowerCase();
  if (lowerStatus.includes('safe') || lowerStatus.includes('approved')) return '✅';
  if (lowerStatus.includes('avoid') || lowerStatus.includes('not recommended')) return '❌';
  if (lowerStatus.includes('caution') || lowerStatus.includes('with care')) return '⚠️';
  return '❓';
}
