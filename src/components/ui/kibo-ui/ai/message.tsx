"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface AIMessageProps {
  children: React.ReactNode
  from?: "user" | "assistant" | "other"
  className?: string
}

export interface AIMessageContentProps {
  children: React.ReactNode
  className?: string
}

/**
 * AIMessage - Main container for chat messages with automatic alignment
 * - user messages: right-aligned with content-first layout
 * - assistant/other messages: left-aligned with avatar-first layout
 */
const AIMessage = React.forwardRef<HTMLDivElement, AIMessageProps>(
  ({ children, from = "assistant", className, ...props }, ref) => {
    const isUser = from === "user"
    
    return (
      <div
        ref={ref}
        className={cn(
          "flex gap-3 max-w-[85%] mb-4",
          isUser 
            ? "ml-auto flex-row-reverse" // User: right-aligned, content then avatar
            : "mr-auto flex-row", // Assistant/Other: left-aligned, avatar then content
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)
AIMessage.displayName = "AIMessage"

/**
 * AIMessageContent - Container for message text with automatic styling
 */
const AIMessageContent = React.forwardRef<HTMLDivElement, AIMessageContentProps>(
  ({ children, className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex-1 space-y-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
AIMessageContent.displayName = "AIMessageContent"

export { AIMessage, AIMessageContent }
