'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>,
  Loader2,
  Spark<PERSON>
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useAutoScroll } from '@/hooks/use-auto-scroll';
import { cn } from '@/lib/utils';
import { useI18n } from '@/hooks/use-i18n';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

interface StreamingItem {
  id?: string;
  title: string;
  subtitle?: string;
  description?: string;
  timestamp?: Date;
}

type ModalState = 'loading' | 'streaming' | 'completed' | 'error';

interface AIStreamingModalProps {
  isOpen: boolean;
  title: string;
  description: string;
  items: StreamingItem[];
  onClose?: () => void;
  className?: string;
  maxVisibleItems?: number;
  // Dynamic content configuration
  analysisType?: string; // e.g., "causes", "symptoms", "properties"
  terminalTitle?: string; // e.g., "Potential Causes Analysis", "Symptoms Analysis"
  terminalSubtitle?: string; // e.g., "Understanding factors...", "Identifying symptoms..."
  loadingMessage?: string; // e.g., "analyzing demographics...", "analyzing causes..."
  progressMessage?: string; // e.g., "Analyzing more potential causes..."
  // NEW: State management props
  modalState?: ModalState;
  isComplete?: boolean;
  error?: string | null;
  autoCloseDelay?: number; // in milliseconds, default 3000
}

/**
 * Loading state component using terminal-themed skeleton bars
 */
const LoadingState: React.FC<{ message: string }> = ({ message }) => (
  <div className="h-full flex flex-col justify-center items-center text-center space-y-4">
    <div className="w-full max-w-sm space-y-3">
      <div className="h-3 w-full bg-slate-700 rounded animate-pulse"></div>
      <div className="h-3 w-5/6 bg-slate-700 rounded animate-pulse"></div>
      <div className="h-3 w-3/4 bg-slate-700 rounded animate-pulse"></div>
    </div>
    <p className="text-green-400 mt-8 font-mono">
      <span className="text-muted-foreground">$</span> {message}
    </p>
  </div>
);

/**
 * Error state component with terminal styling
 */
const ErrorState: React.FC<{ error: string }> = ({ error }) => (
  <div className="space-y-3">
    <p className="text-red-400 font-bold font-mono">[ERROR] An unexpected error occurred during analysis.</p>
    <p className="text-muted-foreground font-mono">
      <strong className="text-foreground">Timestamp:</strong> {new Date().toISOString()}
    </p>
    <p className="text-muted-foreground font-mono">
      <strong className="text-foreground">Details:</strong> {error}
    </p>
    <p className="text-yellow-400 mt-4 font-mono">
      <span className="text-muted-foreground">$</span> Please try again...{' '}
      <span className="inline-block w-2 h-4 bg-yellow-400 animate-pulse ml-1"></span>
    </p>
  </div>
);

/**
 * Completion state component
 */
const CompletionState: React.FC<{ itemCount: number; analysisType: string }> = ({ itemCount, analysisType }) => {
  const { t } = useI18n();

  return (
    <p className="text-green-400 font-mono">
      <span className="text-muted-foreground">$</span>{' '}
      {t('create-recipe:streaming.status.complete', 'Analysis complete. Found {count} {type}.', {
        count: itemCount,
        type: analysisType
      })}
    </p>
  );
};

/**
 * AI Streaming Modal with chat-like auto-scroll behavior
 * 
 * Features:
 * - Modal overlay with backdrop blur
 * - Real-time streaming item display
 * - Auto-scroll to bottom (chat-style)
 * - Progress indication
 * - Smooth animations
 * - Professional enterprise feel
 */
export const AIStreamingModal: React.FC<AIStreamingModalProps> = ({
  isOpen,
  title,
  description,
  items,
  onClose,
  className,
  maxVisibleItems = 100,
  analysisType = "causes",
  terminalTitle,
  terminalSubtitle,
  loadingMessage,
  progressMessage,
  modalState,
  isComplete = false,
  error = null,
  autoCloseDelay = 3000
}) => {
  const { t } = useI18n();

  // Determine current state from props or infer from items/error
  const currentState: ModalState = React.useMemo(() => {
    if (modalState) return modalState;
    if (error) return 'error';
    if (isComplete) return 'completed';
    if (items.length === 0) return 'loading';
    return 'streaming';
  }, [modalState, error, isComplete, items.length]);

  // Auto-close timer for completed state
  const [autoCloseTimer, setAutoCloseTimer] = React.useState<number | null>(null);

  React.useEffect(() => {
    if (currentState === 'completed' && onClose && autoCloseDelay > 0) {
      const timer = window.setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      setAutoCloseTimer(Math.floor(autoCloseDelay / 1000));

      // Countdown timer
      const countdown = setInterval(() => {
        setAutoCloseTimer(prev => {
          if (prev === null || prev <= 1) {
            clearInterval(countdown);
            return null;
          }
          return prev - 1;
        });
      }, 1000);

      return () => {
        clearTimeout(timer);
        clearInterval(countdown);
        setAutoCloseTimer(null);
      };
    }

    return () => {
      // Cleanup function for when conditions aren't met
      setAutoCloseTimer(null);
    };
  }, [currentState, onClose, autoCloseDelay]);

  // Dynamic content based on analysis type
  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'symptoms':
        return {
          terminalTitle: t('create-recipe:streaming.terminal.potentialSymptomsAnalysis', 'Potential Symptoms Analysis'),
          terminalSubtitle: t('create-recipe:streaming.terminal.symptomsSubtitle', 'Identifying symptoms that may manifest based on your selected causes.'),
          loadingMessage: t('create-recipe:streaming.loading.analyzingCauses', 'analyzing selected causes...'),
          progressMessage: t('create-recipe:streaming.progress.analyzingMoreSymptoms', 'Analyzing more potential symptoms...')
        };
      case 'properties':
        return {
          terminalTitle: t('create-recipe:streaming.terminal.therapeuticPropertiesAnalysis', 'Therapeutic Properties Analysis'),
          terminalSubtitle: t('create-recipe:streaming.terminal.propertiesSubtitle', 'Finding therapeutic properties to address your symptoms.'),
          loadingMessage: t('create-recipe:streaming.loading.analyzingSymptoms', 'analyzing symptoms...'),
          progressMessage: t('create-recipe:streaming.progress.analyzingMoreProperties', 'Analyzing more therapeutic properties...')
        };
      case 'oils':
        return {
          terminalTitle: t('create-recipe:streaming.terminal.essentialOilsAnalysis', 'Essential Oils Analysis'),
          terminalSubtitle: t('create-recipe:streaming.terminal.oilsSubtitle', 'Recommending essential oils with the identified properties.'),
          loadingMessage: t('create-recipe:streaming.loading.analyzingProperties', 'analyzing properties...'),
          progressMessage: t('create-recipe:streaming.progress.analyzingMoreOils', 'Analyzing more essential oils...')
        };
      case 'causes':
      default:
        return {
          terminalTitle: t('create-recipe:streaming.terminal.potentialCausesAnalysis', 'Potential Causes Analysis'),
          terminalSubtitle: t('create-recipe:streaming.terminal.causesSubtitle', 'Understanding factors that may contribute to your symptoms.'),
          loadingMessage: t('create-recipe:streaming.loading.analyzingDemographics', 'analyzing demographics...'),
          progressMessage: t('create-recipe:streaming.progress.analyzingMoreCauses', 'Analyzing more potential causes...')
        };
    }
  };

  const defaultContent = getDefaultContent(analysisType);
  const finalTerminalTitle = terminalTitle || defaultContent.terminalTitle;
  const finalTerminalSubtitle = terminalSubtitle || defaultContent.terminalSubtitle;
  const finalLoadingMessage = loadingMessage || defaultContent.loadingMessage;
  const finalProgressMessage = progressMessage || defaultContent.progressMessage;
  // Use the new useAutoScroll API for DRY/source of truth
  const { scrollRef } = useAutoScroll({
    offset: 50,
    smooth: true,
    content: items.length
  });

  // Animated ellipsis hook
  const useAnimatedEllipsis = () => {
    const [ellipsis, setEllipsis] = useState('');

    useEffect(() => {
      const states = ['', ' .', ' ..', ' ...'];
      let index = 0;

      const interval = setInterval(() => {
        setEllipsis(states[index] || '');
        index = (index + 1) % states.length;
      }, 300);

      return () => clearInterval(interval);
    }, []);

    return ellipsis;
  };

  const streamingEllipsis = useAnimatedEllipsis();
  const footerEllipsis = useAnimatedEllipsis();

  const displayItems = items.slice(0, maxVisibleItems);

  // Debug logging
  React.useEffect(() => {
    console.log('🎬 Modal items update:', {
      totalItems: items.length,
      displayItems: displayItems.length,
      firstItem: items[0]?.title,
      lastItem: items[items.length - 1]?.title
    });
  }, [items.length, displayItems.length]);



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn("w-full max-w-2xl mx-auto", className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <div className="relative">
              <Brain className="h-6 w-6 text-primary" />
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full"
              />
            </div>
            <span>{title}</span>
            <Badge variant="secondary" className="text-sm font-medium ml-auto">
              <Sparkles className="h-3 w-3 mr-1" />
              {items.length} {t('create-recipe:streaming.found', 'found')}
            </Badge>
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="pt-0">
          <div className="relative">

            <div className="relative">
              {/* Terminal-like code block */}
              <div className="bg-slate-950 rounded-lg border border-slate-800 overflow-hidden">
                {/* Terminal header */}
                <div className="flex items-center justify-between px-4 py-2 bg-slate-900 border-b border-slate-800">
                  <div className="flex items-center space-x-2">
                    <div className={cn(
                      "h-3 w-3 rounded-full",
                      currentState === 'loading' && "bg-blue-500",
                      currentState === 'streaming' && "bg-green-500",
                      currentState === 'completed' && "bg-green-500",
                      currentState === 'error' && "bg-red-500"
                    )} />
                    <span className="text-sm font-mono text-green-500">
                      {t(`create-recipe:streaming.states.${currentState}`, currentState)}{streamingEllipsis}
                    </span>
                  </div>
                </div>

                {/* Terminal content */}
                <div
                  ref={scrollRef}
                  className="h-80 w-full overflow-y-auto scroll-smooth scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none'
                  }}
                >

                  <div className="p-2 font-mono text-xs leading-tight">
                    {currentState === 'loading' ? (
                      <LoadingState message={finalLoadingMessage} />
                    ) : currentState === 'error' ? (
                      <ErrorState error={error || 'Unknown error occurred'} />
                    ) : displayItems.length === 0 ? (
                      <LoadingState message={finalLoadingMessage} />
                    ) : (
                      <>
                        {/* Terminal header */}
                        <div className="text-cyan-400 mb-2">
                          <div className="text-sm font-bold"># {finalTerminalTitle}</div>
                          <div className="text-slate-400 text-xs">{finalTerminalSubtitle}</div>
                        </div>

                        {/* Progressive Item Reveal */}
                        {displayItems.map((item, index) => {
                          const stableKey = `item-${index}-${item.title?.slice(0, 20)}`;

                          return (
                            <div
                              key={stableKey}
                              className="mb-3 animate-in fade-in slide-in-from-bottom-2 duration-300"
                            >
                              {/* Cause title */}
                              <div className="text-yellow-400 font-bold mb-1">
                                ## {item.title}
                              </div>

                              {/* Cause suggestion */}
                              {item.subtitle && (
                                <div className="text-blue-300 mb-1 leading-tight">
                                  {item.subtitle}
                                </div>
                              )}

                              {/* Cause explanation */}
                              {item.description && (
                                <div className="text-slate-300 leading-tight mb-2">
                                  {item.description}
                                </div>
                              )}

                              {/* Separator line */}
                              {index < displayItems.length - 1 && (
                                <div className="border-t border-slate-700 my-2"></div>
                              )}
                            </div>
                          );
                        })}

                        {/* Terminal cursor and completion state */}
                        {currentState === 'completed' ? (
                          <CompletionState itemCount={displayItems.length} analysisType={analysisType} />
                        ) : (
                          <div className="flex items-center text-green-400 mt-1">
                            <span className="mr-1">$</span>
                            <div className="w-1 h-3 bg-green-400 animate-pulse"></div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>


            </div>

            {/* Progress indicator - only show during streaming state */}
            {currentState === 'streaming' && items.length > 0 && (
              <div className="mt-4 pt-3 border-t">
                <motion.div
                  initial={{ opacity: 0, y: 5 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center justify-center space-x-3 p-3 rounded-lg bg-primary/5 border border-primary/20"
                >
                  <Loader2 className="h-4 w-4 animate-spin text-primary" />
                  <div className="text-center">
                    <p className="text-sm font-medium text-primary">
                      {finalProgressMessage}
                    </p>
                    <div className="flex items-center justify-center text-sm text-muted-foreground">
                      <span>{t('create-recipe:streaming.status.aiProcessing', 'AI is processing your information to find additional insights')}</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}

            {/* Status footer */}
            <div className="mt-3 pt-3 border-t">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  {currentState === 'completed' && <div className="h-4 w-4 rounded-full bg-green-500 flex items-center justify-center"><div className="h-2 w-2 bg-white rounded-full"></div></div>}
                  {currentState === 'error' && <div className="h-4 w-4 rounded-full bg-red-500 flex items-center justify-center">!</div>}

                  <span>
                    {currentState === 'loading' && t('create-recipe:streaming.status.initializing', 'Initializing analysis engine...')}
                    {currentState === 'streaming' && `${t('create-recipe:streaming.status.liveAnalysis', 'Live analysis in progress')}${footerEllipsis}`}
                    {currentState === 'completed' && autoCloseTimer !== null &&
                      t('create-recipe:streaming.status.autoCloseMessage', 'This window will close automatically in {seconds} seconds', { seconds: autoCloseTimer })
                    }
                    {currentState === 'completed' && autoCloseTimer === null &&
                      t('create-recipe:streaming.status.analysisCompleteMessage', 'Analysis complete. You may now close this window.')
                    }
                    {currentState === 'error' && t('create-recipe:streaming.error', 'Analysis failed. Please try again.')}
                  </span>
                </div>
                {items.length > maxVisibleItems && (
                  <span className="text-xs">
                    {t('create-recipe:streaming.showingLatest', 'Showing latest {maxVisible} of {total}', { maxVisible: maxVisibleItems, total: items.length })}
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AIStreamingModal;
