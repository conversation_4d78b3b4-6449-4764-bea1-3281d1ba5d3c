'use client'

import React from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { LanguageService, type SupportedLanguage } from '@/features/auth/services/language.service'
import { cn } from '@/lib/utils'

interface LanguageSelectorProps {
  value?: SupportedLanguage
  onValueChange?: (value: SupportedLanguage) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  id?: string
  user?: any // Clerk user object for auto-population
}

/**
 * Unified Language Selector Component
 * Used by both onboarding and profile forms
 * Automatically pre-populates with user's current language preference
 */
export function LanguageSelector({
  value,
  onValueChange,
  placeholder = "Select your preferred language",
  disabled = false,
  className,
  id,
  user,
}: LanguageSelectorProps) {
  // Auto-populate with user's current language if no value provided
  const currentValue = value || (user ? LanguageService.getUserLanguage(user) : undefined)

  return (
    <Select 
      onValueChange={onValueChange} 
      value={currentValue} 
      disabled={disabled}
    >
      <SelectTrigger id={id} className={cn("w-full", className)}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="max-h-[300px]">
        {LanguageService.LANGUAGE_OPTIONS.map((option) => (
          <SelectItem
            key={option.value}
            value={option.value}
            className="cursor-pointer"
          >
            <div className="flex items-center gap-3">
              <span className="text-lg flex-shrink-0" role="img" aria-label={option.label}>
                {option.flag}
              </span>
              <span className="font-medium text-sm">{option.label}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}