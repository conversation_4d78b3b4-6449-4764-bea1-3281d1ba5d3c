import * as React from 'react';
import { cn } from '@/lib/utils';

interface RelevancyBadgeProps {
  /** The relevancy score (1-5) */
  score: number;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show the numeric score (default: true) */
  showNumber?: boolean;
}

/**
 * Displays a color-coded badge for a relevancy score (1-5).
 * Features a progress bar and numeric score with dark mode support.
 * @example <RelevancyBadge score={4.5} />
 */
export function RelevancyBadge({ 
  score, 
  className, 
  showNumber = true 
}: RelevancyBadgeProps) {
  const getColorScheme = (score: number) => {
    const roundedScore = Math.round(score);
    
    switch (roundedScore) {
      case 5: return { 
        bg: 'bg-gray-100 dark:bg-slate-800', 
        text: 'text-emerald-600 dark:text-emerald-400', 
        border: 'border-gray-300 dark:border-slate-600', 
        fill: 'bg-emerald-500 dark:bg-emerald-400' 
      };
      case 4: return { 
        bg: 'bg-gray-100 dark:bg-gray-800', 
        text: 'text-lime-500 dark:text-lime-400', 
        border: 'border-gray-300 dark:border-gray-600', 
        fill: 'bg-lime-500 dark:bg-lime-400' 
      };
      case 3: return { 
        bg: 'bg-gray-100 dark:bg-gray-800', 
        text: 'text-yellow-500 dark:text-yellow-400', 
        border: 'border-gray-300 dark:border-gray-600', 
        fill: 'bg-yellow-500 dark:bg-yellow-400' 
      };
      case 2: return { 
        bg: 'bg-gray-100 dark:bg-gray-800', 
        text: 'text-orange-500 dark:text-orange-400', 
        border: 'border-gray-300 dark:border-gray-600', 
        fill: 'bg-orange-500 dark:bg-orange-400' 
      };
      default: return { 
        bg: 'bg-gray-100 dark:bg-gray-800', 
        text: 'text-red-500 dark:text-red-400', 
        border: 'border-gray-300 dark:border-gray-600', 
        fill: 'bg-red-500 dark:bg-red-400' 
      };
    }
  };

  const colors = getColorScheme(score);
  const percentage = (score / 5) * 100;

  return (
    <div 
      className={cn(
        'inline-flex items-center gap-2 rounded-full border px-3 py-1.5 text-sm font-medium transition-colors',
        colors.bg,
        colors.border,
        className
      )}
      title={`Relevancy: ${score.toFixed(1)}/5`}
    >
      <div className="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div 
          className={cn('h-full rounded-full transition-all duration-300', colors.fill)}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showNumber && (
        <span className={cn('text-xs font-semibold', colors.text)}>
          {score.toFixed(1)}
        </span>
      )}
    </div>
  );
}
