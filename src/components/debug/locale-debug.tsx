'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

export function LocaleDebug() {
  const pathname = usePathname();

  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && pathname) {
      console.log('🌍 [Client] Locale Debug:', {
        pathname,
        extractedLocale: pathname.split('/')[1] || 'none',
        url: window.location.href,
        userAgent: navigator.userAgent.substring(0, 50) + '...',
        language: navigator.language,
        languages: navigator.languages,
        timestamp: new Date().toISOString()
      });

      // Fetch server-side headers to see what middleware is setting
      fetch('/api/debug-headers')
        .then(res => res.json())
        .then(data => {
          console.log('🔍 [Client] Server headers from middleware:', data);
        })
        .catch(err => {
          console.log('❌ [Client] Failed to fetch debug headers:', err);
        });
    }
  }, [pathname]);

  // Don't render anything visible
  return null;
}
