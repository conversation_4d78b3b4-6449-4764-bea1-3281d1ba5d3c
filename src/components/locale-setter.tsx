/**
 * Client component to set the HTML lang attribute after hydration
 * Prevents hydration mismatches while ensuring proper locale setting
 */

'use client';

import { useEffect } from 'react';

interface LocaleSetterProps {
  locale: string;
}

export function LocaleSetter({ locale }: LocaleSetterProps) {
  useEffect(() => {
    // Set the HTML lang attribute after hydration to avoid mismatches
    if (typeof document !== 'undefined') {
      document.documentElement.lang = locale;
    }
  }, [locale]);

  // This component renders nothing
  return null;
}
