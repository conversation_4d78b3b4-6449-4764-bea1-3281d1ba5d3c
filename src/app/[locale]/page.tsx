/**
 * Localized homepage with server-only i18n - SEO optimized static generation
 * <PERSON><PERSON> moved to client-side for perfect SEO and performance
 */

import { getServerLogger } from '@/lib/logger';
import {
  createTranslatorWithUserPreference,
  getOptimalLocale,
  getAllTranslationsWithUserPreference
} from '@/lib/i18n/server';
import { HomepageLayoutWithI18n } from '@/features/homepage/layout/homepage-layout';
import { Metadata } from 'next';

const logger = getServerLogger('LocalizedHomepage');

interface LocalizedHomepageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: LocalizedHomepageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = await createTranslatorWithUserPreference('homepage', locale);

  return {
    title: t('hero.title') + ' ' + t('hero.rotatingWords.0'),
    description: t('hero.subtitle'),
    alternates: {
      languages: {
        'en': '/en',
        'pt': '/pt',
        'es': '/es',
      },
    },
  };
}

/**
 * SEO-optimized localized homepage - 100% static generation
 * Only i18n server-side, auth handled client-side for perfect SEO
 */
export default async function LocalizedHomepage({ params }: LocalizedHomepageProps) {
  const { locale } = await params;

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('[LocalizedHomepage] Received params:', { locale });
    console.log('[LocalizedHomepage] About to call getOptimalLocale with:', locale);
  }

  try {
    // Get optimal locale (respects user preferences)
    const optimalLocale = await getOptimalLocale(locale);
    
    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[LocalizedHomepage] getOptimalLocale returned:', optimalLocale);
    }

    // Get translations for static generation - NO AUTH SERVER-SIDE
    const { translations } = await getAllTranslationsWithUserPreference(['homepage', 'common'], locale);

    logger.info('Localized homepage loaded (static)', {
      locale: optimalLocale,
      urlLocale: locale,
      operation: 'LocalizedHomepage'
    });

    // Return static layout without HydrationBoundary - auth handled client-side
    return (
      <HomepageLayoutWithI18n
        locale={optimalLocale}
        translations={translations}
      />
    );
  } catch (err) {
    logger.error('Critical error in localized homepage', {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      locale,
      operation: 'LocalizedHomepage'
    });

    // Fallback to basic layout without hydrated state
    const fallbackTranslations = await getAllTranslationsWithUserPreference(['homepage', 'common'], 'en');
    return (
      <HomepageLayoutWithI18n
        locale="en"
        translations={fallbackTranslations.translations}
      />
    );
  }
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
