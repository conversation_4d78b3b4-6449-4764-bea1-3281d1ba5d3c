/**
 * Debug/Test page for i18n and middleware functionality
 * Shows locale detection, headers, and middleware behavior
 */

import { headers } from 'next/headers';
import { getServerLogger } from '@/lib/logger';
import {
  createTranslatorWithUserPreference,
  getOptimalLocale,
} from '@/lib/i18n/server';

const logger = getServerLogger('TestPage');

interface TestPageProps {
  params: Promise<{ locale: string }>;
}

export default async function TestPage({ params }: TestPageProps) {
  const { locale } = await params;
  const headersList = await headers();
  
  // Get optimal locale and translator
  const optimalLocale = await getOptimalLocale(locale);
  const t = await createTranslatorWithUserPreference('common', locale);

  // Debug information
  const debugInfo = {
    receivedLocale: locale,
    optimalLocale: optimalLocale,
    headers: {
      'x-locale': headersList.get('x-locale'),
      'x-original-path': headersList.get('x-original-path'),
      'x-detected-locale': headersList.get('x-detected-locale'),
      'accept-language': headersList.get('accept-language'),
      'user-agent': headersList.get('user-agent'),
    },
    url: headersList.get('x-url') || 'not available',
    timestamp: new Date().toISOString(),
  };

  console.log('[TestPage] Debug info:', debugInfo);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🧪 i18n & Middleware Test Page
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Locale Information */}
            <div className="bg-blue-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-blue-900 mb-4">
                📍 Locale Detection
              </h2>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Received Locale:</span>{' '}
                  <code className="bg-blue-100 px-2 py-1 rounded">{locale}</code>
                </div>
                <div>
                  <span className="font-medium">Optimal Locale:</span>{' '}
                  <code className="bg-blue-100 px-2 py-1 rounded">{optimalLocale}</code>
                </div>
                <div>
                  <span className="font-medium">Test Translation:</span>{' '}
                  <span className="italic">"{t('loading') || 'Loading...'}"</span>
                </div>
              </div>
            </div>

            {/* Headers Information */}
            <div className="bg-green-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-green-900 mb-4">
                📡 Middleware Headers
              </h2>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">x-locale:</span>{' '}
                  <code className="bg-green-100 px-2 py-1 rounded">
                    {debugInfo.headers['x-locale'] || 'not set'}
                  </code>
                </div>
                <div>
                  <span className="font-medium">x-detected-locale:</span>{' '}
                  <code className="bg-green-100 px-2 py-1 rounded">
                    {debugInfo.headers['x-detected-locale'] || 'not set'}
                  </code>
                </div>
                <div>
                  <span className="font-medium">x-original-path:</span>{' '}
                  <code className="bg-green-100 px-2 py-1 rounded">
                    {debugInfo.headers['x-original-path'] || 'not set'}
                  </code>
                </div>
              </div>
            </div>

            {/* Browser Information */}
            <div className="bg-purple-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-purple-900 mb-4">
                🌐 Browser Detection
              </h2>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Accept-Language:</span>
                  <div className="mt-1">
                    <code className="bg-purple-100 px-2 py-1 rounded text-xs break-all">
                      {debugInfo.headers['accept-language'] || 'not available'}
                    </code>
                  </div>
                </div>
                <div>
                  <span className="font-medium">User-Agent:</span>
                  <div className="mt-1">
                    <code className="bg-purple-100 px-2 py-1 rounded text-xs break-all">
                      {debugInfo.headers['user-agent']?.substring(0, 100) || 'not available'}...
                    </code>
                  </div>
                </div>
              </div>
            </div>

            {/* Test Links */}
            <div className="bg-orange-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold text-orange-900 mb-4">
                🔗 Test Navigation
              </h2>
              <div className="space-y-3">
                <div>
                  <a 
                    href="/test" 
                    className="inline-block bg-orange-200 hover:bg-orange-300 px-3 py-2 rounded text-sm font-medium transition-colors"
                  >
                    /test → should redirect
                  </a>
                </div>
                <div>
                  <a 
                    href="/en/test" 
                    className="inline-block bg-orange-200 hover:bg-orange-300 px-3 py-2 rounded text-sm font-medium transition-colors"
                  >
                    /en/test → direct access
                  </a>
                </div>
                <div>
                  <a 
                    href="/pt/test" 
                    className="inline-block bg-orange-200 hover:bg-orange-300 px-3 py-2 rounded text-sm font-medium transition-colors"
                  >
                    /pt/test → direct access
                  </a>
                </div>
                <div>
                  <a 
                    href="/" 
                    className="inline-block bg-orange-200 hover:bg-orange-300 px-3 py-2 rounded text-sm font-medium transition-colors"
                  >
                    / → should redirect to locale
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Raw Debug JSON */}
          <div className="mt-8 bg-gray-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              🐛 Raw Debug Data
            </h2>
            <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>

          {/* Client-side debugging */}
          <div className="mt-8 bg-yellow-50 p-6 rounded-lg">
            <h2 className="text-xl font-semibold text-yellow-900 mb-4">
              💻 Client-side Info
            </h2>
            <div id="client-info" className="text-sm">
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                    document.addEventListener('DOMContentLoaded', function() {
                      const clientInfo = {
                        url: window.location.href,
                        pathname: window.location.pathname,
                        language: navigator.language,
                        languages: navigator.languages,
                        userAgent: navigator.userAgent.substring(0, 100),
                        cookie: document.cookie
                      };
                      document.getElementById('client-info').innerHTML = 
                        '<pre class="bg-yellow-100 p-4 rounded text-xs overflow-auto">' + 
                        JSON.stringify(clientInfo, null, 2) + 
                        '</pre>';
                    });
                  `
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
