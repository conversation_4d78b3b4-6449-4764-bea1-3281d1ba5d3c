import { SignUp } from '@clerk/nextjs';

interface Props {
  params: Promise<{ locale: string }>;
}

/**
 * KISS approach: Use Clerk's built-in SignUp component with catch-all routing
 * This allows Clerk's internal routing to work properly (e.g., /register/verify-email)
 */
export default async function RegisterPage({ params }: Props) {
  const { locale } = await params;

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <SignUp 
        routing="path"
        path={`/${locale}/register`}
        signInUrl={`/${locale}/login`}
        fallbackRedirectUrl={`/${locale}/onboarding`}
        signInFallbackRedirectUrl={`/${locale}/dashboard`}
      />
    </div>
  );
}