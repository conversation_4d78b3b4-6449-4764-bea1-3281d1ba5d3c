import { SignIn, GoogleOneTap } from '@clerk/nextjs';

interface Props {
  params: Promise<{ locale: string }>;
}

/**
 * KISS approach: Use Clerk's built-in SignIn component with catch-all routing
 * This allows Clerk's internal routing to work properly (e.g., /login/verify-email)
 */
export default async function LoginPage({ params }: Props) {
  const { locale } = await params;

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <GoogleOneTap 
        signInForceRedirectUrl={`/${locale}/dashboard`}
        signUpForceRedirectUrl={`/${locale}/onboarding`}
      />
      <SignIn 
        routing="path"
        path={`/${locale}/login`}
        signUpUrl={`/${locale}/register`}
        fallbackRedirectUrl={`/${locale}/dashboard`}
        signUpFallbackRedirectUrl={`/${locale}/onboarding`}
      />
    </div>
  );
}