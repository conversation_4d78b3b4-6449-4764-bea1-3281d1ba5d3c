
import React from 'react';

interface AuthLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

/**
 * KISS approach: Simple auth layout
 * Clerk components handle their own styling
 */
export default async function AuthLayout({ children, params }: AuthLayoutProps): Promise<JSX.Element> {
  const { locale } = await params;
  
  return (
    <div className="min-h-screen bg-gray-50">
      {children}
    </div>
  );
}
