/**
 * Locale-specific layout following template approach
 * Simple, clean, and focused on locale handling
 */

import { notFound } from 'next/navigation';
import { i18n, type Locale } from '../../../i18n-config';

interface Props {
  params: Promise<{ locale: string }>;
  children: React.ReactNode;
}

export default async function LocaleLayout({ params, children }: Props) {
  // Await the params first
  const { locale } = await params;
  
  // Validate locale
  if (!i18n.locales.includes(locale as Locale)) {
    notFound();
  }

  return (
    <html lang={locale}>
      <body>
        {children}
      </body>
    </html>
  );
}

export async function generateStaticParams() {
  return i18n.locales.map((locale) => ({ locale }));
}
