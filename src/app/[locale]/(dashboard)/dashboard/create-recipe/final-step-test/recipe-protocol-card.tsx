/**
 * @fileoverview Detailed recipe protocol card component
 * Shows complete recipe information with collapsible sections
 * Matches standalone-v1.html structure exactly
 */

'use client';

import React from 'react';
import { useI18n } from '@/hooks/use-i18n';

type RecipeTimeSlot = 'morning' | 'mid-day' | 'night';

// Local droplet color system using theme variables only
const DROPLET_COLORS = [
  'hsl(var(--primary))',
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(var(--accent))',
  'hsl(var(--secondary))',
  'hsl(var(--muted))',
  'hsl(var(--destructive))'
] as const;

const getDropletColor = (oilIndex: number): string => {
  return DROPLET_COLORS[oilIndex % DROPLET_COLORS.length];
};

interface EssentialOil {
  oil_id: string;
  name_localized: string;
  scientific_name: string;
  drops: number;
}

interface CarrierOil {
  name_localized: string;
  properties_localized: string;
}

interface UsageInstruction {
  method: string;
  description: string;
  frequency: string;
}

interface OilRationale {
  name_localized: string;
  properties: string[];
  rationale_localized: string;
}

interface Recipe {
  time_range_localized: string;
  application_type_localized: string;
  time_of_day_localized: string;
  recipe_theme_localized: string;
  formulation: {
    total_drops: number;
    dilution_percentage: number;
    bottle_size_ml: number;
    bottle_type_localized: string;
  };
  ingredients: {
    essential_oils: EssentialOil[];
    carrier_oil: {
      recommended: CarrierOil;
      alternative: CarrierOil;
    };
  };
  usage_instructions_localized: UsageInstruction[];
  preparation_steps_localized: string[];
  oil_rationales: OilRationale[];
  ritual_suggestion_localized: string;
}

interface RecipeProtocolCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: Recipe | null;
}

/**
 * Detailed recipe protocol card with collapsible sections
 * Follows the exact design from standalone-v1.html protocol cards
 */
export const RecipeProtocolCard = React.memo(function RecipeProtocolCard({ recipe }: RecipeProtocolCardProps) {
  const { t } = useI18n();

  if (!recipe) {
    return (
      <div className="w-full max-w-4xl bg-muted rounded-2xl p-8 animate-pulse border border-border">
        <div className="text-center text-muted-foreground">{t('create-recipe:steps.final-recipes.recipeCard.notAvailable')}</div>
      </div>
    );
  }

  return (
    <div className="protocol-card w-full max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col">
      {/* Time Badge */}
      <div className="time-badge flex flex-col items-center justify-center gap-1">
        <span className="font-bold">{recipe.time_range_localized}</span>
        <span className="inline-block bg-primary/10 text-primary text-xs font-semibold px-3 py-1 rounded-full">
          {recipe.application_type_localized}
        </span>
      </div>

      {/* Protocol Header */}
      <div className="protocol-header">
        <div className="protocol-title">{recipe.time_of_day_localized}</div>
        <div className="protocol-subtitle">{recipe.recipe_theme_localized}</div>
      </div>

      {/* Recipe Visual Section */}
      <div className="recipe-visual">
        {/* Droplet Visualizer with INDEX-BASED colors */}
        <div className="droplet-visualizer">
          {recipe.ingredients.essential_oils.map((oil, oilIndex) => (
            Array.from({ length: oil.drops }).map((_, dropIndex) => (
              <div
                key={`${oil.oil_id}-${dropIndex}`}
                className="droplet"
                style={{
                  backgroundColor: getDropletColor(oilIndex),
                  height: `${12 + Math.random() * 8}px`,
                  animationDelay: `${(oilIndex * oil.drops + dropIndex) * 0.1}s`
                }}
              />
            ))
          ))}
        </div>

        {/* Quick Info Grid */}
        <div className="quick-info grid grid-cols-4 gap-4 py-4">
          <div className="info-item">
            <div className="label text-xs text-muted-foreground font-normal uppercase tracking-wide">{t('create-recipe:steps.final-recipes.recipeCard.totalDrops')}</div>
            <div className="value text-lg font-bold text-foreground">{recipe.formulation.total_drops}</div>
          </div>
          <div className="info-item">
            <div className="label text-xs text-muted-foreground font-normal uppercase tracking-wide">{t('create-recipe:steps.final-recipes.recipeCard.dilution')}</div>
            <div className="value text-lg font-bold text-foreground">{recipe.formulation.dilution_percentage}%</div>
          </div>
          <div className="info-item">
            <div className="label text-xs text-muted-foreground font-normal uppercase tracking-wide">{t('create-recipe:steps.final-recipes.recipeCard.size')}</div>
            <div className="value text-lg font-bold text-foreground">{recipe.formulation.bottle_size_ml}ml</div>
          </div>
          <div className="info-item">
            <div className="label text-xs text-muted-foreground font-normal uppercase tracking-wide">{t('create-recipe:steps.final-recipes.recipeCard.dispenser')}</div>
            <div className="value text-lg font-bold text-foreground">{recipe.formulation.bottle_type_localized}</div>
          </div>
        </div>

        {/* Ingredients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Essential Oils */}
          <div>
            <h5 className="text-sm font-semibold text-foreground uppercase tracking-wide mb-2">{t('create-recipe:steps.final-recipes.recipeCard.essentialOils')}</h5>
            <div className="space-y-2 border-t border-border pt-2">
              {recipe.ingredients.essential_oils.map((oil) => (
                <div key={oil.oil_id} className="ingredient-item">
                  <div className="ingredient-badge">{oil.drops} {t('create-recipe:steps.final-recipes.recipeCard.drops')}</div>
                  <div className="ingredient-details">
                    <div className="name">{oil.name_localized}</div>
                    <div className="botanical">{oil.scientific_name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Carrier Oil */}
          <div className="carrier-oil-section">
            <h5 className="text-sm font-semibold text-foreground uppercase tracking-wide mb-2">{t('create-recipe:steps.final-recipes.recipeCard.carrierOil')}</h5>
            <div className="space-y-2 border-t border-border pt-2">
              <div className="bg-primary/10 p-3 rounded-lg border border-primary/20">
                <h4 className="font-bold text-primary">{t('create-recipe:steps.final-recipes.recipeCard.recommended')}</h4>
                <div className="name font-bold text-primary">{recipe.ingredients.carrier_oil.recommended.name_localized}</div>
                <div className="properties text-xs text-primary/70">{recipe.ingredients.carrier_oil.recommended.properties_localized}</div>
              </div>

              <div className="bg-secondary/10 p-3 rounded-lg border border-secondary/20">
                <h4 className="font-bold text-foreground">{t('create-recipe:steps.final-recipes.recipeCard.alternative')}</h4>
                <div className="name font-bold text-foreground">{recipe.ingredients.carrier_oil.alternative?.name_localized}</div>
                <div className="properties text-xs text-muted-foreground">{recipe.ingredients.carrier_oil.alternative?.properties_localized}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Collapsible Sections using HTML details/summary */}
      <div className="w-full">
        {/* Como Usar Section */}
        <details>
          <summary className="collapsible-strip flex justify-between items-center">
            <div className="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <div>
                <div className="title">{t('create-recipe:steps.final-recipes.recipeCard.sections.usage.title')}</div>
                <div className="subtitle">{t('create-recipe:steps.final-recipes.recipeCard.sections.usage.subtitle')}</div>
              </div>
            </div>
            <svg className="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </summary>
          <div className="collapsible-content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recipe.usage_instructions_localized.map((instruction, index) => (
                <div key={index} className="usage-item bg-muted/50 p-4 rounded-lg text-left">
                  <h5 className="font-semibold text-foreground">{instruction.method}</h5>
                  <p className="text-sm text-muted-foreground">{instruction.description}</p>
                  <span className="frequency-tag inline-block mt-2 text-xs font-medium bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                    {instruction.frequency}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </details>

        {/* Instruções de Preparo Section (Dynamic Checklist) */}
        <details>
          <summary className="collapsible-strip flex justify-between items-center">
            <div className="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              <div>
                <div className="title">{t('create-recipe:steps.final-recipes.recipeCard.sections.preparation.title')}</div>
                <div className="subtitle">{t('create-recipe:steps.final-recipes.recipeCard.sections.preparation.subtitle')}</div>
              </div>
            </div>
            <svg className="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </summary>
          <div className="collapsible-content">
            <div className="checklist">
              {recipe.preparation_steps_localized.map((step, index) => (
                <label key={index} className="checklist-item flex items-center gap-3 p-2 hover:bg-muted/50 rounded cursor-pointer">
                  <input type="checkbox" className="w-4 h-4 text-primary bg-background border-border rounded focus:ring-primary focus:ring-2" />
                  <span className="text-sm text-foreground">{step}</span>
                </label>
              ))}
            </div>
          </div>
        </details>

        {/* Como Funciona Section */}
        <details>
          <summary className="collapsible-strip flex justify-between items-center">
            <div className="flex items-center gap-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 20h9" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16.5 3.5a2.121 2.121 0 013 3L7 19.5 3 21l1.5-4L16.5 3.5z" />
              </svg>
              <div>
                <div className="title">{t('create-recipe:steps.final-recipes.recipeCard.sections.science.title')}</div>
                <div className="subtitle">{t('create-recipe:steps.final-recipes.recipeCard.sections.science.subtitle')}</div>
              </div>
            </div>
            <svg className="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </summary>
          <div className="collapsible-content">
            {recipe.oil_rationales.map((rationale, index) => (
              <div key={index} className="oil-rationale mb-4 p-4 bg-muted/30 rounded-lg">
                <h5 className="font-semibold text-foreground mb-2">{rationale.name_localized}</h5>
                <div className="properties-tags flex flex-wrap gap-1 mb-2">
                  {rationale.properties.map((property) => (
                    <span key={property} className="property-tag inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                      {property}
                    </span>
                  ))}
                </div>
                <p className="text-sm text-muted-foreground">{rationale.rationale_localized}</p>
              </div>
            ))}

            {/* Synergy section - use ritual_suggestion_localized as mentioned in gap analysis */}
            <div className="synergy-section mt-6 p-4 bg-primary/5 rounded-lg border border-primary/10">
              <h5 className="font-semibold text-primary mb-2">{t('create-recipe:steps.final-recipes.recipeCard.sections.science.rationale')}</h5>
              <p className="text-sm text-primary/90">{recipe.ritual_suggestion_localized}</p>
            </div>
          </div>
        </details>
      </div>
    </div>
  );
});