/**
 * @fileoverview Final recipes display mockup component
 * Mimics the UI/UX of standalone-v1.html using mock data for testing
 */

'use client';

import React, { useState, useCallback } from 'react';
import { useI18n } from '@/hooks/use-i18n';
import { mockFinalRecipes, mockUserData, mockEchoData } from './mock-data';
import { ProtocolSummaryCard } from './protocol-summary-card';
import { RecipeProtocolCard } from './recipe-protocol-card';
import { SafetyWarnings } from './safety-warnings';

/**
 * Tab types for navigation
 */
type TabType = 'overview' | 'recipes' | 'studies' | 'security';
type RecipeTimeSlot = 'morning' | 'mid-day' | 'night';

/**
 * Main final recipes display mockup component with tab navigation
 * Follows the exact structure from standalone-v1.html
 */
export function FinalRecipesDisplayMockup() {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [activeProtocol, setActiveProtocol] = useState<RecipeTimeSlot>('morning');
  const { t } = useI18n();

  // Switch between tabs
  const switchTab = (tab: TabType) => {
    setActiveTab(tab);
  };

  // Switch between protocol timelines in recipes tab
  const switchProtocol = (protocol: RecipeTimeSlot) => {
    setActiveProtocol(protocol);
  };

  // Switch to recipes tab and specific protocol
  const switchToRecipes = (protocol?: RecipeTimeSlot) => {
    setActiveTab('recipes');
    if (protocol) {
      setActiveProtocol(protocol);
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto">
      {/* Tab Navigation */}
      <nav className="tabs-nav sticky top-0 z-10 bg-background/80 backdrop-blur-sm border-b border-border mb-8">
        <div className="flex gap-6 px-4">
          <button
            className={`tab-button flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${activeTab === 'overview'
              ? 'border-primary text-primary bg-primary/5'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              }`}
            onClick={() => switchTab('overview')}
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {t('create-recipe:steps.final-recipes.tabs.overview')}
          </button>
          <button
            className={`tab-button flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${activeTab === 'recipes'
              ? 'border-primary text-primary bg-primary/5'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              }`}
            onClick={() => switchTab('recipes')}
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
            {t('create-recipe:steps.final-recipes.tabs.recipes')}
          </button>
          <button
            className={`tab-button flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${activeTab === 'studies'
              ? 'border-primary text-primary bg-primary/5'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              }`}
            onClick={() => switchTab('studies')}
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 20h9" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16.5 3.5a2.121 2.121 0 013 3L7 19.5 3 21l1.5-4L16.5 3.5z" />
            </svg>
            {t('create-recipe:steps.final-recipes.tabs.studies')}
          </button>
          <button
            className={`tab-button flex items-center gap-2 px-4 py-3 border-b-2 transition-colors ${activeTab === 'security'
              ? 'border-primary text-primary bg-primary/5'
              : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
              }`}
            onClick={() => switchTab('security')}
          >
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            {t('create-recipe:steps.final-recipes.tabs.security')}
          </button>
        </div>
      </nav>

      {/* Tab Content */}
      <main>
        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <OverviewTab
            healthConcern={mockUserData.healthConcern}
            demographics={mockUserData.demographics}
            selectedCauses={mockUserData.selectedCauses}
            selectedSymptoms={mockUserData.selectedSymptoms}
            finalRecipes={mockFinalRecipes}
            onSwitchToRecipes={switchToRecipes}
            t={t}
          />
        )}

        {/* Recipes Tab */}
        {activeTab === 'recipes' && (
          <RecipesTab
            activeProtocol={activeProtocol}
            onSwitchProtocol={switchProtocol}
            finalRecipes={mockFinalRecipes}
            t={t}
          />
        )}

        {/* Studies Tab */}
        {activeTab === 'studies' && (
          <StudiesTab t={t} />
        )}

        {/* Security Tab */}
        {activeTab === 'security' && (
          <SecurityTab
            demographics={mockUserData.demographics}
            finalRecipes={mockFinalRecipes}
            t={t}
          />
        )}
      </main>
    </div>
  );
}

/**
 * Overview tab component
 */
interface OverviewTabProps {
  healthConcern: any;
  demographics: any;
  selectedCauses: any[];
  selectedSymptoms: any[];
  finalRecipes: any;
  onSwitchToRecipes: (protocol?: RecipeTimeSlot) => void;
  t: (key: string, fallback?: string) => string;
}

function OverviewTab({
  healthConcern,
  demographics,
  selectedCauses,
  selectedSymptoms,
  finalRecipes,
  onSwitchToRecipes,
  t
}: OverviewTabProps) {
  const [flippedCard, setFlippedCard] = React.useState<string | null>(null);
  const generatedRecipes = [
    finalRecipes.morning.recipe,
    finalRecipes.midDay.recipe,
    finalRecipes.night.recipe
  ].filter(Boolean);

  // UUID lookup functions for causes and symptoms
  const getCauseNameById = useCallback((causeId: string) => {
    return selectedCauses.find(cause => cause.cause_id === causeId)?.cause_name || 'Causa desconhecida';
  }, [selectedCauses]);

  const getSymptomNameById = useCallback((symptomId: string) => {
    return selectedSymptoms.find(symptom => symptom.symptom_id === symptomId)?.symptom_name || 'Sintoma desconhecido';
  }, [selectedSymptoms]);

  // Extract echo data from first available recipe
  const echoData = React.useMemo(() => {
    return mockEchoData;
  }, []);

  const therapeuticStrategy = React.useMemo(() => {
    if (generatedRecipes.length === 0) return null;

    // Aggregate therapeutic properties from all recipes
    const allTherapeuticProperties = generatedRecipes.flatMap(r =>
      r.oil_rationales || []
    );
    const uniqueTherapeuticProperties = allTherapeuticProperties.filter((prop, index, self) =>
      index === self.findIndex(p => p.name_localized === prop.name_localized)
    );

    // Aggregate application methods
    const allApplicationMethods = generatedRecipes.map(r => r.application_type_localized).filter(Boolean);
    const uniqueApplicationMethods = Array.from(new Set(allApplicationMethods));

    const themes = generatedRecipes.map(r => r.recipe_theme_localized);
    const benefits = generatedRecipes.map(r => r.holistic_benefit_localized);

    return {
      themes,
      benefits,
      uniqueTherapeuticProperties,
      uniqueApplicationMethods
    };
  }, [generatedRecipes]);

  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">
        {/* User Profile */}
        <div className="lg:col-span-2">
          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
              <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {t('create-recipe:steps.final-recipes.overview.userProfile.title')}
            </h2>
            <div className="profile-data space-y-2">
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.condition')}:</strong> <span className="text-muted-foreground">{echoData?.health_concern_input || healthConcern?.healthConcern || 'Não informado'}</span></div>
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.age')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input?.age_specific ? `${echoData.user_info_input.age_specific} anos` : `${demographics?.specificAge || 'Não informado'} anos`}</span></div>
              <div className="text-sm"><strong>{t('create-recipe:steps.final-recipes.overview.userProfile.gender')}:</strong> <span className="text-muted-foreground">{echoData?.user_info_input?.gender || demographics?.gender || 'Não informado'}</span></div>
            </div>

            {(selectedCauses.length > 0 || echoData?.selected_cause_ids) && (
              <div className="pt-4">
                <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                  {t('create-recipe:steps.final-recipes.overview.userProfile.identifiedCauses')}
                </span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {/* Show from selectedCauses array */}
                  {selectedCauses.map((cause) => (
                    <span key={cause.cause_id} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                      {cause.cause_name}
                    </span>
                  ))}
                  {/* Show from echo data UUIDs */}
                  {echoData?.selected_cause_ids?.map((causeId) => (
                    <span key={causeId} className="inline-block px-2 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full">
                      {getCauseNameById(causeId)}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {(selectedSymptoms.length > 0 || echoData?.selected_symptom_ids) && (
              <div className="pt-4">
                <span className="text-xs text-muted-foreground uppercase tracking-wide font-medium">
                  Sintomas Identificados
                </span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {/* Show from selectedSymptoms array */}
                  {selectedSymptoms.map((symptom) => (
                    <span key={symptom.symptom_id} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
                      {symptom.symptom_name}
                    </span>
                  ))}
                  {/* Show from echo data UUIDs */}
                  {echoData?.selected_symptom_ids?.map((symptomId) => (
                    <span key={symptomId} className="inline-block px-2 py-1 text-xs font-medium bg-secondary/10 text-secondary-foreground rounded-full">
                      {getSymptomNameById(symptomId)}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Therapeutic Strategy */}
        <div className="lg:col-span-3">
          <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
              <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2m0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              {t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.title')}
            </h2>
            {therapeuticStrategy ? (
              <div className="space-y-4">
                {/* Therapeutic Properties - aggregated */}
                <div className="therapeutic-properties mb-4">
                  <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.properties')}</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {therapeuticStrategy.uniqueTherapeuticProperties.map((property, index) => (
                      <div key={index} className="bg-primary/5 p-3 rounded-lg border border-primary/10">
                        <strong className="text-primary">{property.name_localized}</strong>
                        <p className="text-sm text-muted-foreground">{property.rationale_localized}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Application Methods - aggregated */}
                <div className="application-methods">
                  <h4 className="text-base font-medium text-foreground mb-2">{t('create-recipe:steps.final-recipes.overview.therapeuticStrategy.applicationMethods')}</h4>
                  <div className="flex flex-wrap gap-2">
                    {therapeuticStrategy.uniqueApplicationMethods.map((method, index) => (
                      <span key={index} className="inline-block px-3 py-1 text-sm font-medium bg-accent/10 text-accent-foreground rounded-full border border-accent/20">
                        {method}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-muted-foreground">{t('create-recipe:steps.final-recipes.loading')}</div>
            )}
          </div>
        </div>
      </div>

      {/* Protocol Summary Cards */}
      <div className="lg:col-span-5">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h2 className="text-xl font-bold text-card-foreground mb-4 flex items-center gap-2">
            <svg className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
            </svg>
            Resumo dos Protocolos
          </h2>
          <div className="w-full mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
            <ProtocolSummaryCard
              timeSlot="morning"
              recipe={finalRecipes.morning.recipe}
              onViewDetails={() => onSwitchToRecipes('morning')}
              isFlipped={flippedCard === 'morning'}
              onFlip={() => setFlippedCard(flippedCard === 'morning' ? null : 'morning')}
            />
            <ProtocolSummaryCard
              timeSlot="mid-day"
              recipe={finalRecipes.midDay.recipe}
              onViewDetails={() => onSwitchToRecipes('mid-day')}
              isFlipped={flippedCard === 'mid-day'}
              onFlip={() => setFlippedCard(flippedCard === 'mid-day' ? null : 'mid-day')}
            />
            <ProtocolSummaryCard
              timeSlot="night"
              recipe={finalRecipes.night.recipe}
              onViewDetails={() => onSwitchToRecipes('night')}
              isFlipped={flippedCard === 'night'}
              onFlip={() => setFlippedCard(flippedCard === 'night' ? null : 'night')}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Recipes tab component
 */
interface RecipesTabProps {
  activeProtocol: RecipeTimeSlot;
  onSwitchProtocol: (protocol: RecipeTimeSlot) => void;
  finalRecipes: any;
  t: (key: string, fallback?: string) => string;
}

function RecipesTab({ activeProtocol, onSwitchProtocol, finalRecipes, t }: RecipesTabProps) {
  // Map from JSON data.recipe_protocol for each time slot
  const timeSlots = [
    {
      key: 'morning' as RecipeTimeSlot,
      time: finalRecipes.morning.recipe?.time_range_localized || '6h às 9h',
      label: 'Protocolo Matinal',
      description: finalRecipes.morning.recipe?.description_localized || 'Foco e calma para começar o dia'
    },
    {
      key: 'mid-day' as RecipeTimeSlot,
      time: finalRecipes.midDay.recipe?.time_range_localized || '11h às 14h',
      label: 'Protocolo Diurno',
      description: finalRecipes.midDay.recipe?.description_localized || 'Alívio imediato da dor'
    },
    {
      key: 'night' as RecipeTimeSlot,
      time: finalRecipes.night.recipe?.time_range_localized || '20h - 23h',
      label: 'Protocolo Noturno',
      description: finalRecipes.night.recipe?.description_localized || 'Sono reparador e relaxamento'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 px-2 sm:px-4 md:px-0">
      {/* Timeline Navigation */}
      <aside className="md:col-span-1 flex flex-col items-center md:items-stretch">
        <div className="relative flex flex-col h-full timeline">
          {timeSlots.map((slot) => (
            <div
              key={slot.key}
              className={`relative pl-10 pb-8 cursor-pointer transition-all duration-200 timeline-item ${activeProtocol === slot.key ? 'active active-box' : ''
                }`}
              onClick={() => onSwitchProtocol(slot.key)}
            >
              {/* Timeline dot */}
              <div className="dot" />

              {/* Content */}
              <div className={`p-4 rounded-lg transition-all duration-200 border ${activeProtocol === slot.key
                ? 'bg-primary/10 border-primary/20 shadow-md'
                : 'bg-card border-border hover:bg-muted/50'
                }`}>
                <p className="text-sm text-primary">{slot.time}</p>
                <h4 className="font-bold text-foreground">{slot.label}</h4>
                <p className="text-sm text-muted-foreground">{slot.description}</p>
              </div>
            </div>
          ))}
        </div>
      </aside>

      {/* Protocol Cards Container */}
      <section className="md:col-span-3 flex flex-col items-center gap-8 w-full">
        <RecipeProtocolCard
          timeSlot={activeProtocol}
          recipe={finalRecipes[activeProtocol === 'mid-day' ? 'midDay' : activeProtocol].recipe}
        />
      </section>
    </div>
  );
}

/**
 * Studies tab component
 */
interface StudiesTabProps {
  t: (key: string, fallback?: string) => string;
}

function StudiesTab({ t }: StudiesTabProps) {
  return (
    <div className="space-y-6">
      <div className="bg-card border border-border rounded-lg p-8 text-center">
        <h2 className="text-xl font-bold text-card-foreground mb-4">
          {t('create-recipe:steps.final-recipes.studies.title')}
        </h2>
        <p className="text-muted-foreground mb-4">
          {t('create-recipe:steps.final-recipes.studies.description')}
        </p>
        <p className="text-sm text-muted-foreground">
          {t('create-recipe:steps.final-recipes.studies.inDevelopment')}
        </p>
      </div>
    </div>
  );
}

/**
 * Security tab component
 */
interface SecurityTabProps {
  demographics: any;
  finalRecipes: any;
  t: (key: string, fallback?: string) => string;
}

function SecurityTab({ finalRecipes }: SecurityTabProps) {
  // Aggregate safety warnings from all recipes
  const allSafetyWarnings = React.useMemo(() => {
    const recipes = [
      finalRecipes.morning.recipe,
      finalRecipes.midDay.recipe,
      finalRecipes.night.recipe
    ].filter(Boolean);

    return recipes.flatMap(recipe => recipe.safety_warnings || []);
  }, [finalRecipes]);

  return (
    <div className="space-y-6">
      {/* SafetyWarnings component using JSON safety_warnings array */}
      <SafetyWarnings warnings={allSafetyWarnings} />
    </div>
  );
}