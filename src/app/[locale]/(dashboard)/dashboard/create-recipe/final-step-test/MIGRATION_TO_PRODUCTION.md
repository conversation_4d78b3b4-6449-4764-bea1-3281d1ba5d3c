# AI Prompt: Junior Developer - Final Recipe UI Migration

## 🎯 Your Mission

You are a **Junior Frontend Developer** tasked with migrating the working Final Recipe UI mockup to production. A senior developer has already created a **fully functional mockup** that matches the target design. Your job is to apply these working solutions to fix the broken production components.

## 📋 Context & Background

### What Has Been Done ✅
- **Working mockup created** at `src/app/(dashboard)/dashboard/create-recipe/final-step-test/`
- **All UI issues identified and fixed** in the mockup
- **Exact design match** achieved with `tasks/final-step-ui/standalone-v1.html`
- **API schema compatibility** needs adaptation because production has data transformations. Verified with production JSON responses
- **All interactions working**: flip cards, timeline navigation, collapsible sections

### What You Need to Do 🔧
- **Apply the working solutions** from mockup to production components
- **Fix the broken production UI** using the mockup as reference
- **Maintain all existing backend functionality** while fixing the visual issues
- **Follow strict constraints** to preserve system integrity

## 🚨 Critical Constraints - MUST FOLLOW

- ❌ **No hardcoded colors or styles** - use only theme variables from `src/styles/globals.css`
- ❌ **No hardcoded user-facing text** - use translation keys from `src/lib/i18n/messages/[locale]/create-recipe.json`
- ❌ **Do not change theme colors** - preserve existing design system
- ❌ **Do not break build process** - `npm run build` must remain at 100% static generation
- ❌ **No test files, performance optimizations, or monitoring** - focus only on UI fixes
- ✅ **Follow DRY, YAGNI, KISS principles**
- ✅ **Update translation files** for any new text
- ✅ **Essential fixes only** - implement exactly what's needed

## 📁 File Mapping: Mockup → Production

| Mockup (Working Reference) | Production (Needs Fixing) |
|----------------------------|---------------------------|
| `src/app/(dashboard)/dashboard/create-recipe/final-step-test/final-recipes-display.tsx` | `src/features/create-recipe/components/final-recipes-display.tsx` |
| `src/app/(dashboard)/dashboard/create-recipe/final-step-test/protocol-summary-card.tsx` | `src/features/create-recipe/components/protocol-summary-card.tsx` |
| `src/app/(dashboard)/dashboard/create-recipe/final-step-test/recipe-protocol-card.tsx` | `src/features/create-recipe/components/recipe-protocol-card.tsx` |
| `src/app/(dashboard)/dashboard/create-recipe/final-step-test/safety-warnings.tsx` | `src/features/create-recipe/components/safety-warnings.tsx` |

## 🔧 Specific Issues to Fix

### Issue 1: Flip Card Animation Broken
**Problem**: Multiple cards can flip simultaneously, animation doesn't work properly
**Location**: `src/features/create-recipe/components/protocol-summary-card.tsx` & `final-recipes-display.tsx`

**Solution from Mockup**:
```typescript
// In final-recipes-display.tsx - Add parent state management
const [flippedCard, setFlippedCard] = useState<string | null>(null);

// Pass controlled state to cards
<ProtocolSummaryCard
  timeSlot="morning"
  recipe={finalRecipes.morning.recipe}
  onViewDetails={() => onSwitchToRecipes('morning')}
  isFlipped={flippedCard === 'morning'}
  onFlip={() => setFlippedCard(flippedCard === 'morning' ? null : 'morning')}
/>

// In protocol-summary-card.tsx - Update interface
interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
  isFlipped: boolean;  // Add this
  onFlip: () => void;  // Add this
}

// Remove internal useState, use props instead
// Change CSS class to: className={`h-96 flip-card ${isFlipped ? 'is-flipped' : ''}`}
```

### Issue 2: Alternative Carrier Oil Not Displaying
**Problem**: Only "Recomendado" shows, "Alternativa" is invisible
**Location**: `src/features/create-recipe/components/recipe-protocol-card.tsx`

**Solution from Mockup**:
```typescript
// Fix text colors - change from text-secondary to text-foreground
<div className="bg-secondary/10 p-3 rounded-lg border border-secondary/20">
  <h4 className="font-bold text-foreground">{t('create-recipe:steps.final-recipes.recipeCard.alternative')}</h4>
  <div className="name font-bold text-foreground">{recipe.ingredients.carrier_oil.alternative?.name_localized}</div>
  <div className="properties text-xs text-muted-foreground">{recipe.ingredients.carrier_oil.alternative?.properties_localized}</div>
</div>

// Remove any conditional rendering that hides the alternative
// Use optional chaining for safety: alternative?.name_localized
```

### Issue 3: Timeline Active State Not Working
**Problem**: Selected protocol dot doesn't show as filled/active
**Location**: `src/features/create-recipe/components/final-recipes-display.tsx`

**Solution from Mockup**:
```typescript
// Fix CSS classes in timeline item
className={`relative pl-10 pb-8 cursor-pointer transition-all duration-200 timeline-item ${
  activeProtocol === slot.key ? 'active active-box' : ''
}`}

// Ensure CSS in globals.css has:
.timeline-item.active .dot {
  border-color: hsl(var(--primary));
  background-color: hsl(var(--primary));
}
```

### Issue 4: Missing Translation Keys
**Problem**: Some text shows as missing translation keys
**Location**: `src/lib/i18n/messages/pt/create-recipe.json`

**Solution from Mockup** - Add these keys:
```json
{
  "create-recipe": {
    "steps": {
      "final-recipes": {
        "recipeCard": {
          "notAvailable": "Receita não disponível",
          "drops": "gotas",
          "alternative": "Alternativa",
          "totalDrops": "Total de Gotas",
          "dilution": "Diluição",
          "size": "Tamanho",
          "dispenser": "Tampa",
          "essentialOils": "Óleos Essenciais",
          "carrierOil": "Óleo Carreador",
          "recommended": "Recomendado"
        },
        "overview": {
          "protocolSummary": {
            "synergyFor": "Sinergia para",
            "viewDetails": "Ver Detalhes",
            "clickToView": "Clique no botão para ver os detalhes",
            "close": "Fechar",
            "objective": "Objetivo",
            "quickPreparation": "Preparo rápido",
            "howToUse": "Como Usar"
          }
        }
      }
    }
  }
}
```

## 📋 Step-by-Step Migration Process

### Step 1: Backup & Preparation
ALready commited to Git, so we have a safe checkpoint to revert to if needed.

### Step 2: Fix Flip Cards
1. **Open** `src/features/create-recipe/components/final-recipes-display.tsx`
2. **Add** parent flip state management (see Issue 1 solution)
3. **Update** ProtocolSummaryCard props to include `isFlipped` and `onFlip`
4. **Open** `src/features/create-recipe/components/protocol-summary-card.tsx`
5. **Remove** internal `useState` for flip state
6. **Update** interface to accept `isFlipped` and `onFlip` props
7. **Fix** CSS class to use `is-flipped` instead of `flipped`

### Step 3: Fix Carrier Oil Display
1. **Open** `src/features/create-recipe/components/recipe-protocol-card.tsx`
2. **Find** the carrier oil alternative section
3. **Change** text colors from `text-secondary` to `text-foreground`
4. **Remove** any conditional rendering hiding the alternative
5. **Add** optional chaining for safety

### Step 4: Fix Timeline Active State
1. **Open** `src/features/create-recipe/components/final-recipes-display.tsx`
2. **Find** the timeline item className
3. **Update** to include both `active` and `active-box` classes
4. **Verify** CSS in `src/styles/globals.css` has proper timeline styles

### Step 5: Add Missing Translations
1. **Open** `src/lib/i18n/messages/pt/create-recipe.json`
2. **Add** all missing translation keys from Issue 4 solution
3. **Verify** no hardcoded text remains in components

### Step 6: Test & Validate
1. **Run** `npm run build` to ensure no build errors
2. **Test** flip card behavior (only one at a time)
3. **Verify** both carrier oils display
4. **Check** timeline active states work
5. **Confirm** all text displays properly

## 🎯 Success Criteria

Your migration is complete when:

- ✅ **Flip cards work exactly like mockup** (one at a time, smooth animation)
- ✅ **Both carrier oils display** (Recomendado + Alternativa)
- ✅ **Timeline shows active states** (filled dots for selected protocol)
- ✅ **All text displays properly** (no missing translation keys)
- ✅ **Build process works** (`npm run build` succeeds)
- ✅ **No hardcoded colors/text** (only theme variables and translations)
- ✅ **Visual match with mockup** (compare side by side)

## 🔍 How to Compare Your Work

### Visual Comparison
1. **Run mockup**: Navigate to `/dashboard/create-recipe/final-step-test`
2. **Run production**: Navigate to production final recipes page
3. **Compare side by side**: Both should look and behave identically

### Functional Testing
- **Flip Cards**: Click "Ver Detalhes" - only one card should flip at a time
- **Timeline**: Click different protocols - dots should fill/unfill properly
- **Carrier Oils**: Both "Recomendado" and "Alternativa" should be visible
- **Collapsible Sections**: All three sections should expand/collapse
- **Tab Navigation**: All tabs should work properly

## 📚 Reference Files

### Working Mockup (Your Reference)
- `src/app/(dashboard)/dashboard/create-recipe/final-step-test/` - All files here work correctly
- Use these as your source of truth for how things should work

### Production Files (What You're Fixing)
- `src/features/create-recipe/components/` - Apply fixes to these files

### Design Reference
- `tasks/final-step-ui/standalone-v1.html` - Target design
- `tasks/final-step-ui/final-recipes-*-output.json` - API data structure

### Styling & Translations
- `src/styles/globals.css` - Theme variables and CSS
- `src/lib/i18n/messages/pt/create-recipe.json` - Translation keys

## ⚠️ Common Pitfalls to Avoid

1. **Don't change theme colors** - use existing CSS variables only
2. **Don't add hardcoded text** - use translation keys for all user-facing text
3. **Don't break existing functionality with backend** - preserve all current backend features
4. **Don't add unnecessary complexity** - apply only the specific fixes needed
5. **Don't skip testing** - verify each fix works before moving to the next

## 🆘 If You Get Stuck

1. **Compare with mockup** - The mockup has all the working solutions
2. **Check console errors** - Look for missing imports or type errors
3. **Verify file paths** - Ensure all imports are correct for production
4. **Test incrementally** - Fix one issue at a time and test
5. **Follow the exact solutions** - Don't deviate from the provided fixes

## 🎉 Final Validation

Before submitting your work:

1. **Side-by-side comparison** - Mockup and production should be identical
2. **All interactions work** - Flip, timeline, collapsible, tabs
3. **Build succeeds** - `npm run build` completes without errors
4. **No console errors** - Clean browser console
5. **Mobile responsive** - Test on different screen sizes

Your goal is to make the production components work exactly like the mockup. The mockup is your blueprint - follow it precisely and you'll succeed! 🚀