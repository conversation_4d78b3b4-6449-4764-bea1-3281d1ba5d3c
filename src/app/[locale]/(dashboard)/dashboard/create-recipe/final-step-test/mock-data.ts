/**
 * @fileoverview Mock data for Final Step UI testing
 * Uses exact data structure from JSON files to simulate API responses
 */

// Mock data extracted from JSON files - using exact schema from production
export const mockFinalRecipes = {
  morning: {
    recipe: {
      recipe_id: "39f8e7b6-2f4a-4a8e-9f3b-1c2e4d5f6a7b",
      time_slot: "morning",
      time_of_day_localized: "manhã",
      time_range_localized: "6h às 9h",
      recipe_theme_localized: "Manhã Revigorante e Equilibrada",
      description_localized: "Este óleo de aromaterapia para manhã foi especialmente elaborado para ajudar a aliviar inchaço abdominal e desconforto digestivo, promovendo uma sensação de bem-estar e equilíbrio logo no início do dia.",
      holistic_benefit_localized: "Ao incorporar esta mistura na sua rotina matinal, você pode experimentar uma sensação de calma, alívio de desconfortos digestivos e uma energia equilibrada para enfrentar o dia.",
      application_type_localized: "Inalação e aplicação tópica suave na região abdominal",
      preparation_summary_localized: "Misture os óleos em um frasco roll-on de 10ml e complete com óleo carreador. Agite bem antes de usar.",
      usage_summary_localized: "Aplique nos pulsos, nuca e têmporas ao acordar para um início de dia equilibrado.",
      ritual_suggestion_localized: "A combinação de hortelã-pimenta e gengibre atua sinergicamente para aliviar inchaços, gases e desconfortos estomacais.",
      formulation: {
        total_drops: 15,
        dilution_percentage: 1,
        bottle_size_ml: 10,
        bottle_type_localized: "frasco de vidro escuro de 10ml"
      },
      ingredients: {
        essential_oils: [
          {
            oil_id: "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            name_localized: "Hortelã-pimenta",
            scientific_name: "Mentha Piperita",
            drops: 5
          },
          {
            oil_id: "56b7e686-5417-4bcf-a975-9888f559a4f3",
            name_localized: "Gengibre",
            scientific_name: "Zingiber officinale",
            drops: 4
          },
          {
            oil_id: "caef2077-85b5-479f-a828-7b7be804e498",
            name_localized: "Limão siciliano",
            scientific_name: "Citrus limon",
            drops: 3
          },
          {
            oil_id: "68c0695a-9213-4e50-9877-e328df604ef7",
            name_localized: "Capim-limão",
            scientific_name: "Cymbopogon flexuosus",
            drops: 2
          }
        ],
        carrier_oil: {
          recommended: {
            name_localized: "Óleo de massagem relaxante",
            properties_localized: "Óleo vegetal de base leve, ideal para massagens suaves no abdômen, facilitando a absorção dos óleos essenciais e promovendo um efeito calmante e relaxante."
          },
          alternative: {
            name_localized: "Óleo de coco",
            properties_localized: "Óleo vegetal de rápida absorção, com propriedades nutritivas e calmantes para a pele, além de potencializar os efeitos dos óleos essenciais."
          }
        }
      },
      usage_instructions_localized: [
        {
          method: "Aplicação Tópica",
          description: "Aplicar nos pulsos, nuca e atrás das orelhas.",
          frequency: "1x pela manhã"
        },
        {
          method: "Inalação",
          description: "Inalar diretamente do frasco por 30 segundos.",
          frequency: "Conforme necessário"
        }
      ],
      preparation_steps_localized: [
        "Higienize o frasco roll-on com álcool 70%",
        "Adicione 5 gotas de hortelã-pimenta",
        "Adicione 4 gotas de gengibre",
        "Adicione 3 gotas de limão siciliano",
        "Adicione 2 gotas de capim-limão",
        "Complete com óleo carreador até a marca de 10ml",
        "Feche bem e agite por 30 segundos",
        "Etiquete com data e ingredientes"
      ],
      oil_rationales: [
        {
          name_localized: "Hortelã-pimenta",
          properties: ["Antiespasmódica", "Digestiva", "Refrescante"],
          rationale_localized: "Possui propriedades antiespasmódicas, ajudando a diminuir as cólicas e relaxar o trato digestivo."
        },
        {
          name_localized: "Gengibre",
          properties: ["Anti-inflamatória", "Digestiva", "Estimulante"],
          rationale_localized: "Estimula a digestão e reduz inflamações, complementando os efeitos da hortelã-pimenta."
        },
        {
          name_localized: "Limão Siciliano",
          properties: ["Desintoxicante", "Estimulante", "Purificante"],
          rationale_localized: "Promove uma ação desintoxicante e estimulante, potencializando os efeitos digestivos."
        },
        {
          name_localized: "Capim-limão",
          properties: ["Calmante", "Digestiva", "Antiespasmódica"],
          rationale_localized: "Reforça os efeitos calmantes e digestivos da mistura."
        }
      ],
      safety_warnings: [
        {
          type: "patch_test",
          title_localized: "Teste de Sensibilidade",
          warning_text_localized: "Faça um teste de sensibilidade em pequena área da pele antes do primeiro uso."
        },
        {
          type: "storage",
          title_localized: "Armazenamento",
          warning_text_localized: "Mantenha em local fresco e seco, protegido da luz solar direta."
        }
      ]
    }
  },
  midDay: {
    recipe: {
      recipe_id: "a1b2c3d4-e5f6-7890-ab12-cd34ef56ab78",
      time_slot: "mid-day",
      time_of_day_localized: "Meio-dia",
      time_range_localized: "11h às 14h",
      recipe_theme_localized: "Alívio do Inchaço Digestivo",
      description_localized: "Este blend aromático é especialmente criado para promover uma sensação de alívio e bem-estar após as refeições, ajudando a aliviar o inchaço abdominal frequente.",
      holistic_benefit_localized: "Este aroma combina propriedades antiespasmódicas e digestivas para ajudar a aliviar o desconforto abdominal, promovendo uma sensação de leveza e equilíbrio interno.",
      application_type_localized: "Óleo de massagem abdominal, difusor de ambiente e aplicação tópica diluída",
      preparation_summary_localized: "Misture os óleos em um frasco de vidro âmbar de 10ml e complete com óleo de amêndoas doces. Agite bem antes de usar.",
      usage_summary_localized: "Aplique na região abdominal com movimentos circulares suaves após as refeições.",
      ritual_suggestion_localized: "A combinação de hortelã-pimenta, gengibre, limão, funcho e tangerina oferece um efeito sinérgico que promove a digestão saudável.",
      formulation: {
        total_drops: 15,
        dilution_percentage: 1.46,
        bottle_size_ml: 10,
        bottle_type_localized: "frasco de vidro âmbar de 10ml"
      },
      ingredients: {
        essential_oils: [
          {
            oil_id: "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            name_localized: "Hortelã-pimenta",
            scientific_name: "Mentha piperita",
            drops: 4
          },
          {
            oil_id: "b8e3d174-46fb-4ae9-8c4f-1f2e5d6a8b9c",
            name_localized: "Gengibre",
            scientific_name: "Zingiber officinale",
            drops: 3
          },
          {
            oil_id: "d0f5e396-68hd-4cg1-0e6g-3g4f7e8b0d1e",
            name_localized: "Funcho",
            scientific_name: "Foeniculum vulgare",
            drops: 3
          }
        ],
        carrier_oil: {
          recommended: {
            name_localized: "Óleo de amêndoas doces",
            properties_localized: "Suave, nutritivo, ideal para massagem abdominal"
          },
          alternative: {
            name_localized: "Óleo de coco fracionado",
            properties_localized: "Absorção rápida, não oleoso, hipoalergênico"
          }
        }
      },
      usage_instructions_localized: [
        {
          method: "Massagem Abdominal",
          description: "Aplicar na região abdominal com movimentos circulares suaves.",
          frequency: "Após as refeições"
        },
        {
          method: "Difusão",
          description: "Adicionar 3-5 gotas no difusor durante as refeições.",
          frequency: "Durante o almoço"
        }
      ],
      preparation_steps_localized: [
        "Higienize o frasco de vidro âmbar com álcool 70%",
        "Adicione 4 gotas de hortelã-pimenta",
        "Adicione 3 gotas de gengibre",
        "Adicione 3 gotas de funcho",
        "Complete com óleo de amêndoas doces até 10ml",
        "Feche bem e agite vigorosamente",
        "Deixe descansar por 24h antes do primeiro uso"
      ],
      oil_rationales: [
        {
          name_localized: "Hortelã-pimenta",
          properties: ["Antiespasmódica", "Digestiva", "Refrescante"],
          rationale_localized: "Alivia gases e cólicas intestinais, promovendo conforto digestivo."
        },
        {
          name_localized: "Gengibre",
          properties: ["Anti-inflamatória", "Digestiva", "Carminativa"],
          rationale_localized: "Estimula a digestão e reduz inflamações do trato gastrointestinal."
        },
        {
          name_localized: "Funcho",
          properties: ["Carminativa", "Antiespasmódica", "Digestiva"],
          rationale_localized: "Tradicionalmente usado para aliviar gases e inchaço abdominal."
        }
      ],
      safety_warnings: [
        {
          type: "patch_test",
          title_localized: "Teste de Sensibilidade",
          warning_text_localized: "Realize teste em pequena área da pele antes do uso completo."
        },
        {
          type: "alert",
          title_localized: "Uso Abdominal",
          warning_text_localized: "Evite aplicação em caso de feridas abertas ou irritação na pele."
        }
      ]
    }
  },
  night: {
    recipe: {
      recipe_id: "a1b2c3d4-e5f6-7890-ab12-cd34ef56ab78",
      time_slot: "night",
      time_of_day_localized: "Noite",
      time_range_localized: "20h - 23h",
      recipe_theme_localized: "Noite Calmante",
      description_localized: "Este blend foi criado para ajudar a aliviar o desconforto digestivo, como inchaço abdominal, promovendo uma sensação de calma e relaxamento antes de dormir.",
      holistic_benefit_localized: "Favorece uma digestão tranquila e uma noite de sono mais relaxada, ajudando a aliviar tensões digestivas e promovendo bem-estar geral.",
      application_type_localized: "Uso tópico e aromático, ideal para momentos de relaxamento noturno",
      preparation_summary_localized: "Misture os óleos em um frasco de vidro âmbar de 30ml e complete com óleo de amêndoas doces. Agite suavemente antes de usar.",
      usage_summary_localized: "Aplique na sola dos pés e no peito 30 minutos antes de dormir para uma noite de sono tranquila.",
      ritual_suggestion_localized: "Este blend combina hortelã-pimenta e gengibre com lavanda para promover relaxamento profundo e digestão tranquila.",
      formulation: {
        total_drops: 12,
        dilution_percentage: 2,
        bottle_size_ml: 30,
        bottle_type_localized: "Frasco de vidro âmbar de 30ml"
      },
      ingredients: {
        essential_oils: [
          {
            oil_id: "56b7e686-5417-4bcf-a975-9888f559a4f3",
            name_localized: "Lavanda",
            scientific_name: "Lavandula angustifolia",
            drops: 6
          },
          {
            oil_id: "ad7c9072-35ea-4fd8-a7f7-fe1f96bcfccf",
            name_localized: "Hortelã-pimenta",
            scientific_name: "Mentha piperita",
            drops: 3
          },
          {
            oil_id: "e1g6f407-79ie-4dh2-1f7h-4h5g8f9c1e2f",
            name_localized: "Manjerona",
            scientific_name: "Origanum majorana",
            drops: 3
          }
        ],
        carrier_oil: {
          recommended: {
            name_localized: "Óleo de amêndoas doces",
            properties_localized: "Hidrata e promove absorção eficaz dos óleos essenciais."
          },
          alternative: {
            name_localized: "Óleo de coco",
            properties_localized: "Óleo nutritivo que ajuda na dispersão dos óleos essenciais na pele, promovendo suavidade e sensação acolhedora."
          }
        }
      },
      usage_instructions_localized: [
        {
          method: "Aplicação nos Pés",
          description: "Aplicar na sola dos pés com massagem suave.",
          frequency: "30 min antes de dormir"
        },
        {
          method: "Aplicação no Peito",
          description: "Aplicar no peito para inalação durante o sono.",
          frequency: "Antes de deitar"
        }
      ],
      preparation_steps_localized: [
        "Higienize o frasco de vidro âmbar de 30ml",
        "Adicione 6 gotas de lavanda",
        "Adicione 3 gotas de hortelã-pimenta",
        "Adicione 3 gotas de manjerona",
        "Complete com óleo de amêndoas doces até 30ml",
        "Feche e agite delicadamente",
        "Deixe sinergizar por 48h antes do uso"
      ],
      oil_rationales: [
        {
          name_localized: "Lavanda",
          properties: ["Calmante", "Relaxante", "Sedativa"],
          rationale_localized: "Promove relaxamento profundo e prepara o corpo para o descanso."
        },
        {
          name_localized: "Hortelã-pimenta",
          properties: ["Digestiva", "Refrescante", "Antiespasmódica"],
          rationale_localized: "Alivia desconfortos digestivos noturnos de forma suave."
        },
        {
          name_localized: "Manjerona",
          properties: ["Calmante", "Sedativa", "Antiespasmódica"],
          rationale_localized: "Oferece efeito calmante para o sistema nervoso, promovendo sono tranquilo."
        }
      ],
      safety_warnings: [
        {
          type: "storage",
          title_localized: "Armazenamento Noturno",
          warning_text_localized: "Mantenha o frasco ao lado da cama, em local seguro e acessível."
        },
        {
          type: "legal",
          title_localized: "Uso Noturno",
          warning_text_localized: "Não dirija ou opere máquinas após a aplicação devido ao efeito relaxante."
        }
      ]
    }
  }
};

// Mock user data
export const mockUserData = {
  healthConcern: {
    healthConcern: "Dores de cabeça crônicas"
  },
  demographics: {
    gender: "Feminino",
    specificAge: 3,
    ageCategory: "child"
  },
  selectedCauses: [
    {
      cause_id: "cause-1",
      cause_name: "Estresse"
    },
    {
      cause_id: "cause-2", 
      cause_name: "Privação de sono"
    }
  ],
  selectedSymptoms: [
    {
      symptom_id: "symptom-1",
      symptom_name: "Dor constante"
    },
    {
      symptom_id: "symptom-2",
      symptom_name: "Tensão muscular"
    }
  ]
};

// Mock echo data (from first recipe)
export const mockEchoData = {
  health_concern_input: "Dores de cabeça crônicas",
  user_info_input: {
    age_specific: 3,
    age_unit: "anos",
    gender: "Feminino"
  },
  selected_cause_ids: ["cause-1", "cause-2"],
  selected_symptom_ids: ["symptom-1", "symptom-2"]
};