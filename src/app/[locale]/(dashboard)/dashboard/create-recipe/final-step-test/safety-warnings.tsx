/**
 * @fileoverview Safety warnings component for essential oil recipes
 * Shows age-appropriate safety warnings and guidelines
 */

'use client';

import React from 'react';

interface SafetyWarning {
  type: string;
  title_localized: string;
  warning_text_localized: string;
  severity: 'high' | 'medium' | 'low';
  message_localized: string;
  guidance_localized: string;
  warning_type?: string;
}

interface SafetyWarningsProps {
  warnings: SafetyWarning[];
}

/**
 * Get warning color class based on type
 */
const getWarningColorClass = (type: string) => {
  switch (type) {
    case 'patch_test': return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    case 'storage': return 'bg-blue-50 border-blue-200 text-blue-800';
    case 'alert': return 'bg-red-50 border-red-200 text-red-800';
    case 'legal': return 'bg-muted border-border text-muted-foreground';
    default: return 'bg-muted border-border text-muted-foreground';
  }
};

/**
 * Safety warnings dynamic accordion using JSON safety_warnings array
 */
export function SafetyWarnings({ warnings }: SafetyWarningsProps) {
  if (!warnings || warnings.length === 0) {
    return (
      <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-2xl p-6">
        <div className="flex items-center gap-3 mb-3">
          <CheckIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Perfil de Segurança</h3>
        </div>
        <p className="text-green-700 dark:text-green-300">
          Baseado na sua idade e perfil, as receitas recomendadas são seguras quando usadas conforme as instruções.
        </p>
      </div>
    );
  }

  return (
    <SafetyWarningsAccordion warnings={warnings} />
  );
}

/**
 * Safety warnings accordion component
 */
function SafetyWarningsAccordion({ warnings }: { warnings: SafetyWarning[] }) {
  return (
    <div className="safety-warnings space-y-2">
      {warnings.map((warning, index) => (
        <details key={index} className={`warning-item border rounded-lg p-4 ${getWarningColorClass(warning.type)}`}>
          <summary className="cursor-pointer font-medium">{warning.title_localized}</summary>
          <div className="mt-2" dangerouslySetInnerHTML={{ __html: warning.warning_text_localized }} />
        </details>
      ))}
    </div>
  );
}

/**
 * Comprehensive Safety Protocol component - from HTML accordion
 */
export function ComprehensiveSafetyProtocol() {
  return (
    <div className="bg-card rounded-2xl p-6 shadow-lg border border-border">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-card-foreground mb-2 flex items-center justify-center gap-3">
          <span className="text-3xl">🛡️</span>
          Protocolo de Segurança Essencial
        </h1>
        <p className="text-muted-foreground">Leia com atenção antes de utilizar qualquer receita.</p>
      </div>

      {/* Safety Accordion - matching HTML structure */}
      <div className="space-y-2">
        {/* 1. Sensitivity Test (Mandatory) */}
        <details className="bg-yellow-50 dark:bg-yellow-950/20 border-l-4 border-yellow-400 dark:border-yellow-600 rounded-r-lg border-0">
          <summary className="font-bold text-yellow-900 dark:text-yellow-100 px-4 py-4 cursor-pointer">
            1. Teste de Sensibilidade (Obrigatório)
          </summary>
          <div className="px-4 pb-4">
            <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-4">
              Antes do primeiro uso de qualquer nova mistura, realize este teste para prevenir reações alérgicas. É um passo crucial para a segurança.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <p className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Como Fazer:</p>
                <ol className="list-decimal list-inside text-yellow-900 dark:text-yellow-200 space-y-2 pl-2">
                  <li>Prepare uma pequena quantidade da mistura.</li>
                  <li>Aplique uma gota na parte interna do antebraço.</li>
                  <li>Cubra a área com um curativo e aguarde 24 horas.</li>
                  <li>Após o período, remova o curativo e observe a pele.</li>
                </ol>
              </div>
              <div>
                <p className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">O que Observar:</p>
                <ul className="list-disc list-inside text-yellow-900 dark:text-yellow-200 space-y-2 pl-2">
                  <li>Vermelhidão ou irritação</li>
                  <li>Coceira intensa ou queimação</li>
                  <li>Inchaço, bolhas ou urticária</li>
                  <li>Qualquer forma de desconforto na área</li>
                </ul>
              </div>
            </div>
            <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-4">
              <strong>Resultado:</strong> Se notar qualquer uma dessas reações, não utilize a mistura. Lave a área com óleo vegetal (ex: coco, amêndoas) e depois com água e sabão.
            </p>
          </div>
        </details>

        {/* 2. Safe Use and Storage */}
        <details className="bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-400 dark:border-blue-600 rounded-r-lg border-0">
          <summary className="font-bold text-blue-900 dark:text-blue-100 px-4 py-4 cursor-pointer">
            2. Uso Seguro e Armazenamento
          </summary>
          <div className="px-4 pb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-900 dark:text-blue-200">
              <div>
                <h4 className="font-semibold mb-2">Diretrizes de Aplicação:</h4>
                <ul className="list-disc list-inside space-y-2 pl-2">
                  <li><strong>Sempre dilua</strong> os óleos essenciais em um óleo carreador, conforme a receita. Nunca aplique puros na pele.</li>
                  <li>Mantenha longe dos <strong>olhos, interior dos ouvidos e mucosas</strong>.</li>
                  <li>Evite aplicar as misturas próximo ao rosto de crianças.</li>
                  <li>O uso em crianças deve ser sempre <strong>supervisionado por um adulto</strong>.</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Armazenamento Correto:</h4>
                <ul className="list-disc list-inside space-y-2 pl-2">
                  <li>Guarde em frascos de vidro escuro (âmbar ou azul).</li>
                  <li>Mantenha em <strong>local fresco e ao abrigo da luz</strong> solar direta.</li>
                  <li>Certifique-se de que as tampas estejam bem fechadas.</li>
                  <li>Mantenha <strong>fora do alcance de crianças e animais</strong> de estimação.</li>
                </ul>
              </div>
            </div>
          </div>
        </details>

        {/* 3. Warning Signs and Necessary Action */}
        <details className="bg-red-50 dark:bg-red-950/20 border-l-4 border-red-400 dark:border-red-600 rounded-r-lg border-0">
          <summary className="font-bold text-red-900 dark:text-red-100 px-4 py-4 cursor-pointer">
            3. Sinais de Alerta e Ação Necessária
          </summary>
          <div className="px-4 pb-4">
            <p className="text-sm text-red-800 dark:text-red-200 mb-4">
              A aromaterapia é um suporte, mas não substitui a avaliação médica. Fique atento a estes sinais.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Procure Ajuda Médica Imediata se:</h4>
                <ul className="list-disc list-inside text-red-900 dark:text-red-200 space-y-2 pl-2">
                  <li>Ocorrer uma <strong>reação alérgica grave</strong> (inchaço no rosto ou garganta, urticária generalizada).</li>
                  <li>Surgirem <strong>dificuldades respiratórias</strong> após o uso.</li>
                  <li>Os sintomas que você está tratando piorarem drasticamente.</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Consulte um Médico se a Condição Apresentar:</h4>
                <ul className="list-disc list-inside text-red-900 dark:text-red-200 space-y-2 pl-2">
                  <li>Dores de cabeça muito frequentes (&gt;3x/semana) ou incapacitantes.</li>
                  <li>Febre associada aos sintomas.</li>
                  <li>Vômitos recorrentes ou mudanças de comportamento.</li>
                  <li>Qualquer sintoma severo, persistente ou preocupante.</li>
                </ul>
              </div>
            </div>
          </div>
        </details>
      </div>

      {/* Legal Notice and Responsibility - matching HTML structure */}
      <div className="bg-muted border-l-4 border-muted-foreground/30 rounded-r-lg p-6 shadow-sm mt-6">
        <h3 className="text-xl font-bold text-foreground mb-3 flex items-center gap-2">
          <span className="font-mono bg-muted-foreground/20 text-foreground rounded-full w-8 h-8 flex items-center justify-center text-lg">4</span>
          Aviso Legal e de Responsabilidade
        </h3>
        <div className="space-y-3 text-sm text-muted-foreground">
          <p>
            <strong>Natureza Complementar:</strong> A aromaterapia é uma prática de bem-estar e não substitui diagnósticos, tratamentos ou conselhos médicos. As informações e receitas aqui contidas são para fins educacionais.
          </p>
          <p>
            <strong>Responsabilidade Individual:</strong> O uso dos óleos essenciais é de sua inteira responsabilidade. As reações podem variar. Monitore sempre as respostas do corpo e ajuste o uso ou suspenda-o se necessário.
          </p>
          <p>
            <strong>Busca Profissional:</strong> Consulte sempre um profissional de saúde qualificado (médico ou aromaterapeuta certificado) antes de iniciar o uso de óleos essenciais, especialmente em crianças, gestantes, lactantes ou pessoas com condições médicas preexistentes.
          </p>
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function CheckIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
    </svg>
  );
}