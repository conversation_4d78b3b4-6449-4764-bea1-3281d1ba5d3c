import type { Locale } from '../../../../i18n-config';

interface OnboardingLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: Locale }>;
}

/**
 * Onboarding layout 
 * The middleware now handles onboarding completion redirects properly
 * This layout is simplified to just render the children
 */
export default async function OnboardingLayout({ 
  children
}: OnboardingLayoutProps) {
  // Layout simplified - middleware handles all onboarding logic
  return <>{children}</>;
}