import { OnboardingForm } from '@/features/onboarding';
import { getTranslations } from '@/lib/dictionaries';
import type { Locale } from '../../../../i18n-config';

interface Props {
    params: Promise<{ locale: Locale }>;
}

export default async function OnboardingPage({ params }: Props) {
    const { locale } = await params;
    const translations = await getTranslations(locale, 'onboarding');

    return (
        <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
            <div className="w-full max-w-4xl">
                <OnboardingForm locale={locale} translations={{ onboarding: translations }} />
            </div>
        </div>
    );
}