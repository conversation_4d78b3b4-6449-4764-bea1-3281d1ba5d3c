/**
 * Static locale layout with SEO optimization and Clerk localization
 * Gets locale from URL params (static) instead of headers (dynamic)
 * This enables static generation while maintaining proper i18n and SEO
 * Security: Onboarding verification is handled by middleware as per Clerk's official recommendation.
 */

import { notFound } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { i18n } from '../../../i18n-config';
import { getClerkLocalization, getClerkUrls } from '@/features/auth/config';
import type { Metadata } from 'next';

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

// Generate static params for all supported locales
// This enables static generation at build time
export function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' }
  ];
}

/**
 * Server-side metadata generation for SEO optimization
 * Runs on the server and correctly merges metadata into the root layout
 * This is the proper way to handle locale-specific SEO without hydration issues
 */
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale } = await params;
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://aromachat.com';

  return {
    // Set locale-specific Open Graph metadata
    openGraph: {
      locale: locale === 'pt' ? 'pt_BR' : locale === 'es' ? 'es_ES' : 'en_US',
    },

    // Generate hreflang tags for all supported locales
    alternates: {
      canonical: `${baseUrl}/${locale}`,
      languages: {
        'en': `${baseUrl}/en`,
        'pt': `${baseUrl}/pt`,
        'es': `${baseUrl}/es`,
        'x-default': `${baseUrl}/en`,
      },
    },
  };
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Validate locale using centralized config
  if (!i18n.locales.includes(locale as any)) {
    notFound();
  }

  // Get locale-specific Clerk configuration (static - no headers() usage)
  const clerkLocalization = getClerkLocalization(locale);
  const clerkUrls = getClerkUrls(locale as any);

  // Security Note: All onboarding and authentication protection is handled by middleware.
  // This approach follows Clerk's official documentation and prevents build-time issues.
  // See: https://clerk.com/docs/guides/add-onboarding

  return (
    <ClerkProvider
      localization={clerkLocalization}
      signInUrl={clerkUrls.signInUrl}
      signUpUrl={clerkUrls.signUpUrl}
      signInFallbackRedirectUrl={clerkUrls.signInFallbackRedirectUrl}
      signUpFallbackRedirectUrl={clerkUrls.signUpFallbackRedirectUrl}
    >
      {children}
    </ClerkProvider>
  );
}
