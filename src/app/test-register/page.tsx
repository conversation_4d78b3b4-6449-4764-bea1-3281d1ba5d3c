import { SignUp } from '@clerk/nextjs'

export default function TestRegisterPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            🧪 Test Register (No OAuth)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Testing basic email/password registration first
          </p>
        </div>
        
        <SignUp 
          fallbackRedirectUrl="/dashboard"
          signInUrl="/test-login"
          appearance={{
            elements: {
              socialButtonsBlockButton: {
                display: 'none' // Hide OAuth buttons for testing
              }
            }
          }}
        />
      </div>
    </div>
  )
}
