import { SignIn } from '@clerk/nextjs'

export default function TestLoginPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            🧪 Test Login (No OAuth)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Testing basic email/password auth first
          </p>
        </div>
        
        <SignIn 
          fallbackRedirectUrl="/dashboard"
          signUpUrl="/test-register"
          appearance={{
            elements: {
              socialButtonsBlockButton: {
                display: 'none' // Hide OAuth buttons for testing
              }
            }
          }}
        />
      </div>
    </div>
  )
}
