import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
  const secretKey = process.env.CLERK_SECRET_KEY
  
  return NextResponse.json({
    hasPublishableKey: !!publishableKey,
    hasSecretKey: !!secretKey,
    publishableKeyLength: publishableKey?.length || 0,
    secretKeyLength: secretKey?.length || 0,
    publishableKeyPrefix: publishableKey?.substring(0, 10) + '...',
    secretKeyPrefix: secretKey?.substring(0, 10) + '...',
    nodeEnv: process.env.NODE_ENV,
  })
}
