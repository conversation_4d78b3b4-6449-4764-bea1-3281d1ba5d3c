import { NextRequest } from 'next/server';
import { batchEnrichOils, type SuggestedOilData } from '@/lib/ai/services/batch-enrichment.service';
import { 
  validateOpenAI<PERSON>ey,
  parseJsonBody, 
  createErrorResponse, 
  createSuccessResponse,
  withErrorHandling
} from '@/lib/ai/utils/api-helpers';
import { FileLogger } from '@/lib/debug/file-logger';

async function handler(request: NextRequest) {
  const traceId = `batch-enrichment-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const startTime = performance.now();
  
  console.log(`🚀 [API-${traceId}] ===== BATCH ENRICHMENT API STARTED =====`);
  console.log(`📡 [API-${traceId}] Request method: ${request.method}`);
  console.log(`🌐 [API-${traceId}] Request URL: ${request.url}`);
  console.log(`⏰ [API-${traceId}] Start time: ${new Date().toISOString()}`);
  
  const inputLogger = new FileLogger({ logDirectory: 'input', fileName: `batch-enrichment-input-${traceId}.json`, mode: 'transactional' });
  const finalOutputLogger = new FileLogger({ logDirectory: 'output', fileName: `batch-enrichment-final-${traceId}.json`, mode: 'transactional' });

  try {
    console.log(`🔐 [API-${traceId}] Validating OpenAI key...`);
    validateOpenAIKey();
    console.log(`✅ [API-${traceId}] OpenAI key validation passed`);
  
    console.log(`📥 [API-${traceId}] Parsing request body...`);
    const body = await parseJsonBody(request);
    await inputLogger.log(body);
    console.log(`✅ [API-${traceId}] Request body parsed successfully`);

    const { suggestedOils } = body;

    if (!suggestedOils || !Array.isArray(suggestedOils)) {
      console.error(`❌ [API-${traceId}] Invalid request: suggestedOils missing or not an array`);
      console.error(`❌ [API-${traceId}] Received body keys:`, Object.keys(body));
      const errorMessage = { error: '`suggestedOils` array is required in the request body.' };
      await finalOutputLogger.log(errorMessage);
      return createErrorResponse(errorMessage.error, 400);
    }

    console.log(`🔍 [API-${traceId}] Received batch enrichment request for ${suggestedOils.length} oils`);
    console.log(`📋 [API-${traceId}] Sample oil data:`, suggestedOils[0]);

    const serviceStartTime = performance.now();
    const result = await batchEnrichOils(suggestedOils as SuggestedOilData[]);
    const serviceTime = performance.now() - serviceStartTime;

    console.log(`✅ [API-${traceId}] Batch enrichment service completed in ${Math.round(serviceTime)}ms`);
    console.log(`📊 [API-${traceId}] Service results:`, {
      total_input: result.total_input,
      total_enriched: result.total_enriched,
      total_not_found: result.total_not_found,
      total_discarded: result.total_discarded,
      success_rate: `${((result.total_enriched / result.total_input) * 100).toFixed(1)}%`,
      service_processing_time_ms: result.processing_time_ms
    });

    // Log the raw result data before wrapping it in a response
    console.log(`💾 [API-${traceId}] Logging final output...`);
    await finalOutputLogger.log({
      data: result,
      timestamp: new Date().toISOString(),
      status: 'success',
      traceId
    });

    const totalTime = performance.now() - startTime;
    console.log(`🎯 [API-${traceId}] ===== BATCH ENRICHMENT API COMPLETED =====`);
    console.log(`⏱️  [API-${traceId}] Total API time: ${Math.round(totalTime)}ms`);
    console.log(`📤 [API-${traceId}] Returning success response`);

    return createSuccessResponse(result);
  } catch (error: unknown) {
    const totalTime = performance.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
    
    console.error(`💥 [API-${traceId}] ===== BATCH ENRICHMENT API FAILED =====`);
    console.error(`❌ [API-${traceId}] Error message: ${errorMessage}`);
    console.error(`❌ [API-${traceId}] Error type: ${error instanceof Error ? error.constructor.name : typeof error}`);
    console.error(`❌ [API-${traceId}] Stack trace:`, errorStack);
    console.error(`⏱️  [API-${traceId}] Failed after: ${Math.round(totalTime)}ms`);
    
    const logData = {
      error: errorMessage,
      errorType: error instanceof Error ? error.constructor.name : typeof error,
      stack: errorStack,
      timestamp: new Date().toISOString(),
      status: 'error',
      traceId,
      totalTimeMs: Math.round(totalTime)
    };
    
    await finalOutputLogger.log(logData);
    return createErrorResponse(errorMessage, 500);
  }
}

export const POST = withErrorHandling(handler); 