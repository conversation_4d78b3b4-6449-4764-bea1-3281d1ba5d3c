import { useCallback, useEffect, useRef, useState } from "react";

interface ScrollState {
  isAtBottom: boolean;
  autoScrollEnabled: boolean;
}

interface UseAutoScrollOptions {
  offset?: number;
  smooth?: boolean;
  content?: React.ReactNode;
  enabled?: boolean;
  scrollDelay?: number;
}

export function useAutoScroll(options: UseAutoScrollOptions = {}) {
  const { offset = 20, smooth = false, content, enabled = true, scrollDelay = 0 } = options;
  const scrollRef = useRef<HTMLDivElement>(null);
  const lastContentHeight = useRef(0);
  const userHasScrolled = useRef(false);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const [scrollState, setScrollState] = useState<ScrollState>({
    isAtBottom: true,
    autoScrollEnabled: true,
  });

  const checkIsAtBottom = useCallback(
    (element: HTMLElement) => {
      const { scrollTop, scrollHeight, clientHeight } = element;
      const distanceToBottom = Math.abs(
        scrollHeight - scrollTop - clientHeight,
      );
      return distanceToBottom <= offset;
    },
    [offset],
  );

  const scrollToBottom = useCallback(
    (instant?: boolean) => {
      if (!scrollRef.current || !enabled) return;

      // Clear any pending scroll timeout
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      const performScroll = () => {
        if (!scrollRef.current) return;

        const targetScrollTop =
          scrollRef.current.scrollHeight - scrollRef.current.clientHeight;

        if (instant) {
          scrollRef.current.scrollTop = targetScrollTop;
        } else {
          scrollRef.current.scrollTo({
            top: targetScrollTop,
            behavior: smooth ? "smooth" : "auto",
          });
        }

        setScrollState({
          isAtBottom: true,
          autoScrollEnabled: true,
        });
        userHasScrolled.current = false;
      };

      if (scrollDelay > 0) {
        scrollTimeoutRef.current = setTimeout(performScroll, scrollDelay);
      } else {
        performScroll();
      }
    },
    [smooth, enabled, scrollDelay],
  );

  const handleScroll = useCallback(() => {
    if (!scrollRef.current || !enabled) return;

    const atBottom = checkIsAtBottom(scrollRef.current);

    setScrollState((prev) => ({
      isAtBottom: atBottom,
      // Re-enable auto-scroll if at the bottom
      autoScrollEnabled: atBottom ? true : prev.autoScrollEnabled,
    }));

    // Track user scrolling behavior
    if (!atBottom) {
      userHasScrolled.current = true;
    }
  }, [checkIsAtBottom, enabled]);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element || !enabled) return;

    element.addEventListener("scroll", handleScroll, { passive: true });
    return () => element.removeEventListener("scroll", handleScroll);
  }, [handleScroll, enabled]);

  useEffect(() => {
    const scrollElement = scrollRef.current;
    if (!scrollElement || !enabled) return;

    const currentHeight = scrollElement.scrollHeight;
    const hasNewContent = currentHeight !== lastContentHeight.current;

    if (hasNewContent && scrollState.autoScrollEnabled) {
      requestAnimationFrame(() => {
        scrollToBottom(lastContentHeight.current === 0);
      });
      lastContentHeight.current = currentHeight;
    } else if (hasNewContent) {
      lastContentHeight.current = currentHeight;
    }
  }, [content, scrollState.autoScrollEnabled, scrollToBottom, enabled]);

  useEffect(() => {
    const element = scrollRef.current;
    if (!element || !enabled) return;

    const resizeObserver = new ResizeObserver(() => {
      if (scrollState.autoScrollEnabled) {
        scrollToBottom(true);
      }
    });

    resizeObserver.observe(element);
    return () => resizeObserver.disconnect();
  }, [scrollState.autoScrollEnabled, scrollToBottom, enabled]);

  const disableAutoScroll = useCallback(() => {
    const atBottom = scrollRef.current
      ? checkIsAtBottom(scrollRef.current)
      : false;

    // Only disable if not at bottom
    if (!atBottom) {
      userHasScrolled.current = true;
      setScrollState((prev) => ({
        ...prev,
        autoScrollEnabled: false,
      }));
    }
  }, [checkIsAtBottom]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  return {
    scrollRef,
    isAtBottom: scrollState.isAtBottom,
    autoScrollEnabled: scrollState.autoScrollEnabled,
    scrollToBottom: () => scrollToBottom(false),
    disableAutoScroll,
  };
}
