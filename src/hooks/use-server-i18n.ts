/**
 * Client-side hook that uses server-provided translations
 * Eliminates FOUC and ensures SEO-friendly content
 */

import { useRouter } from 'next/router';
import { useMemo } from 'react';
import type { NamespaceTranslations, SupportedLocale } from '@/lib/i18n';

interface ServerI18nProps {
  locale: SupportedLocale;
  translations: NamespaceTranslations;
}

/**
 * Hook that uses server-provided translations for instant rendering
 * No loading states, no FOUC, SEO-friendly
 */
export function useServerI18n({ locale, translations }: ServerI18nProps) {
  const router = useRouter();
  
  const t = useMemo(() => {
    return (
      key: string,
      fallback?: string,
      variables?: Record<string, string | number>
    ): string => {
      let text: string | undefined;

      // Check if key uses namespace syntax
      if (key.includes(':')) {
        const [namespace, ...keyParts] = key.split(':');
        const keyPath = keyParts.join(':');
        
        if (namespace && translations[namespace]) {
          text = getNestedValue(translations[namespace], keyPath);
        }
      }

      text = text || fallback || key;

      // Simple variable interpolation
      if (variables && typeof text === 'string') {
        Object.entries(variables).forEach(([varKey, value]) => {
          text = text!.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
        });
      }

      return text;
    };
  }, [translations]);

  return {
    t,
    locale,
    isEnglish: locale === 'en',
    isPortuguese: locale === 'pt',
    isSpanish: locale === 'es',
    // Navigation helpers
    switchLocale: (newLocale: SupportedLocale) => {
      router.push(router.asPath, router.asPath, { locale: newLocale });
    },
    getLocalizedPath: (path: string, targetLocale?: SupportedLocale) => {
      const targetLoc = targetLocale || locale;
      return targetLoc === 'en' ? path : `/${targetLoc}${path}`;
    },
  };
}

/**
 * Get nested translation value using dot notation
 */
function getNestedValue(obj: any, path: string): string | undefined {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Component wrapper for server-side translations
 */
interface TProps {
  k: string;
  fallback?: string;
  variables?: Record<string, string | number>;
  translations: NamespaceTranslations;
  locale: SupportedLocale;
}

export function ServerT({ k, fallback, variables, translations, locale }: TProps) {
  const { t } = useServerI18n({ locale, translations });
  return <>{t(k, fallback, variables)}</>;
}