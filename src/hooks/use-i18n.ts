/**
 * Global i18n hook for the entire application
 * Integrates with existing language utilities and supports namespace-based translations
 */

import React, { useMemo, useEffect, useState } from 'react';
import { useUserLanguage } from '@/lib/i18n/utils';
import { 
  mapLanguageCode, 
  loadNamespacedTranslations,
  type SupportedLocale, 
  type TranslationMessages,
  type NamespaceTranslations
} from '@/lib/i18n';

interface TProps {
  k: string;
  fallback?: string;
  variables?: Record<string, string | number>;
}

/**
 * Global hook for internationalization across the entire app
 * Supports both legacy dot notation and new namespace syntax
 */
export function useI18n() {
  const userLanguage = useUserLanguage(); // Your existing hook
  const locale = useMemo(() => mapLanguageCode(userLanguage), [userLanguage]);
  
  const [namespacedTranslations, setNamespacedTranslations] = useState<NamespaceTranslations>({});
  const [isLoading, setIsLoading] = useState(true);

  // Load translations when locale changes
  useEffect(() => {
    let mounted = true;
    
    const loadMessages = async () => {
      setIsLoading(true);
      try {
        // Load namespaced translations
        const [namespacedMessages] = await Promise.all([
          loadNamespacedTranslations(locale)
        ]);
        
        if (mounted) {
          setNamespacedTranslations(namespacedMessages);
        }
      } catch (error) {
        console.warn('Failed to load translations:', error);
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    loadMessages();
    
    return () => {
      mounted = false;
    };
  }, [locale]);

  /**
   * Get nested translation value using dot notation
   */
  const getNestedValue = (obj: any, path: string): string | undefined => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  /**
   * Translation function with namespace support
   * @param key - Translation key (supports 'namespace:key.path' or legacy 'key.path')
   * @param fallback - Fallback text if translation not found
   * @param variables - Variables to interpolate
   */
  const t = (
    key: string, 
    fallback?: string, 
    variables?: Record<string, string | number>
  ): string => {
    let text: string | undefined;

    // Check if key uses namespace syntax
    if (key.includes(':')) {
      const [namespace, ...keyParts] = key.split(':');
      const keyPath = keyParts.join(':');
      
      if (namespace && namespacedTranslations[namespace]) {
        text = getNestedValue(namespacedTranslations[namespace], keyPath);
      }
    }

    text = text || fallback || key;

    // Simple variable interpolation
    if (variables && typeof text === 'string') {
      Object.entries(variables).forEach(([varKey, value]) => {
        text = text!.replace(new RegExp(`\\{${varKey}\\}`, 'g'), String(value));
      });
    }

    return text;
  };

  return {
 t, locale, isLoading, // Utility functions
    isEnglish: locale === 'en',
    isPortuguese: locale === 'pt', 
    isSpanish: locale === 'es',
    // New namespace utilities
    hasNamespace: (namespace: string) => !!namespacedTranslations[namespace],
    getNamespaceKeys: (namespace: string) => Object.keys(namespacedTranslations[namespace] || {}),
    namespaces: Object.keys(namespacedTranslations)
  };
}

/**
 * Simple component wrapper for translations
 * Usage: <T k="common:buttons.save" fallback="Save" /> or legacy <T k="common.buttons.save" fallback="Save" />
 */
export function T({ k, fallback, variables }: TProps) {
  const { t } = useI18n();
  return React.createElement(React.Fragment, null, t(k, fallback, variables));
} 