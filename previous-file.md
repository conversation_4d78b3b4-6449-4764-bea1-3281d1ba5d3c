PS C:\Users\<USER>\cursor\aromachat-0725> git show b1be08199d2fcd4bb0d48fb829641e6f77ec9e05:src/features/dashboard/profile/user-profile-modal.tsx
'use client'

import { UserProfile } from '@clerk/nextjs'
import { Settings, Globe } from 'lucide-react'
import { CustomMetadataForm, LanguageSettingsForm } from './metadata-forms'
import { createPortal } from 'react-dom'
import { useEffect, useState } from 'react'
import { useUser } from '@clerk/nextjs'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { Loader2, Save } from 'lucide-react'
import { setLanguageCookie } from '@/lib/actions/cookie.actions'

interface UserProfileModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

/**
 * Modal version of the UserProfile component
 * Uses React Portal to render outside the normal DOM tree
 */
export function UserProfileModal({ open, onOpenChange }: UserProfileModalProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    // Handle ESC key to close modal
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onOpenChange(false)
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEsc)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEsc)
      document.body.style.overflow = 'unset'
    }
  }, [open, onOpenChange])

  if (!mounted || !open) return null

  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop with blur and darker transparency */}
      <div
        className="absolute inset-0 bg-black/70 backdrop-blur-sm"
        onClick={() => onOpenChange(false)}
      />

      {/* Modal content */}
      <div className="relative z-10 max-w-4xl max-h-[90vh] w-full mx-4 overflow-hidden">
        <UserProfile routing="hash">
          {/* 1. Profile (Account) - Clerk's default page */}
          <UserProfile.Page label="account" />

          {/* 2. Preferences - Custom page for bio */}
          <UserProfile.Page
            label="Preferences"
            labelIcon={<Settings className="w-4 h-4" />}
            url="preferences"
          >
            <PreferencesPage />
          </UserProfile.Page>

          {/* 3. Language - Custom page for language selection */}
          <UserProfile.Page
            label="Language"
            labelIcon={<Globe className="w-4 h-4" />}
            url="language"
          >
            <LanguagePage />
          </UserProfile.Page>

          {/* 4. Security - Clerk's default page */}
          <UserProfile.Page label="security" />
        </UserProfile>
      </div>
    </div>,
    document.body
  )
}

/**
 * Custom preferences page component - matches native Clerk styling
 */
function PreferencesPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Preferences</h2>
        <p className="text-sm text-muted-foreground">Manage your personal preferences and settings.</p>
      </div>

      <CustomMetadataFormNative />
    </div>
  )
}

/**
 * Custom language page component - matches native Clerk styling
 */
function LanguagePage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Language & Region</h2>
        <p className="text-sm text-muted-foreground">Set your preferred language and regional settings.</p>
      </div>

      <LanguageSettingsFormNative />
    </div>
  )
}

/**
 * Native-styled metadata form without card wrapper
 */
function CustomMetadataFormNative() {
  const { user } = useUser()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  const [formData, setFormData] = useState({
    bio: (user?.unsafeMetadata?.['bio'] as string) || '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setIsUpdating(true)
    try {
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          bio: formData.bio,
          updatedAt: new Date().toISOString(),
        },
      });

      toast({
        title: 'Preferences updated!',
        description: 'Your preferences have been saved successfully.',
      });
    } catch (error) {
      console.error('❌ Update error:', error)
      toast({
        title: 'Update failed',
        description: 'There was an error updating your preferences.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-4">Personal Information</h3>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="bio" className="text-sm font-medium">Bio</Label>
            <Textarea
              id="bio"
              placeholder="Tell us about yourself..."
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
              className="min-h-[100px] resize-none"
            />
            <div className="text-xs text-muted-foreground">
              {formData.bio.length}/180 characters
            </div>
          </div>

          <Button type="submit" disabled={isUpdating} className="w-full">
            {isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </>
            )}
          </Button>
        </form>
      </div>
    </div>
  )
}

/**
 * Native-styled language form without card wrapper
 */
function LanguageSettingsFormNative() {
  const { user } = useUser()
  const { toast } = useToast()
  const [isUpdating, setIsUpdating] = useState(false)

  const [language, setLanguage] = useState(
    (user?.unsafeMetadata?.['language'] as string) || 'en'
  )

  const handleLanguageChange = async (newLanguage: string) => {
    if (!user) return

    setIsUpdating(true)
    try {
      await user.update({
        unsafeMetadata: {
          ...user.unsafeMetadata,
          language: newLanguage,
          updatedAt: new Date().toISOString(),
        },
      });

      const cookieResult = await setLanguageCookie(newLanguage);
      if (!cookieResult.success) {
        console.warn('Failed to set language cookie:', cookieResult.error);
      }

      setLanguage(newLanguage)
      toast({
        title: 'Language updated!',
        description: `Language changed to ${getLanguageName(newLanguage)}.`,
      });
    } catch (error) {
      console.error('❌ Language update error:', error)
      toast({
        title: 'Update failed',
        description: 'There was an error updating your language preference.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdating(false)
    }
  }

  const getLanguageName = (code: string) => {
    const languages: Record<string, string> = {
      en: 'English',
      pt: 'Português',
      es: 'Español',
    }
    return languages[code] || code
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium mb-4">Language Preference</h3>

        <div className="space-y-3">
          <Label className="text-sm font-medium">Choose your preferred language</Label>
          <div className="space-y-2">
            {[
              { code: 'en', name: 'English' },
              { code: 'pt', name: 'Português' },
              { code: 'es', name: 'Español' },
            ].map((lang) => (
              <Button
                key={lang.code}
                variant={language === lang.code ? 'default' : 'outline'}
                onClick={() => handleLanguageChange(lang.code)}
                disabled={isUpdating}
                className="w-full justify-start"
              >
                {isUpdating && language === lang.code && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                {lang.name}
              </Button>
            ))}
          </div>

          <div className="text-sm text-muted-foreground">
            Current selection: <strong>{getLanguageName(language)}</strong>
          </div>
        </div>
      </div>
    </div>
  )
}