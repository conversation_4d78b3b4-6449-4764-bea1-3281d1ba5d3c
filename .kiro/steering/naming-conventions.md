---
inclusion: always
---

# Naming Conventions

## Keys

### Translation Keys
- Use `snake_case` for all translation keys
- Structure hierarchically with underscores
- Be descriptive and specific

```typescript
// Good
welcome_message
user_profile_edit_button
error_validation_email_invalid

// Bad
welcomeMsg
editBtn
emailErr
```

### Config Keys
- Use `camelCase` for JavaScript/TypeScript config objects
- Use `SCREAMING_SNAKE_CASE` for environment variables

```typescript
// Config objects
const config = {
  apiBaseUrl: process.env.API_BASE_URL,
  maxRetries: 3,
  timeoutMs: 5000
}

// Environment variables
API_BASE_URL=https://api.example.com
MAX_RETRY_ATTEMPTS=3
DATABASE_CONNECTION_TIMEOUT=5000
```

### API Keys and Identifiers
- Use `snake_case` para colunas do banco de dados
- Use `camelCase` para tipos TypeScript e objetos de resposta da API
- Use `kebab-case` para URLs, parâmetros de URL e headers

```typescript
// Database columns (snake_case)
user_id, first_name, created_at

// API TypeScript types and responses (camelCase)
interface UserProfile {
  userId: string;
  firstName: string;
  createdAt: string;
}

// URLs e Headers (kebab-case)
/api/user-profile/123?sort-by=created-at
x-api-key, content-type
```

## Filenames

### Component Files
- Use `kebab-case` for React components
- Include component type in name when helpful
- Use descriptive prefixes for feature-specific components

```
user-profile.tsx
login-form.tsx
navigation-menu.tsx
loading-spinner.tsx
dashboard-header.tsx
profile-banner-uploader.tsx
```

### Utility and Library Files
- Use `kebab-case` for utilities, helpers, and services
- Use `kebab-case` for configuration files

```
// Utilities and Services
profile.service.ts
server-auth.utils.ts
streaming-utils.ts
language-utils.ts
error-handler.ts

// Config files
next.config.ts
tailwind.config.ts
jest.config.js
```

### Page Files
- Use `kebab-case` for Next.js page routes
- Match URL structure exactly

```
// Pages
page.tsx (for /)
about/page.tsx (for /about)
user-profile/page.tsx (for /user-profile)
api/auth/login/route.ts (for /api/auth/login)
```

### Directory Names
- Use `kebab-case` for directories
- Be descriptive and consistent

```
src/
├── components/
├── lib/
├── app/
│   ├── user-profile/
│   ├── auth/
│   └── api/
└── types/
```

## React Hooks

### Custom Hook Names
- Sempre comece com o prefixo "use"
- Use nomes descritivos que indiquem a funcionalidade
- Mantenha consistência com hooks similares no projeto

```typescript
// Good
useAuthSession()
useUserProfile()
useProfileUpdate()
useServerI18n()
useImageUpload()

// Bad
userProfile()
getUser()
profile()
```


### Hook File Names
- O nome do arquivo do hook deve corresponder ao nome do hook exportado, usando kebab-case
- Use `kebab-case` para o nome do arquivo, mantendo o prefixo "use-"
- Mantenha a consistência com o restante do projeto

```
use-user-profile.ts
use-api-request.ts
use-local-storage.ts
use-toast.ts
use-mobile.tsx
```

## Functions

### Function Names
- Use `camelCase` for all functions
- Use verbs that describe the action
- Be specific about what the function does

```typescript
// Good
getUserById(id: string)
validateEmailFormat(email: string)
formatCurrency(amount: number)
handleSubmitForm()
calculateTotalPrice()

// Bad
user(id)
email(email)
format(amount)
submit()
calculate()
```

### Function Categories

#### Event Handlers
- Prefixe com "handle" para handlers internos do componente
- Prefixe com "on" para props de callback
- Use nomes descritivos que indiquem a ação

```typescript
// Component handlers
handleImageProcessing()
handleSubmitForm()
handleProfileUpdate()

// Callback props
onGoogleLibraryLoad()
onUserLogin()
onChange()
```

#### Service Functions
- Use nomes descritivos que indiquem a ação
- Agrupe funções relacionadas em services
- Use sufixo .service.ts para arquivos de serviço

```typescript
// Em profile.service.ts
updateProfile()
uploadProfileImage()
deleteProfileImage()

// Em auth.service.ts
signInWithGoogle()
signOut()
resetPassword()
```

#### Utils Functions
- Agrupe funções utilitárias por domínio
- Use sufixo .utils.ts para arquivos de utilidades
- Mantenha funções pequenas e focadas

```typescript
// Em server-auth.utils.ts
getSessionFromRequest()
validateToken()

// Em language-utils.ts
getLocaleFromRequest()
loadNamespacedTranslations()
```

## Constants

### Constant Names
- Use `SCREAMING_SNAKE_CASE` apenas para variáveis de ambiente
- Use `camelCase` para constantes do módulo e locais
- Use const para prevenir reatribuições

```typescript
// Environment variables (SCREAMING_SNAKE_CASE)
process.env.NODE_ENV
process.env.API_BASE_URL
process.env.DATABASE_CONNECTION_TIMEOUT

// Module constants (camelCase)
const defaultLocale = 'en';
const locales = ['en', 'pt', 'es'] as const;
const excludedPaths = ['/api', '/images'];

// Local constants (camelCase)
function processData() {
  const originalError = console.error;
  const shouldLog = process.env.NODE_ENV === 'development';
}
```

## Type Definitions

### Interface e Type Names
- Use `PascalCase` para interfaces e tipos
- Seja específico e descritivo
- Use tipos exportados de bibliotecas quando disponível

```typescript
// Interfaces para configurações
interface PineconeConfig {
  apiKey: string;
  environment: string;
}

// Types para props de componentes
type ThemeProviderProps = {
  children: ReactNode;
  defaultTheme?: string;
}

// Types para valores específicos
type SupportedLocale = 'en' | 'pt' | 'es';

// Types importados de bibliotecas
import { User } from '@supabase/supabase-js';
import { ClassValue } from 'clsx';
```

### Convenções de Tipagem
- Use `type` para unions, intersections e tipos mais simples
- Use `interface` para objetos que podem ser estendidos
- Importe tipos com `type` keyword para clareza

```typescript
// Import de tipos
import { type ReactNode } from 'react';
import { type User } from '@supabase/supabase-js';

// Union types
type AuthMethod = 'google' | 'email' | 'github';

// Interface extensível
interface BaseConfig {
  version: string;
}

interface ServiceConfig extends BaseConfig {
  endpoint: string;
}
```