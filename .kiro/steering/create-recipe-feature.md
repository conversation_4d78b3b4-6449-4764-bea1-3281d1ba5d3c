---
inclusion: fileMatch
fileMatchPattern: 'src/features/create-recipe/**/*'
---

# Create Recipe Feature Steering Rules

## Feature Overview
The Essential Oil Recipe Creator is a production-ready, AI-powered 6-step wizard that guides users through creating personalized essential oil recommendations. It features advanced streaming capabilities, parallel processing, and comprehensive TypeScript compliance.

## Architecture Principles

### 1. Modular Streaming Architecture
- **Generic Engine**: Use `src/lib/ai/hooks/use-parallel-streaming-engine.ts` for reusable streaming logic
- **Feature-Specific Wrapper**: Implement business logic in `src/features/create-recipe/hooks/use-create-recipe-streaming.ts`
- **Separation of Concerns**: Keep generic streaming separate from feature-specific business logic
- **Configurable Response Parsers**: Use configurable parsers for different API response structures

### 2. State Management Patterns
- **Zustand Store**: Use `src/features/create-recipe/store/recipe-store.ts` for all state management
- **Non-Persistent State**: Data intentionally not persisted - browser refresh clears all state
- **Optimized Updates**: Only update state when values actually change to prevent unnecessary re-renders
- **Batched Updates**: Use batch update methods for multiple state changes

### 3. Component Organization
```
components/
├── wizard-container.tsx      # Main wizard orchestrator
├── [step]-form.tsx          # Individual step components
├── [feature]-display.tsx    # Display components for results
├── loading-skeletons.tsx    # Loading states
└── error-boundary.tsx       # Error handling
```

## Development Standards

### TypeScript Compliance
- **Strict Mode**: All code must pass TypeScript strict mode
- **Explicit Types**: No implicit `any` types allowed
- **Type Definitions**: Use types from `src/features/create-recipe/types/recipe.types.ts`
- **Schema Validation**: Use Zod schemas from `src/features/create-recipe/schemas/recipe-schemas.ts`

### Form Management
- **React Hook Form**: Use for all form handling
- **Zod Validation**: Integrate Zod schemas with React Hook Form
- **Error Handling**: Implement comprehensive form validation with user-friendly messages
- **Auto-save**: Implement auto-save functionality with 7-day retention

### AI Streaming Implementation
- **Real-time Updates**: Implement incremental progress tracking
- **Parallel Processing**: Use parallel streaming for multiple properties
- **Error Recovery**: Implement retry logic with exponential backoff
- **Progress Indicators**: Show detailed progress counters for user feedback

### Oil Enrichment Patterns
- **Safety Data**: Always enrich oils with safety information from Supabase
- **Enrichment Status**: Track enrichment status (`enriched`, `not_found`, `discarded`)
- **Botanical Matching**: Handle botanical name mismatches with similarity scoring
- **Vector Search**: Use vector similarity for oil matching when exact matches fail

## Code Patterns

### Component Structure
```typescript
interface ComponentProps {
  // Props interface
}

export const ComponentName: React.FC<ComponentProps> = ({ prop1, prop2 }) => {
  // Hooks
  const { state, actions } = useRecipeStore();
  
  // Form handling
  const form = useForm<SchemaType>({
    resolver: zodResolver(validationSchema),
    defaultValues: {}
  });
  
  // Event handlers
  const handleSubmit = async (data: SchemaType) => {
    // Implementation
  };
  
  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};
```

### API Integration
```typescript
// Use the feature-specific streaming hook
const { 
  streamingResults, 
  isStreaming, 
  error, 
  startStreaming 
} = useCreateRecipeStreaming();

// Handle streaming responses
useEffect(() => {
  if (streamingResults.size > 0) {
    // Process results
    const results = Array.from(streamingResults.values());
    // Update store
  }
}, [streamingResults]);
```

### Store Updates
```typescript
// Prefer specific update methods
updateTherapeuticProperties(properties, 'streaming-response');

// Use batched updates for multiple changes
batchUpdateStepData({
  currentStep: RecipeStep.PROPERTIES,
  therapeuticProperties: newProperties
});
```

## File Naming Conventions

### Components
- `wizard-container.tsx` - Main container
- `[step-name]-form.tsx` - Step forms (e.g., `health-concern-form.tsx`)
- `[feature]-display.tsx` - Display components (e.g., `properties-display.tsx`)
- `[feature]-selection.tsx` - Selection components (e.g., `causes-selection.tsx`)

### Hooks
- `use-[feature-name].ts` - Feature-specific hooks
- `use-[action]-[entity].ts` - Action-specific hooks (e.g., `use-recipe-navigation.ts`)

### Utilities
- `[feature]-[purpose].ts` - Utility functions (e.g., `api-data-transform.ts`)
- `type-guards.ts` - Type guard functions
- `local-storage.ts` - Storage utilities

## Testing Requirements

### Test Coverage
- **103+ Tests**: Maintain comprehensive test coverage
- **Component Tests**: Test all React components
- **Hook Tests**: Test custom hooks in isolation
- **Store Tests**: Test Zustand store actions and state changes
- **Schema Tests**: Test Zod validation schemas

### Test Structure
```typescript
describe('ComponentName', () => {
  beforeEach(() => {
    // Setup
  });
  
  it('should handle expected behavior', () => {
    // Test implementation
  });
  
  it('should handle error cases', () => {
    // Error testing
  });
});
```

## Performance Optimization

### Rendering Optimization
- **Selective Re-renders**: Use specific store selectors to prevent unnecessary re-renders
- **Memoization**: Use `useMemo` and `useCallback` for expensive computations
- **Lazy Loading**: Implement lazy loading for heavy components
- **Skeleton Loading**: Use loading skeletons for better UX

### State Management Optimization
- **Minimal Updates**: Only update state when values actually change
- **Batched Operations**: Use batch update methods for multiple state changes
- **Selective Subscriptions**: Use specific store selectors instead of full store

### API Optimization
- **Streaming**: Use streaming APIs for real-time updates
- **Parallel Processing**: Process multiple requests simultaneously
- **Caching**: Implement appropriate caching strategies
- **Error Recovery**: Implement retry logic with exponential backoff

## Error Handling

### Component Level
```typescript
try {
  // Operation
} catch (error) {
  console.error('Component error:', error);
  setError(error.message);
}
```

### Store Level
```typescript
setError: (error: string | null) => {
  set((state) => ({
    error,
    isLoading: false,
    lastUpdated: new Date()
  }));
}
```

### Streaming Level
```typescript
setStreamingError: (error: string | null) => {
  set(() => ({
    streamingError: error,
    isStreamingCauses: false,
    isStreamingSymptoms: false,
    isStreamingProperties: false,
    isStreamingOils: false,
    lastUpdated: new Date()
  }));
}
```

## Accessibility Requirements

### Form Accessibility
- **Labels**: All form inputs must have proper labels
- **ARIA**: Use appropriate ARIA attributes
- **Keyboard Navigation**: Support full keyboard navigation
- **Screen Readers**: Ensure compatibility with screen readers

### Component Accessibility
- **Focus Management**: Proper focus management in wizard navigation
- **Color Contrast**: Ensure sufficient color contrast
- **Alternative Text**: Provide alt text for images and icons
- **Semantic HTML**: Use semantic HTML elements

## Mobile-First Design

### Responsive Components
- **Mobile-First**: Design for mobile first, then enhance for desktop
- **Touch Targets**: Ensure touch targets are at least 44px
- **Viewport**: Use appropriate viewport meta tags
- **Breakpoints**: Use consistent breakpoint system

### Layout Patterns
```typescript
// Use mobile-layout.tsx for mobile-specific layouts
import { MobileLayout } from './mobile-layout';

// Implement responsive design patterns
const Component = () => (
  <div className="container mx-auto px-4 sm:px-6 lg:px-8">
    {/* Content */}
  </div>
);
```

## Security Considerations

### Input Validation
- **Server-Side Validation**: Always validate on server side
- **Sanitization**: Sanitize user inputs
- **XSS Prevention**: Prevent cross-site scripting attacks
- **CSRF Protection**: Implement CSRF protection

### API Security
- **Authentication**: Ensure proper authentication
- **Authorization**: Implement proper authorization
- **Rate Limiting**: Implement rate limiting
- **Input Validation**: Validate all API inputs

## Debugging and Monitoring

### Debug Infrastructure
```typescript
// Use comprehensive logging
console.log('🔄 [Component] Starting operation:', { data });
console.log('✅ [Component] Operation completed:', { result });
console.log('❌ [Component] Operation failed:', { error });
```

### Development Tools
- **Debug Overlay**: Use `recipe-debug-overlay.tsx` for development debugging
- **State Inspection**: Implement state inspection tools
- **Performance Monitoring**: Monitor component performance
- **Error Tracking**: Implement error tracking and reporting

## Migration Guidelines

### Backward Compatibility
- **API Compatibility**: Maintain 100% API compatibility when refactoring
- **Component Interfaces**: Preserve component interfaces
- **Store Structure**: Maintain store structure compatibility
- **Type Definitions**: Preserve existing type definitions

### Refactoring Approach
1. **Create New Implementation**: Build new implementation alongside existing
2. **Gradual Migration**: Migrate components one at a time
3. **Testing**: Ensure comprehensive testing during migration
4. **Cleanup**: Remove deprecated code after successful migration

## Documentation Requirements

### Code Documentation
- **JSDoc Comments**: Use JSDoc for complex functions
- **Type Documentation**: Document complex types
- **Component Documentation**: Document component props and usage
- **Hook Documentation**: Document custom hooks

### README Updates
- **Feature Changes**: Update README for significant changes
- **Architecture Changes**: Document architecture modifications
- **Breaking Changes**: Clearly document breaking changes
- **Migration Guides**: Provide migration guides for major changes

## Common Pitfalls to Avoid

### State Management
- ❌ Don't mutate state directly
- ❌ Don't use JSON.stringify for state comparisons
- ❌ Don't subscribe to entire store when only specific data is needed
- ❌ Don't forget to update `lastUpdated` timestamp

### Component Development
- ❌ Don't use implicit `any` types
- ❌ Don't forget error boundaries
- ❌ Don't skip accessibility attributes
- ❌ Don't ignore mobile responsiveness

### API Integration
- ❌ Don't ignore error handling
- ❌ Don't forget retry logic
- ❌ Don't skip input validation
- ❌ Don't ignore loading states

### Performance
- ❌ Don't cause unnecessary re-renders
- ❌ Don't skip memoization for expensive operations
- ❌ Don't ignore bundle size impact
- ❌ Don't skip lazy loading for heavy components

## Best Practices Summary

1. **Follow TypeScript strict mode** - No exceptions
2. **Use feature-specific streaming hook** - Don't use generic hooks directly
3. **Implement comprehensive error handling** - At all levels
4. **Optimize for performance** - Prevent unnecessary re-renders
5. **Maintain accessibility** - Follow WCAG guidelines
6. **Write comprehensive tests** - Maintain 100+ test coverage
7. **Document significant changes** - Keep documentation current
8. **Follow mobile-first design** - Design for mobile, enhance for desktop
9. **Use consistent naming conventions** - Follow established patterns
10. **Implement proper debugging** - Use comprehensive logging

## Quick Reference

### Key Files
- **Store**: `src/features/create-recipe/store/recipe-store.ts`
- **Types**: `src/features/create-recipe/types/recipe.types.ts`
- **Schemas**: `src/features/create-recipe/schemas/recipe-schemas.ts`
- **Streaming Hook**: `src/features/create-recipe/hooks/use-create-recipe-streaming.ts`
- **Main Container**: `src/features/create-recipe/components/wizard-container.tsx`

### Key Patterns
- Use `useRecipeStore()` for state management
- Use `useCreateRecipeStreaming()` for AI streaming
- Use Zod schemas for validation
- Use React Hook Form for form management
- Use TypeScript strict mode for all code