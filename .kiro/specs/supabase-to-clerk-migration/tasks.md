# Implementation Plan

- [x] 1. Setup Clerk Foundation and Middleware Integration

  - Validate Clerk environment variables are properly configured (already done)
  - Create SSO callback page for OAu- [ ] 6. Database Integration Updates (Critical for user data)
  - ✅ Profile modernization completed (GOOD - keep this approach)
  - ⚠️ **CRITICAL ISSUE FOUND**: Current implementation is OVER-ENGINEERED
  - 🚨 **RECOMMENDATION**: Replace complex implementation with KISS approach
  - **See**: `.kiro/CRITICAL-REVIEW-OVER-ENGINEERING.md` for details
  
  **SIMPLIFIED APPROACH READY**:
  - ✅ Created `use-simple-auth.ts` - 40 lines vs 252 lines (85% reduction)
  - ✅ Created `middleware-simple.ts` - Simple i18n + auth protection 
  - ✅ Created `simple-login-form.tsx` - OAuth-only approach
  - ❌ **DELETE**: Over-engineered service layers, providers, complex hooks
  
  **NEXT**: Replace current complex files with simple implementations
  _Requirements: 8.2, 8.4_rects
  - Update middleware.ts (171 lines) to integrate Clerk authentication with existing i18n routing
  - _Requirements: 1.1, 1.2, 4.3, 5.5_

- [x] 1.1 Validate Clerk Configuration


  - Verify environment variables are already configured and working
  - Confirm NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY and CLERK_SECRET_KEY are set
  - Test basic Clerk connection without breaking existing functionality
  - _Requirements: 1.1, 1.2_

- [x] 1.2 Create SSO Callback Page


  - Create new file src/app/sso-callback/page.tsx
  - Implement simple AuthenticateWithRedirectCallback component from Clerk
  - Handle OAuth redirect completion for Google and Microsoft authentication
  - _Requirements: 2.1, 2.2, 2.3_

- [x] 1.3 Update Middleware with Clerk Integration


  - Replace entire middleware.ts file (171 lines) with Clerk middleware integration
  - CRITICAL: Preserve existing i18n routing functionality (en, pt, es) exactly as is
  - Implement route protection using createRouteMatcher for dashboard and profile routes
  - Maintain all existing excluded paths for static assets
  - Add OAuth callback route handling
  - _Requirements: 4.1, 4.2, 4.3, 5.1, 5.2, 5.4, 5.5_

- [x] 2. Create Clerk Service Layer

  - Replace existing auth-state.service.ts (103 lines) with Clerk equivalent
  - Replace existing auth.actions.ts with Clerk server actions
  - Create new Clerk auth service with same interface patterns
  - _Requirements: 1.1, 1.2, 6.4, 8.2, 8.3_

- [x] 2.1 Create Clerk Auth State Service


  - Create new file src/features/auth/services/clerk-auth-state.service.ts
  - Implement getServerClerkAuthState as direct replacement for getServerAuthState function
  - Use React cache() for deduplication (existing pattern from auth-state.service.ts)
  - Handle static generation errors gracefully (critical for npm run build)
  - Maintain existing logging patterns with getServerLogger
  - _Requirements: 1.2, 8.2, 8.3_

- [x] 2.2 Create Clerk Auth Actions


  - Create new file src/features/auth/actions/clerk-auth.actions.ts
  - Replace server actions from auth.actions.ts with Clerk equivalents
  - Implement getCurrentUser server action using Clerk's currentUser()
  - Implement getAuthSession server action using Clerk's auth()
  - Implement signOutAction with proper redirect handling
  - Maintain existing error handling patterns and logging
  - _Requirements: 1.2, 8.2, 8.3_

- [x] 2.3 Create Clerk Auth Service


  - Create new file src/features/auth/services/clerk-auth.service.ts
  - Replace client-facing auth operations from auth.service.ts (161 lines)
  - Implement cached user profile retrieval using React cache
  - Create authentication status management functions
  - Add error handling with Sentry integration (existing pattern)
  - Define TypeScript interfaces matching existing patterns
  - _Requirements: 1.2, 8.1, 8.2, 8.5_

- [x] 3. Update Root Layout and Provider Integration

  - Update src/app/layout.tsx (103 lines) with specific line changes
  - Replace AuthSessionProvider with ClerkAuthSessionProvider
  - Wrap application with ClerkProvider
  - _Requirements: 1.1, 6.3, 8.1_

- [x] 3.1 Update Root Layout with ClerkProvider


  - Update src/app/layout.tsx line 9: Replace getServerAuthState import with getServerClerkAuthState
  - Add ClerkProvider import on line 1
  - Update line 40: Replace getServerAuthState() call with getServerClerkAuthState()
  - Wrap entire app with ClerkProvider around line 68
  - Test that layout still renders correctly
  - _Requirements: 1.1, 6.3, 8.1_

- [x] 3.2 Create Clerk Auth Session Provider


  - Create new file src/providers/clerk-auth-session-provider.tsx
  - Replace AuthSessionProvider functionality (167 lines) with Clerk equivalent
  - Use useUser() hook from @clerk/nextjs instead of Supabase
  - Maintain existing context pattern and interface
  - Add Sentry integration for error tracking (existing pattern)
  - Manage loading states and user session synchronization
  - _Requirements: 6.3, 8.1, 8.2, 8.5_

- [x] 3.3 Update Server Auth Utils


  - Update src/lib/auth/server-auth.utils.ts (105 lines)
  - Replace line 5 import: getServerAuthState with getServerClerkAuthState
  - Update function call in getServerAuthWithProfilePrefetch
  - Create getServerClerkAuthWithProfilePrefetch function
  - _Requirements: 8.2_

- [x] 4. Migrate Authentication Components

  - Replace useAuth hook (254 lines) with Clerk equivalent maintaining same interface
  - Replace login form (318 lines) with Clerk implementation
  - Update hook exports for backward compatibility during migration
  - _Requirements: 1.2, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 3.4, 3.5, 8.1, 8.2_

- [x] 4.1 Create Clerk Auth Hook


  - Create new file src/features/auth/hooks/use-clerk-auth.ts
  - Replace useAuth hook (254 lines) maintaining exact same interface
  - Return same shape: { user, profile, authUser, isAuthenticated, isLoading, error, profileError, retry, clearErrors }
  - Integrate with ClerkAuthSessionProvider and existing user profile queries
  - Maintain performance monitoring patterns (existing pattern)
  - Add retry logic with exponential backoff for profile errors
  - Implement error handling and Sentry integration (existing patterns)
  - _Requirements: 8.1, 8.2, 8.4, 8.5_

- [x] 4.2 Update Hook Exports for Backward Compatibility


  - Update src/features/auth/hooks/index.ts
  - Add export for useClerkAuth
  - Add alias: export { useClerkAuth as useAuth } for backward compatibility during migration
  - This allows gradual migration of components
  - _Requirements: 8.1_

- [x] 4.3 Replace Login Form Component


  - Replace src/features/auth/components/login-form.tsx completely (318 lines)
  - Use useSignIn hook from @clerk/nextjs for email/password authentication
  - Maintain existing UI structure and styling exactly
  - Preserve OAuth button functionality (Google, Microsoft)
  - Keep error handling patterns and user feedback
  - Maintain password visibility toggle
  - Integrate with existing design system components
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.3, 3.4, 3.5_

- [x] 5. Update All Components Using Auth (7 confirmed components)

  - Update all 7 components identified in senior dev review
  - Update utility files that import auth functions
  - Maintain deletion log for verification
  - _Requirements: 1.3, 7.1, 7.2, 7.3, 7.4, 7.5_

- [x] 5.1 Update Dashboard Components (3 components)


  - Update src/features/dashboard/components/dashboard-user-menu.tsx (line 20)
  - Update src/features/dashboard/dashboard-homepage/dashboard-homepage-view.tsx (line 19)
  - Update src/features/dashboard/profile/profile-view.tsx (line 7)
  - Change import pattern: useAuth → useClerkAuth as useAuth
  - Test that all dashboard functionality works correctly
  - _Requirements: 8.1, 8.2_

- [x] 5.2 Update Homepage and Auth Components (2 components)


  - Update src/features/homepage/components/hero-header/hero-header.tsx (line 14)
  - Update src/features/auth/components/reset-password-form.tsx (line 14)
  - Change import pattern: useAuth → useClerkAuth as useAuth
  - Test that homepage and auth flows work correctly
  - _Requirements: 8.1, 8.2_

- [x] 5.3 Update Recipe and Chat Components (2 components)


  - Update src/features/create-recipe/components/auth-guard.tsx (line 10)
  - Update src/features/dashboard/chat-realtime/components/chat-layout.tsx (line 4)
  - Change import pattern: useAuth → useClerkAuth as useAuth
  - Test that recipe creation and chat functionality works
  - _Requirements: 8.1, 8.2_

- [x] 5.4 Update Utility Files (3 files)


  - Update src/lib/i18n/utils/language-utils.ts (lines 1, 88)
  - Update src/features/realtime/hooks/use-unified-realtime.ts (lines 4, 72)
  - Update src/features/realtime/hooks/use-realtime-mouse-tracking.ts (line 4)
  - Change import patterns to use new Clerk auth functions
  - Test that i18n and realtime functionality works
  - _Requirements: 8.1, 8.2_



- [x] 5.5 Update Dashboard Layout
  - Update src/app/(dashboard)/layout.tsx (82 lines)
  - Replace line 2 import: getServerAuthWithProfilePrefetch → getServerClerkAuthWithProfilePrefetch
  - Test that dashboard layout renders correctly
  - _Requirements: 8.2_

- [ ] 6. Database Integration Updates (Critical for user data)
  - Update profile service to handle Clerk user IDs
  - Consider database schema changes for Clerk integration


  - Handle user ID mapping between systems
  - _Requirements: 8.2, 8.4_

- [x] 6.1 Update Profile Service for Clerk User IDs + MODERNIZATION
  - ✅ Updated src/features/user-auth-data/services/profile.service.ts - LEGACY (kept for backward compatibility)
  - ✅ Created src/features/user-auth-data/services/simplified-profile.service.ts - MODERN APPROACH
  - ✅ Updated src/features/user-auth-data/queries/profile.queries.ts - Added getUserCustomProfile()
  - ✅ Updated src/features/user-auth-data/hooks/use-user-profile-query.ts - Hybrid Clerk + Custom data
  - ✅ Created database-simplification-migration.sql - Schema modernization
  - ✅ Created profile-modernization-guide.md - Comprehensive migration guide
  
  **ARCHITECTURAL DECISION**: Simplified profile system to leverage Clerk's built-in profile management:
  - **Clerk handles**: Name, email, avatar, roles, security, password, 2FA
  - **Database stores**: Only custom business fields (bio, banner_url)
  - **UI Components**: Use Clerk's <UserProfile /> for profile management
  - **Result**: ~80% reduction in custom profile code maintenance
  
  _Requirements: 8.2, 8.4_

- [ ] 6.2 Database Schema Considerations
  - Consider adding clerk_user_id column to profiles table if needed
  - Plan migration strategy for existing user profiles
  - Document any database changes required
  - Test database operations with new user ID structure
  - _Requirements: 8.4_

- [ ] 6.3 Profile Component Modernization (NEW)
  - Replace custom profile management forms with Clerk's <UserProfile /> component
  - Update profile display components to use hybrid Clerk + custom data approach
  - Create custom forms ONLY for business-specific fields (bio, banner)
  - Remove legacy profile fields from UI (role, subscription status, etc.)
  - Test profile management with real user flows
  - _Requirements: 8.2, 8.4_

- [ ] 6.3 Handle User ID Mapping
  - Implement strategy for mapping between Supabase and Clerk user IDs
  - Consider temporary dual-ID storage during transition if needed
  - Document user data migration approach
  - Test user profile consistency
  - _Requirements: 8.4_

- [ ] 7. 🚨 **SIMPLIFIED IMPLEMENTATION (KISS Approach)**
  **PROBLEM**: Current implementation is over-engineered (20+ hours → should be 2 hours)
  **SOLUTION**: Replace complex abstractions with direct Clerk API usage
  
  **Phase 1: Replace Over-engineered Files (30 minutes)**
  - ❌ DELETE: `src/features/auth/hooks/use-clerk-auth.ts` (252 lines)
  - ❌ DELETE: `src/features/auth/services/clerk-auth.service.ts` (161 lines)  
  - ❌ DELETE: `src/features/auth/services/clerk-auth-state.service.ts` (70 lines)
  - ❌ DELETE: `src/features/auth/actions/clerk-auth.actions.ts` (wrapper actions)
  - ❌ DELETE: `src/providers/clerk-auth-session-provider.tsx` (150 lines)
  - ✅ REPLACE: Use `use-simple-auth.ts`, `middleware-simple.ts`, `simple-login-form.tsx`
  
  **Phase 2: Update Components (1 hour)**
  - Update hook exports to use simple version
  - Update all 7 components to use simplified hook
  - Update root layout with just ClerkProvider
  - Replace middleware with simple version
  
  **Phase 3: Test & Clean (30 minutes)**
  - Test OAuth flows and route protection
  - Clean up legacy files
  - Verify build works
  
  **RESULT**: 87% code reduction, easier maintenance, direct Clerk API usage
  _Requirements: 1.1, 1.2, 8.1, 8.2_

- [ ] 7.1 Delete Legacy Auth Services (2 files)
  - Delete src/features/auth/services/auth.service.ts (161 lines)
  - Delete src/features/auth/services/auth-state.service.ts (103 lines)
  - Add deleted files to cleanup log with line counts
  - Verify no remaining imports of these services
  - _Requirements: 1.3, 7.1, 7.2, 7.3_

- [ ] 7.2 Delete Legacy Auth Actions (3 files)
  - Delete src/features/auth/actions/auth.actions.ts
  - Delete src/features/auth/actions/sign-in.action.ts
  - Delete src/features/auth/actions/sign-out.action.ts
  - Add deleted files to cleanup log
  - Verify no remaining imports of these actions
  - _Requirements: 1.3, 7.1, 7.2, 7.3_

- [ ] 7.3 Delete Legacy Auth Hooks and Providers (2 files)
  - Delete src/features/auth/hooks/use-auth.ts (254 lines)
  - Delete src/providers/auth-session-provider.tsx (167 lines)
  - Add deleted files to cleanup log with line counts
  - Verify no remaining imports of these components
  - _Requirements: 1.3, 7.1, 7.2, 7.3_

- [ ] 7.4 Delete Legacy Middleware Utils
  - Delete src/features/auth/utils/middleware.utils.ts
  - Check for any Supabase-specific middleware files in lib/supabase/
  - Add deleted files to cleanup log
  - Verify new Clerk middleware handles all previous functionality
  - _Requirements: 1.3, 7.1, 7.2, 7.3_

- [ ] 7.5 Update Package Dependencies
  - Remove @supabase/ssr from package.json (no longer needed for auth)
  - Keep @supabase/supabase-js for database operations
  - Update any auth-related environment variables if needed
  - Document dependency changes in cleanup log
  - _Requirements: 1.3_

- [ ] 8. Testing and Verification (Critical for npm run build)
  - Test all authentication flows manually
  - Verify route protection works correctly
  - Test internationalization functionality
  - _Requirements: 1.1, 1.2, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 4.1, 4.2, 5.1, 5.2_

- [ ] 8.1 Test Authentication Flows
  - Test email/password login functionality
  - Test Google OAuth authentication flow
  - Test Microsoft OAuth authentication flow
  - Test sign-out functionality
  - Verify error handling for invalid credentials
  - Test all 7 updated components work correctly
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.3, 3.4, 3.5_

- [ ] 8.2 Test Route Protection and Middleware
  - Verify unauthenticated users are redirected from protected routes
  - Test authenticated users can access dashboard and profile pages
  - Verify middleware correctly handles protected route patterns
  - Test authentication state changes update route access
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [ ] 8.3 Test Internationalization Integration (Critical)
  - Verify locale detection works with new middleware
  - Test URL routing preserves locale prefixes (en, pt, es)
  - Confirm authentication components support all locales
  - Test browser locale detection and cookie preferences
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5_

- [ ] 8.4 Final Verification and Documentation
  - Run npm run build to ensure no build errors
  - Verify static page generation works correctly (100% static pages)
  - Test that middleware handles static generation gracefully
  - Confirm no dynamic server usage errors in static contexts
  - Create comprehensive deletion log with all removed files
  - Document migration completion and any remaining considerations
  - _Requirements: 1.3, 5.1, 7.1, 7.2, 7.3, 7.4, 7.5_