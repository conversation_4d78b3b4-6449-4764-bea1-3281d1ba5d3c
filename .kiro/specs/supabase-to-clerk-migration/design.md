# Design Document

## Overview

This design document outlines the architecture and implementation approach for migrating from Supabase Auth to Clerk authentication. The migration follows a systematic replacement strategy that maintains existing functionality while leveraging Clerk's built-in features for authentication, OAuth integration, and session management. The design preserves the current internationalization system and integrates seamlessly with the existing Next.js App Router architecture.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[Login Form] --> B[Clerk Auth Hook]
        C[Protected Components] --> B
        D[OAuth Buttons] --> E[Clerk OAuth]
    end
    
    subgraph "Middleware Layer"
        F[Clerk Middleware] --> G[Route Protection]
        F --> H[i18n Routing]
    end
    
    subgraph "Service Layer"
        I[Clerk Auth Service] --> J[Clerk API]
        K[Auth State Service] --> J
        L[Auth Actions] --> J
    end
    
    subgraph "Provider Layer"
        M[ClerkProvider] --> N[ClerkAuthSessionProvider]
        N --> O[Application Components]
    end
    
    subgraph "External Services"
        J --> P[Clerk Dashboard]
        P --> Q[Google OAuth]
        P --> R[Microsoft OAuth]
    end
```

### Migration Strategy

The migration follows a **complete replacement approach** where each Supabase Auth component is systematically replaced with its Clerk equivalent:

1. **Middleware Integration**: Replace custom auth middleware with Clerk's built-in middleware while preserving i18n functionality
2. **Service Layer Replacement**: Create new Clerk-based services that replace existing Supabase auth services
3. **Component Migration**: Replace authentication components with Clerk-powered equivalents
4. **Provider Updates**: Replace auth session providers with Clerk-based providers
5. **Progressive Cleanup**: Remove legacy code as new components are implemented

## Components and Interfaces

### Core Components

#### 1. Middleware Integration
- **File**: `middleware.ts`
- **Purpose**: Combines Clerk authentication with existing i18n routing
- **Key Features**:
  - Route protection using `createRouteMatcher`
  - OAuth callback handling
  - Locale detection and routing preservation
  - Static generation compatibility

#### 2. Clerk Service Layer

**ClerkAuthService** (`src/features/auth/services/clerk-auth.service.ts`)
- Provides server-side authentication operations
- Cached user profile retrieval
- Authentication status management
- Error handling and logging

**ClerkAuthStateService** (`src/features/auth/services/clerk-auth-state.service.ts`)
- Single source of truth for server-side auth state
- React cache integration for performance
- Static generation error handling
- Replaces existing `auth-state.service.ts`

**ClerkAuthActions** (`src/features/auth/actions/clerk-auth.actions.ts`)
- Server actions for authentication operations
- User retrieval and session management
- Sign-out functionality
- Logging integration

#### 3. Client Components

**ClerkAuthSessionProvider** (`src/providers/clerk-auth-session-provider.tsx`)
- Client-side authentication state management
- Sentry integration for error tracking
- Loading state management
- User session synchronization

**ClerkAuth Hook** (`src/features/auth/hooks/use-clerk-auth.ts`)
- Primary authentication hook for components
- Profile data integration
- Error handling and retry logic
- Unified user object creation

**Login Form** (`src/features/auth/components/login-form.tsx`)
- Clerk-powered authentication form
- OAuth integration (Google, Microsoft)
- Email/password authentication
- Error handling and validation

#### 4. OAuth Integration

**SSO Callback Page** (`src/app/sso-callback/page.tsx`)
- Handles OAuth redirect callbacks
- Uses Clerk's `AuthenticateWithRedirectCallback`
- Seamless OAuth flow completion

### Interface Definitions

#### Authentication State Interface
```typescript
interface ClerkAuthState {
  user: any | null;
  profile: UserProfile | null | undefined;
  authUser: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  profileError: Error | null;
  retry: () => void;
  clearErrors: () => void;
}
```

#### User Profile Interface
```typescript
interface UserProfile {
  id: string;
  email: string | undefined;
  firstName: string | null;
  lastName: string | null;
  imageUrl: string;
  createdAt: Date;
  updatedAt: Date;
}
```

#### Auth Action State Interface
```typescript
interface AuthActionState {
  success: boolean;
  message: string | null;
  errorFields?: Record<string, string> | null;
  redirectTo?: string;
  user?: any;
}
```

## Data Models

### User Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as Client Component
    participant H as useClerkAuth Hook
    participant P as ClerkAuthSessionProvider
    participant CL as Clerk API
    participant S as Server Actions
    
    U->>C: Initiates Login
    C->>H: Calls authentication hook
    H->>P: Gets session state
    P->>CL: Queries Clerk user data
    CL-->>P: Returns user object
    P-->>H: Provides user state
    H->>S: Fetches profile data
    S-->>H: Returns profile information
    H-->>C: Provides unified auth state
    C-->>U: Updates UI
```

### Authentication Flow

1. **Initial Load**: 
   - Server-side auth state retrieved via `getServerClerkAuthState()`
   - Preloaded user data passed to client provider
   - Static generation compatibility maintained

2. **Client Hydration**:
   - `ClerkAuthSessionProvider` initializes with preloaded data
   - `useUser` hook from Clerk provides real-time updates
   - Error states and loading states managed

3. **Authentication Actions**:
   - Login attempts processed through Clerk's `signIn` API
   - OAuth flows handled via `authenticateWithRedirect`
   - Session management through Clerk's built-in mechanisms

4. **Route Protection**:
   - Middleware intercepts protected route requests
   - `createRouteMatcher` defines protected patterns
   - Automatic redirects for unauthenticated users

## Error Handling

### Error Categories

1. **Authentication Errors**:
   - Invalid credentials
   - OAuth failures
   - Session expiration
   - Network connectivity issues

2. **Static Generation Errors**:
   - Dynamic server usage in static contexts
   - Cookie access during build time
   - Graceful fallbacks implemented

3. **Profile Data Errors**:
   - Profile fetch failures
   - Data synchronization issues
   - Retry mechanisms with exponential backoff

### Error Recovery Strategies

- **Retry Logic**: Exponential backoff for transient failures
- **Fallback States**: Graceful degradation when services unavailable
- **User Feedback**: Clear error messages for user-facing issues
- **Logging Integration**: Comprehensive error tracking via Winston and Sentry

## Testing Strategy

### Testing Approach

Given the requirement to prioritize working implementation over comprehensive testing, the testing strategy focuses on:

1. **Manual Testing**: Verify each component works after implementation
2. **Integration Testing**: Ensure OAuth flows work end-to-end
3. **Route Protection Testing**: Verify middleware correctly protects routes
4. **Internationalization Testing**: Confirm locale handling remains functional

### Test Scenarios

- **Authentication Flow**: Login with email/password and OAuth
- **Route Protection**: Access protected routes with/without authentication
- **Session Management**: Session persistence across page reloads
- **Error Handling**: Invalid credentials and network failures
- **Internationalization**: Locale detection and routing

### Legacy Code Cleanup Verification

- **Deletion Log**: Maintain comprehensive list of removed files
- **Import Verification**: Ensure no orphaned imports remain
- **Functionality Testing**: Verify replacement components work correctly
- **Build Process**: Confirm `npm run build` continues to work smoothly

## Implementation Phases

### Phase 1: Foundation Setup (1-2 hours)
- Update middleware with Clerk integration
- Create SSO callback page
- Establish basic Clerk configuration

### Phase 2: Service Layer (2-3 hours)
- Implement Clerk auth services
- Create auth actions
- Set up auth state management

### Phase 3: Provider Integration (1 hour)
- Update root layout with ClerkProvider
- Create ClerkAuthSessionProvider
- Replace existing auth provider

### Phase 4: Component Migration (3-4 hours)
- Create Clerk auth hook
- Replace login form component
- Update authentication components

### Phase 5: Testing and Cleanup (1-2 hours)
- Manual testing of all flows
- Legacy code removal
- Documentation of deleted files

## Security Considerations

- **Environment Variables**: Clerk keys already configured and secured
- **OAuth Configuration**: Proper redirect URL configuration in Clerk dashboard
- **Session Security**: Leverage Clerk's built-in session security
- **Route Protection**: Comprehensive middleware-based protection
- **Error Handling**: Avoid exposing sensitive information in error messages

## Performance Considerations

- **Caching**: React cache for server-side auth state
- **Static Generation**: Maintain compatibility with Next.js static generation
- **Bundle Size**: Clerk SDK adds minimal overhead compared to Supabase Auth
- **Loading States**: Proper loading indicators during authentication flows
- **Error Recovery**: Efficient retry mechanisms to minimize user impact