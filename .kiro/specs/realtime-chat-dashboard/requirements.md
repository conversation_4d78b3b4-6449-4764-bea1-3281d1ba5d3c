# Requirements Document

## Introduction

This feature implements a comprehensive realtime chat interface for the dashboard that combines the existing realtime infrastructure with a user-centric chat experience. The feature will be located at `src/app/(dashboard)/dashboard/chat` and will integrate both mouse tracking and chat functionality using the established unified realtime patterns. The interface will feature an active users sidebar with selective filtering capabilities, ensuring only one user can be selected at a time for focused conversations. The chat operates as a single global room for all authenticated dashboard users with a fresh session approach (no message history persistence) to create a FOMO (Fear of Missing Out) experience.

## Requirements

### Requirement 1: Realtime Chat Interface

**User Story:** As a dashboard user, I want to access a dedicated chat page with realtime messaging capabilities, so that I can communicate with other users in real-time.

#### Acceptance Criteria

1. WHEN a user navigates to `/dashboard/chat` THEN the system SHALL display a full-featured chat interface
2. WHEN the chat interface loads THEN the system SHALL establish a realtime connection using the unified realtime hook
3. WHEN the connection is established THEN the system SHALL display connection status to the user
4. WHEN messages are sent or received THEN the system SHALL update the interface in real-time without page refresh
5. WHEN the user leaves the chat page THEN the system SHALL properly cleanup realtime connections following official Supabase patterns (user REF mcp server for uptodate docs)

### Requirement 2: Active Users Sidebar

**User Story:** As a chat user, I want to see a list of active users on the left side of the interface, so that I can identify who is currently online and available for conversation.

#### Acceptance Criteria

1. WHEN the chat interface loads THEN the system SHALL display an active users sidebar on the left side
2. WHEN users join or leave the chat room THEN the system SHALL update the active users list in real-time
3. WHEN displaying users THEN the system SHALL show user avatar, name, and online status indicator
4. WHEN a user is online THEN the system SHALL display a green status indicator
5. WHEN a user is offline THEN the system SHALL display a gray status indicator
6. WHEN the users list is empty THEN the system SHALL display an appropriate empty state message

### Requirement 3: User Selection and Message Filtering

**User Story:** As a chat user, I want to click on a user in the sidebar to filter messages to show only that user's messages, so that I can focus on conversations with specific individuals.

#### Acceptance Criteria

1. WHEN a user clicks on another user in the sidebar THEN the system SHALL filter messages to show only that user's messages
2. WHEN a user is selected THEN the system SHALL highlight the selected user in the sidebar with visual feedback
3. WHEN a user clicks on the same selected user THEN the system SHALL deselect the user and show all messages
4. WHEN no user is selected THEN the system SHALL display all messages from all users
5. WHEN a user is selected THEN the system SHALL prevent selection of other users (only one user can be selected at a time)
6. WHEN messages are filtered THEN the system SHALL maintain real-time updates for the filtered view
7. WHEN a user navigates away from the chat page THEN the system SHALL clear any user selection
8. WHEN a user returns to the chat page THEN the system SHALL show all messages with no user pre-selected

### Requirement 4: Mouse Tracking Integration

**User Story:** As a chat user, I want to see other users' mouse cursors moving in real-time, so that I can have a more interactive and engaging chat experience.

#### Acceptance Criteria

1. WHEN the chat interface is active THEN the system SHALL track and display mouse cursor positions of all connected users
2. WHEN a user moves their mouse THEN the system SHALL broadcast the cursor position to other users in real-time
3. WHEN displaying cursors THEN the system SHALL show each user's cursor with their name and a unique color
4. WHEN a user leaves the chat THEN the system SHALL remove their cursor from the display
5. WHEN cursor data is received THEN the system SHALL animate cursor movements smoothly with appropriate transitions

### Requirement 5: Message Display and Interaction

**User Story:** As a chat user, I want to send and receive messages with proper formatting and timestamps, so that I can have meaningful conversations with other users.

#### Acceptance Criteria

1. WHEN messages are displayed THEN the system SHALL show sender name, message content, and timestamp
2. WHEN a user sends a message THEN the system SHALL immediately display it in the chat area
3. WHEN messages are received THEN the system SHALL display them with appropriate visual styling
4. WHEN displaying own messages THEN the system SHALL visually distinguish them from other users' messages
5. WHEN the chat area has new messages THEN the system SHALL auto-scroll to the bottom unless the user is reading older messages
6. WHEN the message input is empty THEN the system SHALL disable the send button

### Requirement 6: Design System Compliance

**User Story:** As a user, I want the chat interface to match the application's design system and theme, so that I have a consistent visual experience.

#### Acceptance Criteria

1. WHEN the chat interface renders THEN the system SHALL use only CSS variables from the design system
2. WHEN the theme changes (light/dark mode) THEN the system SHALL update all colors and styling accordingly
3. WHEN displaying user colors THEN the system SHALL use the established chart color palette from the design system
4. WHEN styling components THEN the system SHALL use consistent spacing, typography, and border radius from the design system
5. WHEN displaying status indicators THEN the system SHALL use themed colors that work in both light and dark modes

### Requirement 7: Performance and Reliability

**User Story:** As a chat user, I want the interface to be responsive and reliable, so that I can have smooth real-time conversations without technical issues.

#### Acceptance Criteria

1. WHEN mouse events occur THEN the system SHALL throttle cursor position updates to 50ms intervals
2. WHEN the component unmounts THEN the system SHALL properly cleanup all event listeners and realtime connections
3. WHEN network issues occur THEN the system SHALL display appropriate connection status and error messages
4. WHEN the chat loads THEN the system SHALL use the single channel pattern to prevent subscription conflicts
5. WHEN handling high-frequency events THEN the system SHALL maintain smooth performance without blocking the UI

### Requirement 8: Session and Room Management

**User Story:** As a chat user, I want to join a single global chat room with fresh conversations each session, so that I experience real-time interactions without being overwhelmed by historical messages.

#### Acceptance Criteria

1. WHEN a user joins the chat THEN the system SHALL connect them to a single global room named "dashboard-chat"
2. WHEN the chat loads THEN the system SHALL start with an empty message history (no persistence of previous messages)
3. WHEN users join the chat THEN the system SHALL only show messages sent during the current active session
4. WHEN all users leave the chat room THEN the system SHALL allow the session to reset naturally
5. WHEN a user refreshes or rejoins THEN the system SHALL start fresh without loading previous conversation history

### Requirement 9: Mobile Responsiveness

**User Story:** As a mobile user, I want the chat interface to adapt to smaller screens while maintaining functionality, so that I can participate in conversations from any device.

#### Acceptance Criteria

1. WHEN the interface is viewed on mobile devices THEN the system SHALL collapse the sidebar to show only user avatars
2. WHEN displaying collapsed sidebar THEN the system SHALL use the same avatar component from the dashboard user menu (DRY principle)
3. WHEN a user taps an avatar on mobile THEN the system SHALL still filter messages for that user
4. WHEN the sidebar is collapsed THEN the system SHALL provide visual indicators for selected users
5. WHEN switching between desktop and mobile views THEN the system SHALL maintain the current user selection state

### Requirement 10: Integration with Existing Infrastructure

**User Story:** As a developer, I want the new chat feature to leverage existing realtime infrastructure, so that it maintains consistency and reliability with other realtime features.

#### Acceptance Criteria

1. WHEN implementing the chat THEN the system SHALL use the existing `useUnifiedRealtime` hook
2. WHEN displaying cursors THEN the system SHALL use the existing `Cursor` component
3. WHEN displaying messages THEN the system SHALL use the existing `ChatMessageItem` component
4. WHEN implementing auto-scroll THEN the system SHALL use the existing `useAutoScroll` hook
5. WHEN following patterns THEN the system SHALL adhere to all constraints documented in the realtime documentation
6. WHEN referencing the UI design THEN the system SHALL incorporate elements from the `ruixen-mono-chat` component in the prompt