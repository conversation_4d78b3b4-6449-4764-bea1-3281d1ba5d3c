# Design Document

## Overview

The Realtime Chat Dashboard feature creates a comprehensive chat interface that leverages the existing realtime infrastructure to provide a seamless, real-time communication experience. The design follows the established patterns from the `src/features/realtime` infrastructure while incorporating the complete UI structure from the `ruixen-mono-chat` component referenced in the project prompt. 

**Key Integration Points from Prompt:**
- **Component Structure**: Adapts the complete `ruixen-mono-chat.tsx` layout with header, sidebar, chat area, and footer
- **User Selection Logic**: Implements the `selectedSender` state pattern for filtering messages by user
- **Message Status**: Incorporates message status indicators (sent, delivered, read) with CheckCheck/Check icons
- **Reaction System**: Includes emoji reactions with count and reacted state
- **Avatar System**: Uses Image components with online status indicators
- **Responsive Design**: Maintains the shadcn/ui structure with proper separation of concerns

The architecture ensures single-channel pattern compliance, design system integration, and mobile responsiveness while following the established feature organization pattern.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Chat Page] --> B[Chat Layout Component]
    B --> C[Active Users Sidebar]
    B --> D[Chat Area]
    B --> E[Message Input]
    
    F[useUnifiedRealtime Hook] --> G[Supabase Channel: dashboard-chat]
    G --> H[Broadcast Events]
    H --> I[chat_message]
    H --> J[cursor_move]
    
    C --> K[User Selection State]
    D --> L[Message Filtering Logic]
    D --> M[Auto-scroll Hook]
    
    N[Design System] --> O[CSS Variables]
    N --> P[Chart Colors]
    N --> Q[Theme Support]
```

### Component Hierarchy

```
src/app/(dashboard)/dashboard/chat/
└── page.tsx                          # Main chat page

src/features/dashboard/chat-realtime/
├── components/
│   ├── chat-layout.tsx               # Main layout wrapper
│   ├── active-users-sidebar.tsx      # Left sidebar with users
│   ├── chat-area.tsx                 # Main chat message area
│   ├── message-input.tsx             # Bottom input area
│   └── user-avatar.tsx               # Reusable user avatar (DRY)
├── hooks/
│   └── use-chat-state.ts             # Chat-specific state management
└── index.ts                          # Feature exports
```

### UI Layout Structure (Inspired by ruixen-mono-chat)

```
┌─────────────────────────────────────────────────────────────┐
│ Header: "Dashboard Chat" + Connection Status + More Options │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────────┐ │
│ │ Sidebar     │ │ Chat Area                               │ │
│ │ - User 1    │ │ ┌─────────────────────────────────────┐ │ │
│ │ - User 2    │ │ │ Message 1                           │ │ │
│ │ - User 3    │ │ │ Message 2                           │ │ │
│ │ (selected)  │ │ │ Message 3                           │ │ │
│ │             │ │ │ ...                                 │ │ │
│ │             │ │ └─────────────────────────────────────┘ │ │
│ └─────────────┘ └─────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Footer: [😊] [Message Input.....................] [Send] │
└─────────────────────────────────────────────────────────────┘
```

### Data Flow

1. **User Connection**: User navigates to `/dashboard/chat` → Page loads → `useUnifiedRealtime` establishes connection to "dashboard-chat" room
2. **Message Flow**: User types message → Input component → Hook sends broadcast → All connected users receive → UI updates
3. **User Selection**: User clicks sidebar user → State updates → Messages filter → Real-time updates continue for filtered view
4. **Mouse Tracking**: Mouse moves → Throttled position broadcast → Other users see cursor → Smooth animation

## Components and Interfaces

### 1. Main Chat Page (`page.tsx`)

```typescript
interface ChatPageProps {}

export default function ChatPage(): JSX.Element {
  // Layout wrapper (auth handled by dashboard layout)
  // Error boundaries
  // Mobile detection
}
```

**Responsibilities**:
- Layout initialization (authentication already handled by dashboard layout)
- Error handling
- Mobile detection

### 2. Chat Layout Component (`chat-layout.tsx`)

```typescript
interface ChatLayoutProps {
  children?: React.ReactNode;
}

interface ChatLayoutState {
  selectedUserId: string | null;
  isMobile: boolean;
  sidebarCollapsed: boolean;
}
```

**Responsibilities**:
- Overall layout management
- User selection state management
- Mobile responsiveness coordination
- Realtime connection management

### 3. Active Users Sidebar (`active-users-sidebar.tsx`)

```typescript
interface ActiveUsersSidebarProps {
  users: ActiveUser[];
  selectedUserId: string | null;
  onUserSelect: (userId: string | null) => void;
  isMobile: boolean;
  collapsed: boolean;
}

interface ActiveUser {
  id: string;
  email: string;
  name?: string;
  avatar: string; // User avatar URL
  isOnline: boolean;
  color: string;
  lastSeen: Date;
}
```

**Responsibilities**:
- Display active users list with avatars (adapted from ruixen-mono-chat)
- Handle user selection with visual feedback
- Show online status indicators (green dot for online, gray for offline)
- Mobile avatar-only view
- User filtering state management
- Smooth hover and selection transitions

### 4. Chat Area Component (`chat-area.tsx`)

```typescript
interface ChatAreaProps {
  messages: ChatMessage[];
  selectedUserId: string | null;
  currentUser: User;
  isConnected: boolean;
  onReactionToggle?: (messageId: string, emoji: string) => void;
}

interface FilteredMessage extends ChatMessage {
  isVisible: boolean;
  isFromSelectedUser: boolean;
}
```

**Responsibilities**:
- Message display and filtering (adapted from ruixen-mono-chat filtering logic)
- Message status indicators (CheckCheck for read, Check for delivered)
- Reaction system with emoji buttons and counts
- Auto-scroll management
- Message grouping logic with avatars and timestamps
- Real-time message updates
- Empty state handling ("No messages to display")

### 5. Message Input Component (`message-input.tsx`)

```typescript
interface MessageInputProps {
  onSendMessage: (content: string) => void;
  isConnected: boolean;
  disabled?: boolean;
}

interface MessageInputState {
  content: string;
  isTyping: boolean;
  canSend: boolean;
}
```

**Responsibilities**:
- Message composition with rounded input (adapted from ruixen-mono-chat)
- Send button with icon (using Lucide Send icon)
- Optional emoji button (using Lucide SmilePlus icon)
- Input validation and character limits
- Keyboard shortcuts (Enter to send)
- Connection status awareness
- Smooth transitions and hover effects

### 6. User Avatar Component (`user-avatar.tsx`)

```typescript
interface UserAvatarProps {
  user: ActiveUser;
  size?: 'sm' | 'md' | 'lg';
  showOnlineStatus?: boolean;
  selected?: boolean;
  onClick?: () => void;
}
```

**Responsibilities**:
- Consistent avatar display (DRY principle)
- Online status indicator
- Selection state visualization
- Click handling
- Size variants for different contexts

## Data Models

### Extended Chat Message Model

```typescript
interface ChatMessage {
  id: string;
  content: string;
  user: {
    email: string;
    name?: string;
    avatar: string; // User avatar URL
  };
  createdAt: string;
  // Extended for filtering
  userId: string;
  roomName: string;
  // Message status from ruixen-mono-chat
  status: "sent" | "delivered" | "read";
  // Reaction system from ruixen-mono-chat
  reactions?: Array<{
    emoji: string;
    count: number;
    reacted: boolean;
  }>;
}
```

### Active User Model

```typescript
interface ActiveUser {
  id: string;
  email: string;
  name?: string;
  isOnline: boolean;
  color: string;
  lastSeen: Date;
  cursor?: CursorPosition;
}
```

### Chat State Model

```typescript
interface ChatState {
  // Connection
  isConnected: boolean;
  connectionError?: string;
  
  // Users
  activeUsers: Record<string, ActiveUser>;
  selectedUserId: string | null;
  
  // Messages
  messages: ChatMessage[];
  filteredMessages: ChatMessage[];
  
  // UI State
  isMobile: boolean;
  sidebarCollapsed: boolean;
  isTyping: boolean;
}
```

## Error Handling

### Connection Error Handling

```typescript
interface ConnectionErrorState {
  type: 'connection' | 'subscription' | 'send' | 'timeout';
  message: string;
  retryable: boolean;
  timestamp: Date;
}
```

**Error Scenarios**:
1. **Initial Connection Failure**: Show retry button with connection status
2. **Subscription Conflict**: Prevent multiple subscriptions, show error message
3. **Message Send Failure**: Show failed message indicator, allow retry
4. **Network Timeout**: Show reconnection status, auto-retry logic

### User Experience Error Handling

```typescript
interface UXErrorHandling {
  // Graceful degradation
  fallbackToStaticMode: boolean;
  
  // User feedback
  showConnectionStatus: boolean;
  showRetryOptions: boolean;
  
  // Data preservation
  preserveUnsentMessages: boolean;
  cacheUserSelection: boolean;
}
```



## Mobile Design Considerations

### Responsive Breakpoints

```scss
// Mobile: < 768px
.sidebar-mobile {
  width: 60px; // Avatar-only width
  padding: 8px;
}

// Desktop: >= 768px
.sidebar-desktop {
  width: 240px; // Full sidebar width
  padding: 16px;
}
```

### Mobile-Specific Features

1. **Collapsed Sidebar**: Shows only avatars with online status
2. **Touch Interactions**: Tap to select users, swipe gestures
3. **Optimized Scrolling**: Touch-friendly scroll areas
4. **Reduced Animations**: Performance optimization for mobile

### Avatar Integration (DRY Principle)

```typescript
// Reuse existing dashboard avatar component
import { UserAvatar as DashboardAvatar } from '@/components/dashboard/user-avatar';

// Extend for chat-specific needs
interface ChatUserAvatarProps extends DashboardAvatarProps {
  isOnline: boolean;
  selected: boolean;
  showCursor?: boolean;
}
```

## Internationalization (i18n) Integration

The chat feature will use the existing i18n infrastructure and create a new translation file following the established pattern:

### Translation File Structure

```json
// src/lib/i18n/messages/en/chat.json
{
  "_ai_translation_instructions": {
    "target_language": "English (US)",
    "context": "Real-time chat interface for dashboard users",
    "tone": "Friendly, professional, and conversational"
  },
  "title": "Dashboard Chat",
  "subtitle": "Connect with other users in real-time",
  "sidebar": {
    "title": "Active Users",
    "online": "Online",
    "offline": "Offline",
    "noUsers": "No other users online"
  },
  "chat": {
    "placeholder": "Type a message...",
    "send": "Send message",
    "noMessages": "No messages yet. Start the conversation!",
    "connecting": "Connecting...",
    "connected": "Connected",
    "disconnected": "Disconnected",
    "reconnecting": "Reconnecting..."
  },
  "status": {
    "online": "Online",
    "offline": "Offline",
    "typing": "typing...",
    "lastSeen": "Last seen {time}"
  }
}
```

### Usage Pattern

```typescript
import { useTranslations } from '@/lib/i18n';

export function ChatComponent() {
  const t = useTranslations();
  
  return (
    <input 
      placeholder={t.t('chat.placeholder', 'Type a message...')}
      aria-label={t.t('chat.send', 'Send message')}
    />
  );
}
```

## Design System Integration

### Color Palette Usage

```typescript
// User colors from design system
const CHAT_USER_COLORS = [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
];

// Status colors
const STATUS_COLORS = {
  online: 'hsl(var(--success))',
  offline: 'hsl(var(--muted-foreground))',
  selected: 'hsl(var(--primary))',
  hover: 'hsl(var(--accent))',
};
```

### Component Styling Patterns

Based on the `ruixen-mono-chat` component in the prompt, we'll adapt the UI structure while using design system colors:

```typescript
// Adapted from ruixen-mono-chat with design system colors
const CHAT_STYLES = {
  // Main container
  container: 'w-full max-w-5xl mx-auto p-6 bg-card rounded-3xl shadow-lg flex flex-col h-[550px] border border-border',
  
  // Header section
  header: 'flex justify-between items-center border-b border-border pb-3 mb-6',
  headerTitle: 'text-2xl font-semibold text-foreground',
  headerSubtitle: 'italic text-sm text-muted-foreground',
  
  // Sidebar (participants list)
  sidebar: 'w-56 bg-muted border-r border-border p-4 overflow-y-auto',
  userButton: 'flex items-center gap-3 w-full p-3 mb-3 rounded-lg transition-colors',
  userButtonSelected: 'bg-primary text-primary-foreground',
  userButtonHover: 'hover:bg-accent text-accent-foreground',
  
  // Avatar with online indicator
  avatar: 'rounded-full ring-1 ring-border',
  onlineIndicator: 'absolute bottom-0 right-0 w-3 h-3 rounded-full ring-2 ring-background bg-green-500',
  offlineIndicator: 'absolute bottom-0 right-0 w-3 h-3 rounded-full ring-2 ring-background bg-muted-foreground',
  
  // Chat area
  chatArea: 'flex-1 p-6 overflow-y-auto bg-background',
  messageContainer: 'mb-6 last:mb-0 group border-b border-border pb-4',
  messageHeader: 'flex items-center gap-4 mb-2',
  messageContent: 'text-foreground text-lg mb-1',
  
  // Input footer
  footer: 'mt-6 flex items-center gap-4 border-t border-border pt-4',
  input: 'flex-1 px-5 py-3 rounded-full border border-border bg-background text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring transition',
  sendButton: 'p-3 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition',
  emojiButton: 'p-3 rounded-full bg-muted hover:bg-accent transition',
};
```

### Theme Support

```typescript
// Dark/Light mode support
interface ThemeAwareStyles {
  light: {
    sidebar: 'bg-white border-gray-200';
    message: 'bg-gray-100 text-gray-900';
  };
  dark: {
    sidebar: 'bg-gray-900 border-gray-800';
    message: 'bg-gray-800 text-gray-100';
  };
}
```



## Security Considerations

### User Authentication

Authentication is handled by the dashboard layout (`src/app/(dashboard)/layout.tsx`) using the centralized `getServerAuthWithProfilePrefetch` utility. The chat page inherits authenticated user context through the HydrationBoundary.

```typescript
interface SecurityMeasures {
  // Rate limiting (auth handled by dashboard layout)
  messageRateLimit: number; // messages per minute
  cursorRateLimit: number; // updates per second
}
```

### Data Validation

```typescript
// Input sanitization
interface MessageValidation {
  maxLength: 1000;
  allowedCharacters: RegExp;
  preventXSS: boolean;
  filterProfanity: boolean;
}

// User data validation
interface UserValidation {
  emailFormat: boolean;
  nameLength: { min: 1; max: 50 };
  preventImpersonation: boolean;
}
```

## Accessibility Features

### Keyboard Navigation

```typescript
interface KeyboardSupport {
  // Message input
  enterToSend: boolean;
  shiftEnterNewLine: boolean;
  
  // User selection
  arrowKeyNavigation: boolean;
  spaceToSelect: boolean;
  
  // Chat navigation
  pageUpDown: boolean;
  homeEndKeys: boolean;
}
```

### Screen Reader Support

```typescript
interface A11yFeatures {
  // ARIA labels
  messageAnnouncements: boolean;
  userStatusAnnouncements: boolean;
  connectionStatusAnnouncements: boolean;
  
  // Focus management
  focusTrapping: boolean;
  skipLinks: boolean;
  
  // High contrast support
  respectsPreferredContrast: boolean;
  customColorSchemes: boolean;
}
```

---

**Design Philosophy**: Leverage existing infrastructure while creating an intuitive, performant, and accessible chat experience that follows established patterns and maintains consistency with the overall application design.

**Key Design Decisions**:
1. **Single Channel Pattern**: Prevents subscription conflicts by using one channel for all features
2. **Component Reusability**: Leverages existing realtime components and follows DRY principles
3. **Mobile-First Responsive**: Ensures excellent experience across all device sizes
4. **Design System Compliance**: Uses CSS variables and established color patterns
5. **Performance Optimization**: Implements throttling, memoization, and efficient state management