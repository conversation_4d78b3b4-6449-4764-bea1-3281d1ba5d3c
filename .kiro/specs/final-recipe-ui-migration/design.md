# Design Document

## Overview

This migration involves applying working UI solutions from a functional mockup to fix broken production components in the final recipe display system. The architecture follows a component-based approach where the mockup at `src/app/(dashboard)/dashboard/create-recipe/final-step-test/` serves as the reference implementation, and the production components at `src/features/create-recipe/components/` need to be updated to match the mockup's functionality.

The original task is in src\app\(dashboard)\dashboard\create-recipe\final-step-test\MIGRATION_TO_PRODUCTION.md

The migration focuses on four specific UI issues: flip card animations, carrier oil visibility, timeline active states, and missing translations. All fixes must preserve existing backend functionality while using only theme variables and translation keys.

## Architecture

### Component Hierarchy

```
FinalRecipesDisplay (Main Container)
├── Tab Navigation System
├── Overview Tab
│   └── ProtocolSummaryCard (x3) - Flip card components
└── Recipes Tab
    ├── Timeline Navigation
    └── RecipeProtocolCard - Detailed recipe view
```

### State Management Architecture

The migration implements a centralized flip state management pattern:

- **Parent State Control**: `FinalRecipesDisplay` manages flip state for all cards
- **Single Active Card**: Only one card can be flipped at a time
- **Controlled Components**: `ProtocolSummaryCard` receives flip state as props

### Data Flow

```
Store State → FinalRecipesDisplay → ProtocolSummaryCard
                                 → RecipeProtocolCard
```

## Components and Interfaces

### FinalRecipesDisplay Component

**Current Issues:**
- Multiple cards can flip simultaneously
- Timeline active states not working properly

**Design Solution:**
```typescript
interface FlipState {
  flippedCard: string | null;
  setFlippedCard: (cardId: string | null) => void;
}

// Parent manages all flip states
const [flippedCard, setFlippedCard] = useState<string | null>(null);

// Controlled flip behavior
const handleCardFlip = (cardId: string) => {
  setFlippedCard(flippedCard === cardId ? null : cardId);
};
```

**Timeline Enhancement:**
```typescript
// Active state management with proper CSS classes
className={`timeline-item ${
  activeProtocol === slot.key ? 'active active-box' : ''
}`}
```

### ProtocolSummaryCard Component

**Interface Update:**
```typescript
interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
  isFlipped: boolean;    // New: Controlled flip state
  onFlip: () => void;    // New: Flip handler
}
```

**Design Changes:**
- Remove internal `useState` for flip state
- Use props-based flip control
- Update CSS class to use `is-flipped` instead of `flipped`

### RecipeProtocolCard Component

**Carrier Oil Display Fix:**
```typescript
// Alternative carrier oil section with proper visibility
<div className="bg-secondary/10 p-3 rounded-lg border border-secondary/20">
  <h4 className="font-bold text-foreground">
    {t('create-recipe:steps.final-recipes.recipeCard.alternative')}
  </h4>
  <div className="name font-bold text-foreground">
    {recipe.ingredients.carrier_oil.alternative?.name_localized}
  </div>
  <div className="properties text-xs text-muted-foreground">
    {recipe.ingredients.carrier_oil.alternative?.properties_localized}
  </div>
</div>
```

## Data Models

### Translation Keys Structure

```json
{
  "create-recipe": {
    "steps": {
      "final-recipes": {
        "recipeCard": {
          "notAvailable": "Receita não disponível",
          "drops": "gotas",
          "alternative": "Alternativa",
          "totalDrops": "Total de Gotas",
          "dilution": "Diluição",
          "size": "Tamanho",
          "dispenser": "Tampa",
          "essentialOils": "Óleos Essenciais",
          "carrierOil": "Óleo Carreador",
          "recommended": "Recomendado"
        },
        "overview": {
          "protocolSummary": {
            "synergyFor": "Sinergia para",
            "viewDetails": "Ver Detalhes",
            "clickToView": "Clique no botão para ver os detalhes",
            "close": "Fechar",
            "objective": "Objetivo",
            "quickPreparation": "Preparo rápido",
            "howToUse": "Como Usar"
          }
        }
      }
    }
  }
}
```

### CSS Theme Variables Usage

All styling uses existing theme variables from `src/styles/globals.css`:

- **Colors**: `hsl(var(--primary))`, `hsl(var(--foreground))`, `hsl(var(--muted-foreground))`
- **Flip Animation**: `.flip-card`, `.is-flipped`, `.backface-hidden`
- **Timeline**: `.timeline-item.active .dot`, `.active-box`

## Error Handling

### Graceful Degradation

- **Missing Recipe Data**: Show loading skeleton with proper messaging
- **Translation Fallbacks**: Use translation keys with fallback text
- **Optional Chaining**: Safe access to nested properties (`alternative?.name_localized`)

### Type Safety

```typescript
// Safe property access for carrier oil alternatives
interface CarrierOil {
  name_localized: string;
  properties_localized: string;
  alternative?: {
    name_localized: string;
    properties_localized: string;
  };
}
```

## Testing Strategy

### Visual Regression Testing

1. **Side-by-Side Comparison**: Mockup vs Production
2. **Flip Card Behavior**: Only one card flips at a time
3. **Timeline States**: Active dots show proper styling
4. **Carrier Oil Display**: Both recommended and alternative visible

### Functional Testing

1. **Flip Animation**: Smooth transitions with proper CSS classes
2. **Timeline Navigation**: Active state updates immediately
3. **Translation Display**: All text shows properly without keys
4. **Responsive Behavior**: Works across different screen sizes

### Build Validation

- **Static Generation**: `npm run build` succeeds at 100%
- **No Console Errors**: Clean browser console
- **Type Safety**: No TypeScript errors
- **Import Resolution**: All file paths correct

### Integration Testing

1. **Backend Compatibility**: All existing functionality preserved
2. **Store Integration**: Proper data flow from recipe store
3. **Theme Consistency**: Only theme variables used
4. **Translation System**: Proper i18n integration

## Implementation Constraints

### Strict Requirements

- **No Hardcoded Colors**: Use only `hsl(var(--theme-variable))` format
- **No Hardcoded Text**: All user-facing text via translation keys
- **Preserve Backend**: No changes to data fetching or business logic
- **Build Compatibility**: Maintain 100% static page generation
- **DRY/YAGNI/KISS**: Follow project principles strictly

### File Mapping

| Mockup Reference | Production Target |
|------------------|-------------------|
| `final-step-test/final-recipes-display.tsx` | `components/final-recipes-display.tsx` |
| `final-step-test/protocol-summary-card.tsx` | `components/protocol-summary-card.tsx` |
| `final-step-test/recipe-protocol-card.tsx` | `components/recipe-protocol-card.tsx` |
| `final-step-test/safety-warnings.tsx` | `components/safety-warnings.tsx` |

### Success Criteria

The migration is complete when:
- Flip cards work exactly like mockup (one at a time)
- Both carrier oils display properly
- Timeline shows active states correctly
- All text displays without translation keys
- Build process remains stable
- Visual match with mockup achieved