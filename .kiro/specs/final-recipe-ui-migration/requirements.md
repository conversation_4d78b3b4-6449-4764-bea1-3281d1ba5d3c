# Requirements Document

## Introduction

This feature involves migrating working UI solutions from a functional mockup to fix broken production components in the final recipe display system. The mockup at `src/app/(dashboard)/dashboard/create-recipe/final-step-test/` contains fully functional implementations that match the target design, while the production components at `src/features/create-recipe/components/` have several UI issues that need to be resolved by applying the working solutions from the mockup. The original task is in src\app\(dashboard)\dashboard\create-recipe\final-step-test\MIGRATION_TO_PRODUCTION.md

## Requirements

### Requirement 1

**User Story:** As a user viewing final recipes, I want flip card animations to work properly, so that only one card can be flipped at a time with smooth animations.

#### Acceptance Criteria

1. WHEN a user clicks "Ver Detalhes" on a protocol summary card THEN the system SHALL flip only that specific card
2. WHEN a card is already flipped AND the user clicks "Ver Detalhes" on another card THEN the system SHALL close the first card and flip the new one
3. WHEN a user clicks on a flipped card THEN the system SHALL close that card smoothly
4. WHEN flip animations occur THEN the system SHALL use the `is-flipped` CSS class for proper animation

### Requirement 2

**User Story:** As a user viewing recipe details, I want to see both recommended and alternative carrier oils clearly, so that I can make informed decisions about oil selection.

#### Acceptance Criteria

1. WHEN viewing a recipe protocol card THEN the system SHALL display both "Recomendado" and "Alternativa" carrier oil sections
2. WHEN displaying carrier oil information THEN the system SHALL use `text-foreground` color for oil names to ensure visibility
3. WHEN alternative carrier oil data exists THEN the system SHALL render it with proper optional chaining for safety
4. WHEN carrier oil properties are displayed THEN the system SHALL use `text-muted-foreground` for property descriptions

### Requirement 3

**User Story:** As a user navigating between different protocol timelines, I want to see clear visual indication of the active protocol, so that I know which timeline I'm currently viewing.

#### Acceptance Criteria

1. WHEN a user selects a protocol timeline THEN the system SHALL show the corresponding dot as filled/active
2. WHEN displaying timeline items THEN the system SHALL apply both `active` and `active-box` CSS classes to the selected item
3. WHEN timeline dots are rendered THEN the system SHALL use primary theme colors for active states
4. WHEN switching between protocols THEN the system SHALL update the active state immediately

### Requirement 4

**User Story:** As a user interacting with the interface, I want all text to display properly without missing translation keys, so that I can understand all interface elements.

#### Acceptance Criteria

1. WHEN the interface renders THEN the system SHALL display all text using proper translation keys
2. WHEN translation keys are missing THEN the system SHALL have fallback translations added to `src/lib/i18n/messages/pt/create-recipe.json`
3. WHEN displaying recipe card elements THEN the system SHALL use localized text for "drops", "alternative", "recommended", etc.
4. WHEN showing protocol summary information THEN the system SHALL use translated text for "objective", "how to use", etc.

### Requirement 5

**User Story:** As a developer maintaining the system, I want the migration to preserve all existing functionality and constraints, so that the system remains stable and follows project standards.

#### Acceptance Criteria

1. WHEN applying UI fixes THEN the system SHALL maintain all existing backend functionality
2. WHEN updating components THEN the system SHALL use only theme variables from `src/styles/globals.css`
3. WHEN adding text THEN the system SHALL use only translation keys, never hardcoded strings
4. WHEN building the project THEN the system SHALL maintain 100% static page generation success
5. WHEN implementing changes THEN the system SHALL follow DRY, YAGNI, and KISS principles
6. WHEN migration is complete THEN the system SHALL match the mockup's visual appearance and behavior exactly