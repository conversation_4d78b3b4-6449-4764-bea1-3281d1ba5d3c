# Implementation Plan

- [x] 1. Fix flip card animation system


  - Implement centralized flip state management in FinalRecipesDisplay component
  - Update ProtocolSummaryCard interface to accept controlled flip props
  - Remove internal flip state from ProtocolSummaryCard component
  - Update CSS classes to use 'is-flipped' instead of 'flipped'
  - _Requirements: 1.1, 1.2, 1.3, 1.4_



- [ ] 2. Fix carrier oil visibility in recipe cards
  - Update text colors from 'text-secondary' to 'text-foreground' for carrier oil names
  - Ensure alternative carrier oil section is always visible when data exists
  - Add optional chaining for safe property access to alternative carrier oil data


  - Update carrier oil properties to use 'text-muted-foreground' for descriptions
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [ ] 3. Fix timeline active state visualization
  - Update timeline item className to include both 'active' and 'active-box' classes


  - Ensure timeline dots use primary theme colors for active states
  - Verify CSS classes properly apply to selected timeline items
  - Test immediate active state updates when switching protocols
  - _Requirements: 3.1, 3.2, 3.3, 3.4_




- [ ] 4. Add missing translation keys
  - Add recipe card translation keys to Portuguese translation file
  - Add protocol summary translation keys for overview section
  - Update all hardcoded text in components to use translation keys
  - Verify all user-facing text displays properly without showing raw keys
  - _Requirements: 4.1, 4.2, 4.3, 4.4_

- [ ] 5. Validate migration completeness
  - Test flip card behavior matches mockup (one card at a time)
  - Verify both recommended and alternative carrier oils display correctly
  - Confirm timeline active states work properly with visual feedback
  - Ensure all text displays using translations without missing keys
  - Run build process to confirm 100% static generation success
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 5.6_