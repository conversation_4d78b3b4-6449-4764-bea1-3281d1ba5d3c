# Design Document

## Overview

This design outlines the modernization of user interface components by replacing custom user menu implementations with <PERSON>'s standard `<UserButton />` component. The solution will eliminate code duplication, reduce maintenance overhead, and leverage <PERSON>'s built-in functionality while preserving the existing user experience and design consistency.

## Architecture

### Component Replacement Strategy

The modernization follows a direct replacement approach:

1. **Homepage Header (`hero-header.tsx`)**
   - Replace custom user display logic with `<UserButton />`
   - Remove "Hi, [Name]" text and separate "Sign Out" button
   - Preserve "Dashboard" button as separate navigation
   - Use `<ClerkLoading>` and `<ClerkLoaded>` for loading states

2. **Dashboard User Menu (`dashboard-user-menu.tsx`)**
   - Replace entire custom dropdown implementation with `<UserButton />`
   - Integrate with existing sidebar layout
   - Use Clerk's built-in profile management
   - Preserve custom language selection functionality

### Loading State Management

```typescript
// Replace custom loading logic with Clerk's components
<ClerkLoading>
  {/* Custom loading UI if needed */}
</ClerkLoading>
<ClerkLoaded>
  <UserButton />
</ClerkLoaded>
```

## Components and Interfaces

### Homepage Header Component Structure

```typescript
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';

// Before: Custom implementation
const HeroHeader = () => {
  // Custom user state management
  // Custom avatar/name display
  // Custom sign out logic
}

// After: Clerk UserButton integration
const HeroHeader = () => {
  const userButtonAppearance = {
    ...getClerkAppearance(),
    elements: {
      ...getClerkAppearance().elements,
      userButtonAvatarBox: {
        width: '2rem',
        height: '2rem',
        borderRadius: 'var(--radius)'
      },
      userButtonPopoverCard: {
        backgroundColor: 'hsl(var(--popover))',
        borderColor: 'hsl(var(--border))',
        boxShadow: 'var(--shadow-lg)'
      }
    }
  };

  return (
    <motion.header>
      {/* Navigation items */}
      <div className="auth-section">
        <ClerkLoading>
          <Skeleton className="h-8 w-8 rounded-full" />
        </ClerkLoading>
        <ClerkLoaded>
          <SignedIn>
            <UserButton 
              appearance={userButtonAppearance}
              afterSignOutUrl="/"
            />
            <Button variant="secondary" asChild size="sm">
              <Link href="/dashboard">Dashboard</Link>
            </Button>
          </SignedIn>
          <SignedOut>
            {/* Sign in/up buttons */}
          </SignedOut>
        </ClerkLoaded>
      </div>
    </motion.header>
  )
}
```

### Dashboard User Menu Component Structure

```typescript
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';

// Before: Custom dropdown with SidebarMenu
export function DashboardUserMenu() {
  // Custom user state
  // Custom dropdown logic
  // Custom profile modal
}

// After: Clerk UserButton in sidebar
export function DashboardUserMenu() {
  const sidebarUserButtonAppearance = {
    ...getClerkAppearance(),
    elements: {
      ...getClerkAppearance().elements,
      userButtonAvatarBox: {
        width: '2rem',
        height: '2rem',
        borderRadius: 'var(--radius)',
        filter: 'grayscale(1)' // Match existing sidebar styling
      },
      userButtonPopoverCard: {
        backgroundColor: 'hsl(var(--sidebar-background))',
        borderColor: 'hsl(var(--sidebar-border))',
        boxShadow: 'var(--shadow-lg)'
      }
    }
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <ClerkLoading>
          <SidebarMenuButton size="lg">
            <Skeleton className="h-8 w-8 rounded-lg" />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <Skeleton className="h-4 w-24 mb-1" />
              <Skeleton className="h-3 w-32" />
            </div>
          </SidebarMenuButton>
        </ClerkLoading>
        <ClerkLoaded>
          <UserButton 
            appearance={sidebarUserButtonAppearance}
            afterSignOutUrl="/"
            userProfileMode="modal"
          />
        </ClerkLoaded>
      </SidebarMenuItem>
    </SidebarMenu>
  )
}
```

## Data Models

### Clerk UserButton Props Configuration

```typescript
import { getClerkAppearance } from '@/features/auth/config/clerk-appearance';

interface UserButtonConfig {
  // Homepage Header Configuration
  homepage: {
    appearance: ReturnType<typeof getClerkAppearance> & {
      elements: {
        userButtonAvatarBox: string;
        userButtonPopoverCard: string;
        userButtonPopoverActions: string;
      };
    };
    afterSignOutUrl: string;
    showName?: boolean;
  };
  
  // Dashboard Configuration  
  dashboard: {
    appearance: ReturnType<typeof getClerkAppearance> & {
      elements: {
        userButtonAvatarBox: string;
        userButtonPopoverCard: string;
        userButtonPopoverActions: string;
      };
    };
    afterSignOutUrl: string;
    userProfileMode: 'modal';
    userProfileProps?: {
      // Custom language selection preservation
    };
  };
}

// Extended appearance configuration for UserButton specific styling
const getUserButtonAppearance = () => {
  const baseAppearance = getClerkAppearance();
  
  return {
    ...baseAppearance,
    elements: {
      ...baseAppearance.elements,
      // UserButton specific styling
      userButtonAvatarBox: {
        width: '2rem',
        height: '2rem',
        borderRadius: 'var(--radius)'
      },
      userButtonPopoverCard: {
        backgroundColor: 'hsl(var(--popover))',
        borderColor: 'hsl(var(--border))',
        boxShadow: 'var(--shadow-lg)'
      },
      userButtonPopoverActions: {
        color: 'hsl(var(--popover-foreground))'
      }
    }
  };
};
```

### Legacy Code Removal Map

```typescript
// Functions to be removed from clerk-user-helpers.ts
interface LegacyFunctionsToRemove {
  getUserDisplayName: (user: ClerkUser) => string;
  getUserImageUrl: (user: ClerkUser) => string | null;
  generateUserInitials: (user: ClerkUser) => string;
  getUserFullName: (user: ClerkUser) => string | null;
  getUserFirstName: (user: ClerkUser) => string | null;
  getUserEmail: (user: ClerkUser) => string | null;
}

// Imports to be removed
interface ImportsToRemove {
  heroHeader: [
    'getUserDisplayName',
    'getUserImageUrl', 
    'generateUserInitials',
    'Avatar',
    'AvatarFallback',
    'AvatarImage'
  ];
  dashboardUserMenu: [
    'generateUserInitials',
    'AlertDialog',
    'AlertDialogContent',
    'AlertDialogHeader',
    'AlertDialogTitle',
    'AlertDialogDescription',
    'AlertDialogFooter',
    'AlertDialogCancel',
    'DropdownMenu',
    'DropdownMenuContent',
    'DropdownMenuGroup',
    'DropdownMenuItem',
    'DropdownMenuLabel',
    'DropdownMenuSeparator',
    'DropdownMenuTrigger'
  ];
}
```

## Error Handling

### Clerk Integration Error Handling

```typescript
// Error boundaries for Clerk components
const ClerkErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  return (
    <ErrorBoundary
      fallback={<div>Authentication unavailable</div>}
      onError={(error) => console.error('Clerk error:', error)}
    >
      {children}
    </ErrorBoundary>
  );
};

// Usage in components
<ClerkErrorBoundary>
  <UserButton />
</ClerkErrorBoundary>
```

### Fallback States

```typescript
// Homepage header fallback
<ClerkLoading>
  <div className="flex items-center space-x-4">
    <Skeleton className="h-8 w-8 rounded-full" />
    <Skeleton className="h-9 w-20 rounded-md" />
  </div>
</ClerkLoading>

// Dashboard sidebar fallback
<ClerkLoading>
  <SidebarMenuButton size="lg">
    <Skeleton className="h-8 w-8 rounded-lg" />
    <div className="grid flex-1 text-left text-sm leading-tight">
      <Skeleton className="h-4 w-24 mb-1" />
      <Skeleton className="h-3 w-32" />
    </div>
  </SidebarMenuButton>
</ClerkLoading>
```

## Testing Strategy

### Component Testing Approach

1. **Unit Tests**
   - Test `<UserButton />` rendering in both contexts
   - Test loading states with `<ClerkLoading>` and `<ClerkLoaded>`
   - Test styling application and appearance configuration

2. **Integration Tests**
   - Test navigation flow from homepage to dashboard
   - Test sign-out functionality and redirects
   - Test profile management modal opening

3. **Visual Regression Tests**
   - Compare before/after screenshots of both components
   - Verify design system consistency
   - Test responsive behavior

### Test Cases

```typescript
describe('Homepage Header with UserButton', () => {
  it('should render UserButton when user is signed in', () => {
    // Test UserButton rendering
  });
  
  it('should preserve Dashboard button separately', () => {
    // Test Dashboard button remains
  });
  
  it('should handle loading states correctly', () => {
    // Test ClerkLoading/ClerkLoaded
  });
});

describe('Dashboard User Menu with UserButton', () => {
  it('should integrate with sidebar layout', () => {
    // Test sidebar integration
  });
  
  it('should open Clerk profile management', () => {
    // Test profile modal
  });
  
  it('should preserve custom language selection', () => {
    // Test custom functionality preservation
  });
});
```

## Implementation Phases

### Phase 1: Homepage Header Modernization
1. Replace custom user display with `<UserButton />`
2. Implement `<ClerkLoading>` and `<ClerkLoaded>` wrappers
3. Configure appearance to match existing design
4. Preserve Dashboard button as separate element
5. Remove custom helper function usage

### Phase 2: Dashboard User Menu Modernization  
1. Replace custom dropdown with `<UserButton />`
2. Integrate with existing sidebar layout
3. Configure for modal profile management
4. Preserve language selection functionality
5. Remove confirmation dialog code

### Phase 3: Legacy Code Cleanup
1. Remove helper functions from `clerk-user-helpers.ts`
2. Remove unused imports from both components
3. Remove custom loading state logic
4. Remove sign-out confirmation dialog components
5. Clean up any remaining legacy code references

### Phase 4: Testing and Validation
1. Verify functionality matches requirements
2. Test design system consistency
3. Validate loading states and error handling
4. Confirm clean code principles adherence