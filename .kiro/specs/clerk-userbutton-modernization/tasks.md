# Implementation Plan

- [x] 1. Update homepage header component to use Clerk UserButton


  - Replace custom user display logic with `<UserButton />` component
  - Implement `<ClerkLoading>` and `<ClerkLoaded>` wrappers for loading states
  - Configure UserButton appearance using existing `getClerkAppearance()` function
  - Preserve Dashboard button as separate navigation element
  - Remove custom helper function imports and usage
  - _Requirements: 1.1, 1.3, 1.4, 2.1, 2.2, 4.1, 4.2_


- [x] 2. Update dashboard user menu component to use Clerk UserButton

  - Replace custom dropdown implementation with `<UserButton />` component
  - Integrate UserButton with existing sidebar layout and styling
  - Configure UserButton for modal profile management mode
  - Implement `<ClerkLoading>` and `<ClerkLoaded>` wrappers for sidebar context
  - Remove custom dropdown menu imports and logic
  - _Requirements: 1.2, 2.3, 2.4, 4.1, 4.2, 4.3_


- [x] 3. Remove sign-out confirmation dialog from dashboard component


  - Delete AlertDialog components and related state management
  - Remove confirmation dialog imports and dependencies
  - Remove custom sign-out handler logic
  - Update component to rely on Clerk's default sign-out behavior
  - _Requirements: 2.5, 3.5_


- [x] 4. Clean up legacy helper functions and imports

  - Remove `getUserDisplayName`, `getUserImageUrl`, and `generateUserInitials` functions from `clerk-user-helpers.ts`
  - Remove unused imports from both homepage header and dashboard user menu components
  - Remove custom Avatar, AvatarFallback, and AvatarImage imports where no longer needed
  - Remove custom loading state logic and skeleton components where replaced by Clerk loading
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 5.3_


- [x] 5. Test and validate the implementation


  - Verify UserButton functionality in both homepage header and dashboard contexts
  - Test loading states with ClerkLoading and ClerkLoaded components
  - Validate design system consistency and appearance configuration
  - Confirm Dashboard button navigation works correctly from homepage
  - Test sign-out functionality and redirect behavior
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5, 4.4, 5.1, 5.2, 5.4_