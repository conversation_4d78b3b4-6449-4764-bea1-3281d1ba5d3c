---
title: "`<UserProfile />` component"
description: Clerk's <UserProfile /> component is used to render a beautiful,
  full-featured account management UI that allows users to manage their profile
  and security settings.
sdk: astro, chrome-extension, expo, nextjs, nuxt, react, react-router, remix,
  tanstack-react-start, vue, js-frontend
sdkScoped: "true"
canonical: /docs/:sdk:/components/user/user-profile
lastUpdated: 2025-07-29T18:14:32.000Z
availableSdks: astro,chrome-extension,expo,nextjs,nuxt,react,react-router,remix,tanstack-react-start,vue,js-frontend
notAvailableSdks: ios,nodejs,expressjs,fastify,go,ruby,js-backend,sdk-development
activeSdk: nextjs
---

![The \<UserProfile /> component renders a full-featured account management UI that allows users to manage their profile and security settings.](/docs/images/ui-components/user-profile.png){{ style: { maxWidth: '100%' } }}

The `<UserProfile />` component is used to render a beautiful, full-featured account management UI that allows users to manage their profile, security, and billing settings.

## Properties

All props are optional.

<Properties>
  * `appearance`
  * <code>[Appearance](/docs/customization/overview) | undefined</code>

  Optional object to style your components. Will only affect [Clerk components](/docs/components/overview) and not [Account Portal](/docs/account-portal/overview) pages.

  ***

  * `routing`
  * `'hash' | 'path'`

  The [routing](/docs/guides/routing) strategy for your pages. Defaults to `'path'` for frameworks that handle routing, such as Next.js and Remix. Defaults to `hash` for all other SDK's, such as React.

  ***

  * `path`
  * `string`

  The path where the component is mounted on when `routing` is set to `path`. It is ignored in hash-based routing. For example: `/user-profile`.

  ***

  * `additionalOAuthScopes`
  * `object`

  Specify additional scopes per OAuth provider that your users would like to provide if not already approved.  For example: `{google: ['foo', 'bar'], github: ['qux']}`.

  ***

  * `customPages`
  * <code><SDKLink href="/docs/references/javascript/types/custom-page" sdks={["js-frontend"]}>CustomPage</SDKLink>\[]</code>

  An array of custom pages to add to the user profile. Only available for the <SDKLink href="/docs/references/javascript/overview" sdks={["js-frontend"]}>JavaScript SDK</SDKLink>. To add custom pages with React-based SDK's, see the [dedicated guide](/docs/customization/user-profile).

  ***

  * `fallback?`
  * `ReactNode`

  An optional element to be rendered while the component is mounting.
</Properties>

## Usage with frameworks

<Tabs items={["Next.js", "React", "Astro", "Expo", "Remix", "Tanstack React Start", "Vue"]}>
  <Tab>
    The `<UserProfile />` component must embedded using the [Next.js optional catch-all route](https://nextjs.org/docs/pages/building-your-application/routing/dynamic-routes#optional-catch-all-routes) in order for the routing to work.

    ```jsx {{ filename: 'app/user-profile/[[...user-profile]]/page.tsx' }}
    import { UserProfile } from '@clerk/nextjs'

    const UserProfilePage = () => <UserProfile />

    export default UserProfilePage
    ```
  </Tab>

  <Tab>
    ```jsx {{ filename: '/user-profile.tsx' }}
    import { UserProfile } from '@clerk/clerk-react'

    const UserProfilePage = () => <UserProfile />

    export default UserProfilePage
    ```
  </Tab>

  <Tab>
    ```astro {{ filename: 'pages/user.astro' }}
    ---
    import { UserProfile } from '@clerk/astro/components'
    ---

    <UserProfile />
    ```
  </Tab>

  <Tab>
    > \[!NOTE]
    > This component can be used in Expo Web projects, but won't work in native environments (iOS or Android). For native apps, use the supported native components instead.

    ```jsx {{ filename: '/app/user.profile.web.tsx' }}
    import { UserProfile } from '@clerk/clerk-expo/web'

    export default function UserProfilePage() {
      return <UserProfile />
    }
    ```
  </Tab>

  <Tab>
    ```tsx {{ filename: 'routes/user/$.tsx' }}
    import { UserProfile } from '@clerk/remix'

    export default function UserProfilePage() {
      return <UserProfile />
    }
    ```
  </Tab>

  <Tab>
    ```tsx {{ filename: 'app/routes/user-profile.tsx' }}
    import { UserProfile } from '@clerk/tanstack-react-start'
    import { createFileRoute } from '@tanstack/react-router'

    export const Route = createFileRoute('/user-profile')({
      component: UserProfilePage,
    })

    function UserProfilePage() {
      return <UserProfile />
    }
    ```
  </Tab>

  <Tab>
    ```vue {{ filename: 'user.vue' }}
    <script setup>
    import { UserProfile } from '@clerk/vue'
    </script>

    <template>
      <UserProfile />
    </template>
    ```
  </Tab>
</Tabs>

## Usage with JavaScript

The following methods available on an instance of the <SDKLink href="/docs/references/javascript/clerk" sdks={["js-frontend"]} code={true}>Clerk</SDKLink> class are used to render and control the `<UserProfile />` component:

* <SDKLink href="/docs/:sdk:/components/user/user-profile#mount-user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>mountUserProfile()</SDKLink>
* <SDKLink href="/docs/:sdk:/components/user/user-profile#unmount-user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>unmountUserProfile()</SDKLink>
* <SDKLink href="/docs/:sdk:/components/user/user-profile#open-user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>openUserProfile()</SDKLink>
* <SDKLink href="/docs/:sdk:/components/user/user-profile#close-user-profile" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>closeUserProfile()</SDKLink>

The following examples assume that you have followed the <SDKLink href="/docs/quickstarts/javascript" sdks={["js-frontend"]}>quickstart</SDKLink> in order to add Clerk to your JavaScript application.

### `mountUserProfile()`

Render the `<UserProfile />` component to an HTML `<div>` element.

```typescript
function mountUserProfile(node: HTMLDivElement, props?: UserProfileProps): void
```

#### `mountUserProfile()` params

<Properties>
  * `node`
  * [`HTMLDivElement`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLDivElement)

  The `<div>` element used to render in the `<UserProfile />` component

  ***

  * `props?`
  * <SDKLink href="/docs/:sdk:/components/user/user-profile#properties" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>UserProfileProps</SDKLink>

  The properties to pass to the `<UserProfile />` component
</Properties>

#### `mountUserProfile()` usage

```js {{ filename: 'main.js', mark: [15] }}
import { Clerk } from '@clerk/clerk-js'

// Initialize Clerk with your Clerk Publishable Key
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

const clerk = new Clerk(clerkPubKey)
await clerk.load()

document.getElementById('app').innerHTML = `
  <div id="user-profile"></div>
`

const userProfileDiv = document.getElementById('user-profile')

clerk.mountUserProfile(userProfileDiv)
```

### `unmountUserProfile()`

Unmount and run cleanup on an existing `<UserProfile />` component instance.

```typescript
function unmountUserProfile(node: HTMLDivElement): void
```

#### `unmountUserProfile()` params

<Properties>
  * `node`
  * [`HTMLDivElement`](https://developer.mozilla.org/en-US/docs/Web/API/HTMLDivElement)

  The container `<div>` element with a rendered `<UserProfile />` component instance.
</Properties>

#### `unmountUserProfile()` usage

```js {{ filename: 'main.js', mark: [19] }}
import { Clerk } from '@clerk/clerk-js'

// Initialize Clerk with your Clerk Publishable Key
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

const clerk = new Clerk(clerkPubKey)
await clerk.load()

document.getElementById('app').innerHTML = `
  <div id="user-profile"></div>
`

const userProfileDiv = document.getElementById('user-profile')

clerk.mountUserProfile(userProfileDiv)

// ...

clerk.unmountUserProfile(userProfileDiv)
```

### `openUserProfile()`

Opens the `<UserProfile />` component as an overlay at the root of your HTML `body` element.

```typescript
function openUserProfile(props?: UserProfileProps): void
```

#### `openUserProfile()` params

<Properties>
  * `props?`
  * <SDKLink href="/docs/:sdk:/components/user/user-profile#properties" sdks={["astro","chrome-extension","expo","nextjs","nuxt","react","react-router","remix","tanstack-react-start","vue","js-frontend"]} code={true}>UserProfileProps</SDKLink>

  The properties to pass to the `<UserProfile />` component
</Properties>

#### `openUserProfile()` usage

```js {{ filename: 'main.js', mark: [15] }}
import { Clerk } from '@clerk/clerk-js'

// Initialize Clerk with your Clerk Publishable Key
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

const clerk = new Clerk(clerkPubKey)
await clerk.load()

document.getElementById('app').innerHTML = `
  <div id="user-profile"></div>
`

const userProfileDiv = document.getElementById('user-profile')

clerk.openUserProfile(userProfileDiv)
```

### `closeUserProfile()`

Closes the user profile overlay.

```typescript
function closeUserProfile(): void
```

#### `closeUserProfile()` usage

```js {{ filename: 'main.js', mark: [15] }}
import { Clerk } from '@clerk/clerk-js'

// Initialize Clerk with your Clerk Publishable Key
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

const clerk = new Clerk(clerkPubKey)
await clerk.load()

document.getElementById('app').innerHTML = `
  <div id="user-profile"></div>
`

const userProfileDiv = document.getElementById('user-profile')

clerk.closeUserProfile(userProfileDiv)
```

## Customization

To learn about how to customize Clerk components, see the [customization documentation](/docs/customization/overview).

In addition, you also can add custom pages and links to the `<UserProfile />` navigation sidenav. For more information, refer to the [Custom Pages documentation](/docs/customization/user-profile).
