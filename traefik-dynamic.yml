# Dynamic Traefik configuration for Coolify
# This file defines middleware to sanitize headers for Supabase

http:
  middlewares:
    # Middleware for your main app (adds headers)
    oauth-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Host: "novo.rotinanatural.com.br"
          X-Forwarded-Proto: "https"
    
    # Middleware for Supabase (removes problematic headers)
    supabase-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Host: ""  # Remove this header
          X-Forwarded-Proto: "https"